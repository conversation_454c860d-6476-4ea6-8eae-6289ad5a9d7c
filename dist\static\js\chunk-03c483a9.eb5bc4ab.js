(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-03c483a9"],{"1f56":function(e,a,t){"use strict";t.r(a);var n=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen",value:e.loading,expression:"loading",modifiers:{fullscreen:!0}}],staticClass:"router-wrap-table",attrs:{"element-loading-text":e.$t("management.systemUpgrade.loadingText"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[t("header",{staticClass:"header"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:24}},[t("el-tag",{attrs:{effect:"dark"}},[e._v(e._s(e.$t("management.systemUpgrade.edition"))+"："+e._s(e.data.edition||e.$t("management.systemUpgrade.noEdition")))])],1)],1),t("div",{staticClass:"second"},[t("div",{staticClass:"label"},[e._v(e._s(e.$t("management.systemUpgrade.bag"))+":")]),t("div",{staticClass:"upload"},[t("el-upload",{directives:[{name:"has",rawName:"v-has",value:"upload",expression:"'upload'"}],ref:"upload",staticClass:"header-button-upload",attrs:{action:"#",headers:e.upload.header,"auto-upload":"","show-file-list":!0,limit:1,accept:".zip","file-list":e.upload.files,"on-exceed":e.handleExceed,"on-change":e.onUploadFileChange,"http-request":e.submitUploadFile,"on-remove":e.handleRemove,"before-upload":e.beforeUploadValidate},on:{click:e.clickUploadTable}},[t("el-input",{staticStyle:{cursor:"pointer"},attrs:{placeholder:e.$t("management.systemUpgrade.bag"),"suffix-icon":"el-icon-folder-opened",disabled:""}})],1)],1),t("div",{staticClass:"up"},[t("el-button",{directives:[{name:"has",rawName:"v-has",value:"upload",expression:"'upload'"}],attrs:{type:"primary"},on:{click:e.clickUp}},[e._v(" "+e._s(e.$t("management.systemUpgrade.upGrade"))+" ")])],1),t("div",{staticClass:"back"},[t("el-button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"},{name:"debounce",rawName:"v-debounce",value:e.clickBack,expression:"clickBack"}],attrs:{type:"primary"}},[e._v(" "+e._s(e.$t("management.systemUpgrade.backOff"))+" ")])],1)])],1),t("main",{staticClass:"table-body"},[t("header",{staticClass:"table-body-header"},[t("h2",{staticClass:"table-body-title"},[e._v(" "+e._s(e.$t("management.systemUpgrade.upManagement"))+" ")])]),t("main",{staticClass:"table-body-main"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.data.loading,expression:"data.loading"}],ref:"Table",attrs:{data:e.data.table,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"current-change":e.TableRowChange}},[t("el-table-column",{attrs:{prop:"upgradeContent",sortable:"",label:e.$t("management.systemUpgrade.upContent"),"show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"previousVersion",sortable:"",label:e.$t("management.systemUpgrade.upOldEdition"),"show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"currentVersion",sortable:"",label:e.$t("management.systemUpgrade.upNewEdition"),"show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"upgradeState",sortable:"",label:e.$t("management.systemUpgrade.upResult"),"show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"createDate",sortable:"",label:e.$t("management.systemUpgrade.upTime"),"show-overflow-tooltip":""}})],1)],1)]),t("footer",{staticClass:"table-footer"},[t("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.pagination.pageNum,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.TableSizeChange,"current-change":e.TableCurrentChange}})],1)])},r=[],i=(t("baa5"),t("a434"),t("b0c0"),t("ac1f"),t("5319"),t("1276"),t("96cf"),t("c964")),o=t("d0af"),l=t("4020");function s(e){return Object(l["a"])({url:"/system/upgrade",method:"post",data:e||{}},"upload","300000")}function d(e){return Object(l["a"])({url:"/system/upgrade/records",method:"get",params:e||{}})}function p(e){return Object(l["a"])({url:"/system/upgrade/version",method:"get",params:e||{}})}function u(e){return Object(l["a"])({url:"/system/rollback",method:"put",params:e||{}})}var c=t("f7b5"),g={name:"UpGrade",components:{},data:function(){return{loading:!1,uploadFile:{},upload:{header:{"Content-Type":"multipart/form-data"},files:[]},data:{loading:!1,table:[],edition:""},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,currentRow:{}}}},mounted:function(){this.getTableData(),this.getNowData()},methods:{getTableData:function(){var e=this,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.data.loading=!0,d(a).then((function(a){e.data.table=a.rows,e.pagination.total=a.total,e.data.loading=!1}))},getNowData:function(){var e=this;p().then((function(a){e.data.edition=a}))},TableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.getTableData()},TableCurrentChange:function(e){this.pagination.pageNum=e,this.getTableData()},TableRowChange:function(e){this.pagination.currentRow=e},handleExceed:function(e,a){var t=this.$t("asset.management.exceed",[e.length,e.length,a.length]);this.$message.warning(t)},onUploadFileChange:function(e){this.upload.files.push(e)},handleRemove:function(e,a){this.upload.files.splice(0,1)},submitUploadFile:function(e){if(e.file&&this.upload.files.length>0){var a=new FormData;a.append("name","upload"),a.append("file",e.file),this.uploadFile=a}},clickUp:function(){this.uploadFile&&this.upload.files.length>0?this.uploadTable(this.uploadFile):Object(c["a"])({i18nCode:"validate.upload.empty",type:"error"})},beforeUploadValidate:function(e){if(this.upload.files.length>0){var a=e.name.substring(e.name.lastIndexOf(".zip")+1),t="zip"===a;if(!t)return Object(c["a"])({i18nCode:"tip.upload.typeError",type:"warning"}),!1}if(this.upload.files.length>0){var n=e.size/1024/1024/4<=100;if(!n){var r=this.$t("management.systemUpgrade.fileSize");return this.$message.error(r),!1}}if(this.upload.files.length>0){var i=/^patch-\d{1,}to\d{1,}$/,l=e.name.replace(".zip","");if(!i.test(l))return Object(c["a"])({i18nCode:"management.systemUpgrade.updatePackageNameError",type:"warning"}),!1;var s=l.split("to"),d=Object(o["a"])(s,2),p=d[0],u=d[1],g=p.replace(/^patch-/,"");if(g===u)return Object(c["a"])({i18nCode:"management.systemUpgrade.updatePackageSameError",type:"warning"}),!1;if(g!==this.data.edition)return Object(c["a"])({i18nCode:"management.systemUpgrade.updatePackageCurrentError",type:"warning"}),!1;if(parseInt(g)>parseInt(u))return Object(c["a"])({i18nCode:"management.systemUpgrade.updatePackageNameError",type:"warning"}),!1}return!0},clickUploadTable:function(){this.upload.files=[],this.$refs.upload.submit()},uploadTable:function(e){var a=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a.loading=!0,t.next=3,s(e).then((function(e){a.loading=!1,1===e?(Object(c["a"])({i18nCode:"tip.upload.successUp",type:"success"}),a.uploadFile={},a.handleRemove(),a.getTableData(),a.getNowData()):2===e?Object(c["a"])({i18nCode:"tip.upload.running",type:"error"}):3===e?Object(c["a"])({i18nCode:"tip.upload.nameErr",type:"error"}):4===e?Object(c["a"])({i18nCode:"tip.upload.baseErr",type:"error"}):5===e?Object(c["a"])({i18nCode:"tip.upload.back.none",type:"error"}):6===e?Object(c["a"])({i18nCode:"tip.upload.contentErr",type:"error"}):-1===e&&Object(c["a"])({i18nCode:"tip.upload.fail",type:"error"})})).catch((function(e){console.error(e)}));case 3:case"end":return t.stop()}}),t)})))()},clickBack:function(){var e=this;this.loading=!0,u().then((function(a){e.loading=!1,1===a?(Object(c["a"])({i18nCode:"tip.upload.back.success",type:"success"}),e.uploadFile={},e.handleRemove(),e.getTableData(),e.getNowData()):2===a?Object(c["a"])({i18nCode:"tip.upload.running",type:"error"}):3===a?Object(c["a"])({i18nCode:"tip.upload.nameErr",type:"error"}):4===a?Object(c["a"])({i18nCode:"tip.upload.baseErr",type:"error"}):5===a?Object(c["a"])({i18nCode:"tip.upload.back.none",type:"error"}):-1===a&&Object(c["a"])({i18nCode:"tip.upload.back.error",type:"error"})})).catch((function(e){console.error(e)}))}}},m=g,h=(t("28c8"),t("2877")),b=Object(h["a"])(m,n,r,!1,null,"e41de61e",null);a["default"]=b.exports},"28c8":function(e,a,t){"use strict";var n=t("e504"),r=t.n(n);r.a},a434:function(e,a,t){"use strict";var n=t("23e7"),r=t("23cb"),i=t("a691"),o=t("50c4"),l=t("7b0b"),s=t("65f0"),d=t("8418"),p=t("1dde"),u=t("ae40"),c=p("splice"),g=u("splice",{ACCESSORS:!0,0:0,1:2}),m=Math.max,h=Math.min,b=9007199254740991,f="Maximum allowed length exceeded";n({target:"Array",proto:!0,forced:!c||!g},{splice:function(e,a){var t,n,p,u,c,g,y=l(this),v=o(y.length),w=r(e,v),C=arguments.length;if(0===C?t=n=0:1===C?(t=0,n=v-w):(t=C-2,n=h(m(i(a),0),v-w)),v+t-n>b)throw TypeError(f);for(p=s(y,n),u=0;u<n;u++)c=w+u,c in y&&d(p,u,y[c]);if(p.length=n,t<n){for(u=w;u<v-n;u++)c=u+n,g=u+t,c in y?y[g]=y[c]:delete y[g];for(u=v;u>v-n+t;u--)delete y[u-1]}else if(t>n)for(u=v-n;u>w;u--)c=u+n-1,g=u+t-1,c in y?y[g]=y[c]:delete y[g];for(u=0;u<t;u++)y[u+w]=arguments[u+2];return y.length=v-n+t,p}})},d0af:function(e,a,t){"use strict";function n(e){if(Array.isArray(e))return e}t.d(a,"a",(function(){return l}));t("a4d3"),t("e01a"),t("d28b"),t("d3b7"),t("3ca3"),t("ddb0");function r(e,a){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var t=[],n=!0,r=!1,i=void 0;try{for(var o,l=e[Symbol.iterator]();!(n=(o=l.next()).done);n=!0)if(t.push(o.value),a&&t.length===a)break}catch(s){r=!0,i=s}finally{try{n||null==l["return"]||l["return"]()}finally{if(r)throw i}}return t}}var i=t("dde1");function o(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function l(e,a){return n(e)||r(e,a)||Object(i["a"])(e,a)||o()}},e504:function(e,a,t){}}]);