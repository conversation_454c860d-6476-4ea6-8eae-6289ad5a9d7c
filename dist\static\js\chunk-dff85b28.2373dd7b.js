(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-dff85b28"],{"078a":function(e,t,o){"use strict";var a=o("2b0e"),r=(o("99af"),o("caad"),o("ac1f"),o("2532"),o("5319"),{bind:function(e,t,o){var a=[e.querySelector(".el-dialog__header"),e.querySelector(".el-dialog")],r=a[0],l=a[1];r.style.cssText+=";cursor:move;",l.style.cssText+=";top:0px;";var i=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();r.onmousedown=function(e){var t=[e.clientX-r.offsetLeft,e.clientY-r.offsetTop,l.offsetWidth,l.offsetHeight,document.body.clientWidth,document.body.clientHeight],a=t[0],n=t[1],s=t[2],c=t[3],d=t[4],u=t[5],m=[l.offsetLeft,d-l.offsetLeft-s,l.offsetTop,u-l.offsetTop-c],p=m[0],f=m[1],g=m[2],b=m[3],h=[i(l,"left"),i(l,"top")],y=h[0],v=h[1];y.includes("%")?(y=+document.body.clientWidth*(+y.replace(/%/g,"")/100),v=+document.body.clientHeight*(+v.replace(/%/g,"")/100)):(y=+y.replace(/px/g,""),v=+v.replace(/px/g,"")),document.onmousemove=function(e){var t=e.clientX-a,r=e.clientY-n;-t>p?t=-p:t>f&&(t=f),-r>g?r=-g:r>b&&(r=b),l.style.cssText+=";left:".concat(t+y,"px;top:").concat(r+v,"px;"),o.child.$emit("dragDialog")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),l=function(e){e.directive("el-dialog-drag",r)};window.Vue&&(window["el-dialog-drag"]=r,a["default"].use(l)),r.elDialogDrag=l;t["a"]=r},"19f5":function(e,t,o){},2084:function(e,t,o){"use strict";var a=o("e1d2"),r=o.n(a);r.a},"21f4":function(e,t,o){"use strict";o.d(t,"b",(function(){return a})),o.d(t,"a",(function(){return r}));o("d3b7"),o("ac1f"),o("25f0"),o("5319");function a(e){return"undefined"===typeof e||null===e||""===e}function r(e,t){var o=e.per_page||e.size,a=e.total-o*(e.page-1),r=Math.floor((t-a)/o)+1;r<0&&(r=0);var l=e.page-r;return l<1&&(l=1),l}},2532:function(e,t,o){"use strict";var a=o("23e7"),r=o("5a34"),l=o("1d80"),i=o("ab13");a({target:"String",proto:!0,forced:!i("includes")},{includes:function(e){return!!~String(l(this)).indexOf(r(e),arguments.length>1?arguments[1]:void 0)}})},"5a34":function(e,t,o){var a=o("44e7");e.exports=function(e){if(a(e))throw TypeError("The method doesn't accept regular expressions");return e}},"6c01":function(e,t,o){},"86bf":function(e,t,o){"use strict";var a=o("19f5"),r=o.n(a);r.a},9119:function(e,t,o){"use strict";var a=o("a1cb"),r=o.n(a);r.a},a056:function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"router-wrap-table"},[o("header",{staticClass:"table-header"},[o("section",{staticClass:"table-header-main"},[o("section",{staticClass:"table-header-search"},[o("section",{directives:[{name:"show",rawName:"v-show",value:!e.show.seniorQueryShow,expression:"!show.seniorQueryShow"}],staticClass:"table-header-search-input"},[o("el-input",{attrs:{placeholder:e.$t("tip.placeholder.query",[e.$t("collector.management.table.fuzzyField")]),clearable:""},on:{change:function(t){return e.pageQuery("e")}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.pageQuery("e")}},model:{value:e.query.inputVal,callback:function(t){e.$set(e.query,"inputVal","string"===typeof t?t.trim():t)},expression:"query.inputVal"}},[o("i",{staticClass:"el-input__icon soc-icon-search",attrs:{slot:"prefix"},on:{click:function(t){return e.inputQuery("e")}},slot:"prefix"})])],1),o("section",{staticClass:"table-header-search-button"},[e.show.seniorQueryShow?e._e():o("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:function(t){return e.pageQuery("e")}}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),o("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.clickQueryButton}},[e._v(" "+e._s(e.$t("button.search.exact"))+" "),o("i",{staticClass:"el-icon--right",class:e.show.seniorQueryShow?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),o("section",{staticClass:"table-header-button"},[o("el-button",{directives:[{name:"has",rawName:"v-has",value:"upload",expression:"'upload'"}],on:{click:e.clickLogImportButton}},[e._v(" "+e._s(e.$t("button.logImport"))+" ")]),o("el-button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],on:{click:e.clickAddButton}},[e._v(" "+e._s(e.$t("button.add"))+" ")]),o("el-dropdown",{attrs:{placement:"bottom"},on:{command:e.handleCommand}},[o("el-button",{attrs:{type:"primary"}},[e._v(" "+e._s(e.$t("button.batchText"))+" "),o("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),o("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[o("el-dropdown-item",{attrs:{command:"run"}},[e._v(" "+e._s(e.$t("button.batch.run"))+" ")]),o("el-dropdown-item",{attrs:{command:"stop"}},[e._v(" "+e._s(e.$t("button.batch.stop"))+" ")]),o("el-dropdown-item",{staticStyle:{color:"#e32a0c"},attrs:{command:"delete"}},[e._v(" "+e._s(e.$t("button.batch.delete"))+" ")])],1)],1)],1)]),o("section",{staticClass:"table-header-extend"},[o("el-collapse-transition",[o("div",{directives:[{name:"show",rawName:"v-show",value:e.show.seniorQueryShow,expression:"show.seniorQueryShow"}],staticClass:"table-header-query"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:5}},[o("el-input",{attrs:{clearable:"",placeholder:e.$t("collector.management.table.collectorName")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.collectorName,callback:function(t){e.$set(e.query.seniorQuery,"collectorName","string"===typeof t?t.trim():t)},expression:"query.seniorQuery.collectorName"}})],1),o("el-col",{attrs:{span:5}},[o("el-input",{attrs:{clearable:"",placeholder:e.$t("collector.management.table.ip")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.IP,callback:function(t){e.$set(e.query.seniorQuery,"IP","string"===typeof t?t.trim():t)},expression:"query.seniorQuery.IP"}})],1),o("el-col",{attrs:{span:5}},[o("el-select",{attrs:{clearable:"",placeholder:e.$t("collector.management.placeholder.innerType")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.protId,callback:function(t){e.$set(e.query.seniorQuery,"protId",t)},expression:"query.seniorQuery.protId"}},e._l(e.option.innerTypeOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),o("el-col",{attrs:{span:5}},[o("el-select",{attrs:{clearable:"",placeholder:e.$t("collector.management.placeholder.run")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.useState,callback:function(t){e.$set(e.query.seniorQuery,"useState",t)},expression:"query.seniorQuery.useState"}},e._l(e.option.useStateOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),o("el-col",{attrs:{span:4,align:"right"}},[o("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:function(t){return e.pageQuery("e")}}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),o("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetSeniorQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")]),o("el-button",{ref:"shrinkButton",on:{click:e.clickUpButton}},[o("i",{staticClass:"soc-icon-scroller-top-all"})])],1)],1)],1)])],1)]),o("main",{staticClass:"table-body"},[o("header",{staticClass:"table-body-header"},[o("h2",{staticClass:"table-body-title"},[e._v(" "+e._s(e.title)+" ")])]),o("main",{staticClass:"table-body-main"},[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.data.loading,expression:"data.loading"}],ref:"CollectorTable",attrs:{data:e.data.table,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"selection-change":e.tableSelectsChange}},[o("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),e._l(e.option.tableOption,(function(t,a){return o("el-table-column",{key:a,attrs:{prop:t,label:e.$t("collector.management.table."+t),"show-overflow-tooltip":""}})})),o("el-table-column",{attrs:{prop:"agentStatus",label:e.$t("collector.management.table.agentStatus")},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1===t.row.agentStatus?e.$t("collector.management.label.agentStatus.on"):e.$t("collector.management.label.agentStatus.off"))+" ")]}}])}),o("el-table-column",{attrs:{prop:"runState",label:e.$t("collector.management.table.runState")},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s("1"===t.row.runState?"正在运行":"暂停")+" ")]}}])}),o("el-table-column",{attrs:{prop:"useState",label:e.$t("collector.management.table.run")},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{attrs:{disabled:0===t.row.agentStatus,"active-value":"1","inactive-value":"0"},on:{change:function(o){return e.toggleStatus(t.row)}},model:{value:t.row.useState,callback:function(o){e.$set(t.row,"useState",o)},expression:"scope.row.useState"}})]}}])}),o("el-table-column",{attrs:{fixed:"right",width:"240"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticClass:"el-button--blue",attrs:{disabled:0===t.row.agentStatus},on:{click:function(o){return e.clickUpdateButton(t.row)}}},[e._v(" "+e._s(e.$t("button.update"))+" ")]),o("el-button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticClass:"el-button--red",on:{click:function(o){return e.clickDeleteButton(t.row)}}},[e._v(" "+e._s(e.$t("button.delete"))+" ")])]}}])})],2)],1)]),o("footer",{staticClass:"table-footer"},[e.pagination.visible?o("el-pagination",{attrs:{small:"",background:"",align:"right",layout:"total, sizes, prev, pager, next, jumper","current-page":e.pagination.pageNum,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,total:e.pagination.total},on:{"size-change":e.tableSizeChange,"current-change":e.tableCurrentChange}}):e._e()],1),o("add-dialog",{attrs:{width:"60%",visible:e.dialog.addDialog.visibility,title:e.dialog.addDialog.title,form:e.dialog.addDialog.form,"device-type-option":e.option.deviceTypeOption,"agent-option":e.option.agentOption,"filter-option":e.option.filterOption,"collection-address-option":e.option.collectionAddressOption,"inner-type-option":e.option.innerTypeOption},on:{"update:visible":function(t){return e.$set(e.dialog.addDialog,"visibility",t)},"on-change":e.changeAccess,"on-submit":e.clickSubmitAdd}}),o("update-dialog",{attrs:{width:"60%",visible:e.dialog.updateDialog.visibility,title:e.dialog.updateDialog.title,form:e.dialog.updateDialog.form,"filter-option":e.option.filterOption,"collection-address-option":e.option.collectionAddressOption,"device-type-option":e.option.deviceTypeOption,"agent-option":e.option.agentOption,"inner-type-option":e.option.innerTypeOption,"prot-name":e.flag.protName},on:{"update:visible":function(t){return e.$set(e.dialog.updateDialog,"visibility",t)},"on-submit":e.clickSubmitUpdate}}),o("log-import-dialog",{ref:"logImportRef",attrs:{visible:e.dialog.logImportDialog.visible,title:e.title,form:e.dialog.logImportDialog.form,"filter-option":e.option.filterOption,"collection-address-option":e.option.collectionAddressOption,"device-type-option":e.option.deviceTypeOption},on:{"update:visible":function(t){return e.$set(e.dialog.logImportDialog,"visible",t)},"on-submit":e.clickLogImportSubmit}})],1)},r=[],l=(o("d81d"),o("d3b7"),o("ac1f"),o("1276"),o("d0af")),i=(o("96cf"),o("c964")),n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("custom-dialog",{ref:"dialogTemplate",attrs:{visible:e.visible,title:e.title,width:e.width,loading:e.dialogLoading},on:{"on-close":e.clickCancelDialog,"on-submit":e.clickSubmitForm}},[o("el-form",{ref:"addForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[[o("basic-comp",{ref:"basicRef",attrs:{form:e.form,"inner-type-option":e.innerTypeOption,"agent-option":e.agentOption},on:{"on-change-accessmode":e.changeAccessMode}})],"101"===e.form.model.protId||"102"===e.form.model.protId?[o("syslog-comp",{ref:"syslogRef",attrs:{form:e.form,"device-type-option":e.options.logSource,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption},on:{"on-add-logsource":e.clickAddLogSource}})]:e._e(),"103"===e.form.model.protId?[o("jdbc-comp",{ref:"jdbcRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption}})]:e._e(),"104"===e.form.model.protId?[o("ssh-comp",{ref:"sshRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption}})]:e._e(),"106"===e.form.model.protId?[o("netflow-comp",{ref:"netflowRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption}})]:e._e(),"105"===e.form.model.protId?[o("wmi-comp",{ref:"wmiRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption}})]:e._e(),"107"===e.form.model.protId||"108"===e.form.model.protId?[o("ftp-comp",{ref:"ftpRef",attrs:{form:e.form,"device-type-option":e.options.logSource,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption},on:{"on-add-logsource":e.clickAddLogSource}})]:e._e(),"109"===e.form.model.protId?[o("netbios-comp",{ref:"netbiosRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption}})]:e._e(),"110"===e.form.model.protId?o("section",[o("kafka-comp",{ref:"kafkaRef",attrs:{form:e.form,"device-type-option":e.options.logSource,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption},on:{"on-add-logsource":e.clickAddLogSource}})],1):e._e()],2),o("add-log-source-dialog",{attrs:{visible:e.dialog.add.visible,model:e.dialog.add.model,options:e.options},on:{"update:visible":function(t){return e.$set(e.dialog.add,"visible",t)},"on-submit":e.addLogSourceSubmit}})],1)},s=[],c=o("d465"),d=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"basicForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"collectorName",label:e.form.info.collectorName.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.collectorName,callback:function(t){e.$set(e.form.model,"collectorName","string"===typeof t?t.trim():t)},expression:"form.model.collectorName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"codeWay",label:e.form.info.codeWay.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.codeWay")},model:{value:e.form.model.codeWay,callback:function(t){e.$set(e.form.model,"codeWay",t)},expression:"form.model.codeWay"}},e._l(e.codeOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"protId",label:e.form.info.protId.label}},[o("el-select",{attrs:{disabled:"update"===e.sourcePage},on:{change:e.changeAccessMode},model:{value:e.form.model.protId,callback:function(t){e.$set(e.form.model,"protId",t)},expression:"form.model.protId"}},e._l(e.innerTypeOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"agentId",label:e.form.info.agentIp.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.agentIp")},model:{value:e.form.model.agentId,callback:function(t){e.$set(e.form.model,"agentId",t)},expression:"form.model.agentId"}},e._l(e.agentOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value,disabled:"0"===e.type}})})),1)],1)],1)],1)],1)},u=[],m={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},innerTypeOption:{required:!0,type:Array},agentOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},data:function(){return{codeOption:[{label:"自动",value:"auto"},{label:"UTF-8",value:"utf8"},{label:"GBK",value:"gbk"}]}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.basicForm.validate((function(t){e=t})),e},changeAccessMode:function(e){this.$emit("on-change-accessmode",e)}}},p=m,f=o("2877"),g=Object(f["a"])(p,d,u,!1,null,null,null),b=g.exports,h=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"syslogForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"ip",label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"propertyKind",label:e.form.info.propertyKind.label}},[o("el-cascader",{ref:"cascader",attrs:{filterable:"",clearable:"",options:e.deviceTypeOption,placeholder:e.$t("collector.management.placeholder.propertyKind"),props:{expandTrigger:"hover"}},model:{value:e.form.model.propertyKind,callback:function(t){e.$set(e.form.model,"propertyKind",t)},expression:"form.model.propertyKind"}}),o("i",{staticClass:"el-icon-s-operation icon-log-source",attrs:{title:e.$t("collector.management.dialog.title.logSourceAdd")},on:{click:e.clickAddLogSource}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{prop:"describe",label:e.form.info.describe.label}},[o("el-input",{attrs:{type:"textarea",maxlength:"500",rows:4},model:{value:e.form.model.describe,callback:function(t){e.$set(e.form.model,"describe",t)},expression:"form.model.describe"}})],1)],1)],1)],1)},y=[],v={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},deviceTypeOption:{required:!0,type:Array},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.syslogForm.validate((function(t){e=t})),e},clickAddLogSource:function(){this.$emit("on-add-logsource")}}},$=v,k=(o("9119"),Object(f["a"])($,h,y,!1,null,"08018166",null)),w=k.exports,O=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"jdbcForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"dbType",label:e.form.info.dbType.label}},[o("el-select",{attrs:{clearable:"",placeholder:e.$t("collector.management.placeholder.dbType")},model:{value:e.form.model.dbType,callback:function(t){e.$set(e.form.model,"dbType",t)},expression:"form.model.dbType"}},e._l(e.dataBaseOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"dbInst",label:e.form.info.dbInst.label}},[o("el-input",{attrs:{maxlength:"20"},model:{value:e.form.model.dbInst,callback:function(t){e.$set(e.form.model,"dbInst","string"===typeof t?t.trim():t)},expression:"form.model.dbInst"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.ip.key,label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.port.key,label:e.form.info.port.label}},[o("el-input",{attrs:{maxlength:"20"},model:{value:e.form.model.port,callback:function(t){e.$set(e.form.model,"port","string"===typeof t?t.trim():t)},expression:"form.model.port"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.userName.key,label:e.form.info.userName.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.userName,callback:function(t){e.$set(e.form.model,"userName","string"===typeof t?t.trim():t)},expression:"form.model.userName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.password.key,label:e.form.info.password.label}},[o("el-input",{attrs:{type:"password",maxlength:"50"},model:{value:e.form.model.password,callback:function(t){e.$set(e.form.model,"password","string"===typeof t?t.trim():t)},expression:"form.model.password"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1)},S=[],x={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},data:function(){return{dataBaseOption:[{label:"mysql",value:"mysql"},{label:"oracle",value:"oracle"}]}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.jdbcForm.validate((function(t){e=t})),e}}},C=x,A=Object(f["a"])(C,O,S,!1,null,null,null),N=A.exports,q=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"sshForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.ip.key,label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.port.key,label:e.form.info.port.label}},[o("el-input",{attrs:{maxlength:"20"},model:{value:e.form.model.port,callback:function(t){e.$set(e.form.model,"port","string"===typeof t?t.trim():t)},expression:"form.model.port"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.userName.key,label:e.form.info.userName.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.userName,callback:function(t){e.$set(e.form.model,"userName","string"===typeof t?t.trim():t)},expression:"form.model.userName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.password.key,label:e.form.info.password.label}},[o("el-input",{attrs:{type:"password",maxlength:"50"},model:{value:e.form.model.password,callback:function(t){e.$set(e.form.model,"password","string"===typeof t?t.trim():t)},expression:"form.model.password"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1)},I=[],_={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.sshForm.validate((function(t){e=t})),e}}},j=_,T=Object(f["a"])(j,q,I,!1,null,null,null),F=T.exports,D=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"netflowForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"ip",label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),o("el-row")],1)},R=[],Q={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.netflowForm.validate((function(t){e=t})),e}}},z=Q,K=Object(f["a"])(z,D,R,!1,null,null,null),L=K.exports,B=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"wmiForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.ip.key,label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.domain.key,label:e.form.info.domain.label}},[o("el-input",{attrs:{maxlength:"255"},model:{value:e.form.model.domain,callback:function(t){e.$set(e.form.model,"domain","string"===typeof t?t.trim():t)},expression:"form.model.domain"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.userName.key,label:e.form.info.userName.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.userName,callback:function(t){e.$set(e.form.model,"userName","string"===typeof t?t.trim():t)},expression:"form.model.userName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.password.key,label:e.form.info.password.label}},[o("el-input",{attrs:{type:"password",maxlength:"50"},model:{value:e.form.model.password,callback:function(t){e.$set(e.form.model,"password","string"===typeof t?t.trim():t)},expression:"form.model.password"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1)},W=[],E={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.wmiForm.validate((function(t){e=t})),e}}},V=E,P=Object(f["a"])(V,B,W,!1,null,null,null),M=P.exports,U=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"ftpForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"propertyKind",label:e.form.info.propertyKind.label}},[o("el-cascader",{ref:"cascader",attrs:{filterable:"",clearable:"",options:e.deviceTypeOption,placeholder:e.$t("collector.management.placeholder.propertyKind"),props:{expandTrigger:"hover"}},model:{value:e.form.model.propertyKind,callback:function(t){e.$set(e.form.model,"propertyKind",t)},expression:"form.model.propertyKind"}}),e._v(" "),o("i",{staticClass:"el-icon-s-operation icon-log-source",attrs:{title:e.$t("collector.management.dialog.title.logSourceAdd")},on:{click:e.clickAddLogSource}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"directory",label:e.form.info.directory.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.directory,callback:function(t){e.$set(e.form.model,"directory","string"===typeof t?t.trim():t)},expression:"form.model.directory"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.ip.key,label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.port.key,label:e.form.info.port.label}},[o("el-input",{attrs:{maxlength:"20"},model:{value:e.form.model.port,callback:function(t){e.$set(e.form.model,"port","string"===typeof t?t.trim():t)},expression:"form.model.port"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.userName.key,label:e.form.info.userName.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.userName,callback:function(t){e.$set(e.form.model,"userName","string"===typeof t?t.trim():t)},expression:"form.model.userName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.password.key,label:e.form.info.password.label}},[o("el-input",{attrs:{type:"password",maxlength:"50"},model:{value:e.form.model.password,callback:function(t){e.$set(e.form.model,"password","string"===typeof t?t.trim():t)},expression:"form.model.password"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1)},Z=[],G={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},deviceTypeOption:{required:!0,type:Array},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.ftpForm.validate((function(t){e=t})),e},clickAddLogSource:function(){this.$emit("on-add-logsource")}}},J=G,H=(o("ebd6"),Object(f["a"])(J,U,Z,!1,null,"2b57dea6",null)),X=H.exports,Y=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"netbiosForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"ip",label:e.form.info.ip.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.ip,callback:function(t){e.$set(e.form.model,"ip",t)},expression:"form.model.ip"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1)},ee=[],te={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.netbiosForm.validate((function(t){e=t})),e}}},oe=te,ae=Object(f["a"])(oe,Y,ee,!1,null,null,null),re=ae.exports,le=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-form",{ref:"kafkaForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"propertyKind",label:e.form.info.propertyKind.label}},[o("el-cascader",{ref:"cascader",attrs:{filterable:"",clearable:"",options:e.deviceTypeOption,placeholder:e.$t("collector.management.placeholder.propertyKind"),props:{expandTrigger:"hover"}},model:{value:e.form.model.propertyKind,callback:function(t){e.$set(e.form.model,"propertyKind",t)},expression:"form.model.propertyKind"}}),e._v(" "),o("i",{staticClass:"el-icon-s-operation icon-log-source",attrs:{title:e.$t("collector.management.dialog.title.logSourceAdd")},on:{click:e.clickAddLogSource}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"topic",label:e.form.info.topic.label}},[o("el-input",{attrs:{maxlength:"100"},model:{value:e.form.model.topic,callback:function(t){e.$set(e.form.model,"topic","string"===typeof t?t.trim():t)},expression:"form.model.topic"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"kafkaAddress",label:e.form.info.kafkaAddress.label}},[o("el-input",{model:{value:e.form.model.kafkaAddress,callback:function(t){e.$set(e.form.model,"kafkaAddress","string"===typeof t?t.trim():t)},expression:"form.model.kafkaAddress"}})],1)],1),o("span",{staticClass:"tip-address"},[e._v("注：输入格式为ip:端口，多个设备之间“,”分隔")])],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.userName.key,label:e.form.info.userName.label}},[o("el-input",{attrs:{maxlength:"50"},model:{value:e.form.model.userName,callback:function(t){e.$set(e.form.model,"userName","string"===typeof t?t.trim():t)},expression:"form.model.userName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:e.form.info.password.key,label:e.form.info.password.label}},[o("el-input",{attrs:{type:"password",maxlength:"50"},model:{value:e.form.model.password,callback:function(t){e.$set(e.form.model,"password","string"===typeof t?t.trim():t)},expression:"form.model.password"}})],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.form.info.strategy.label}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1)],1)},ie=[],ne={props:{form:{required:!0,type:Object},validate:{type:Boolean,default:!0},deviceTypeOption:{required:!0,type:Array},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},sourcePage:{type:String,default:"add"}},computed:{rules:function(){return this.validate?this.form.rules:null}},methods:{validateForm:function(){var e=!1;return this.$refs.kafkaForm.validate((function(t){e=t})),e},clickAddLogSource:function(){this.$emit("on-add-logsource")}}},se=ne,ce=(o("86bf"),Object(f["a"])(se,le,ie,!1,null,"6bbb8ed0",null)),de=ce.exports,ue=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("custom-dialog",{ref:"dialogDom",attrs:{visible:e.visible,title:e.$t("dialog.title.add",[e.title]),width:"30%"},on:{"on-close":e.clickCancel,"on-submit":e.clickSubmit}},[o("el-form",{ref:"formDom",attrs:{model:e.model,rules:e.rules,"label-width":"110px"}},[o("el-form-item",{attrs:{prop:"typeName",label:e.$t("collector.logSource.label.typeName")}},[o("el-input",{attrs:{maxlength:"32"},model:{value:e.model.typeName,callback:function(t){e.$set(e.model,"typeName",t)},expression:"model.typeName"}})],1),o("el-form-item",{attrs:{prop:"categoryId",label:e.$t("collector.logSource.label.categoryName")}},[o("el-select",{attrs:{clearable:"",filterable:"",placeholder:e.$t("collector.logSource.placeholder.categoryName")},model:{value:e.model.categoryId,callback:function(t){e.$set(e.model,"categoryId",t)},expression:"model.categoryId"}},e._l(e.options.category,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),o("el-form-item",{attrs:{prop:"manufact",label:e.$t("collector.logSource.label.manufact")}},[o("el-select",{attrs:{filterable:"","allow-create":"","default-first-option":"",placeholder:e.$t("collector.logSource.placeholder.manufact")},model:{value:e.model.manufact,callback:function(t){e.$set(e.model,"manufact",t)},expression:"model.manufact"}},e._l(e.options.manufact,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1),o("el-form-item",{attrs:{prop:"desc",label:e.$t("collector.logSource.label.desc")}},[o("el-input",{attrs:{type:"textarea",rows:4,maxlength:"1024"},model:{value:e.model.desc,callback:function(t){e.$set(e.model,"desc",t)},expression:"model.desc"}})],1)],1)],1)},me=[],pe=(o("25f0"),o("f7b5")),fe={components:{CustomDialog:c["a"]},props:{visible:{required:!0,type:Boolean},model:{required:!0,type:Object},options:{required:!0,type:Object}},data:function(){return{title:this.$t("collector.logSource.title"),dialogVisible:this.visible,rules:{manufact:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],categoryId:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],typeName:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}]}}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit("update:visible",e)}},methods:{clickCancel:function(){this.$refs.dialogDom.end(),this.dialogVisible=!1},clickSubmit:function(){var e=this;this.$refs.formDom.validate((function(t){t?e.$confirm(e.$t("tip.confirm.submit"),e.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){e.model.manufact=e.model.manufact.toString(),e.$emit("on-submit",e.model),e.clickCancel()})):Object(pe["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1}))})),this.$refs.dialogDom.end()}}},ge=fe,be=Object(f["a"])(ge,ue,me,!1,null,null,null),he=be.exports,ye=(o("99af"),o("4020"));function ve(e){return Object(ye["a"])({url:"/collector/management/collectors",method:"get",params:e||{}})}function $e(e){return Object(ye["a"])({url:"/collector/management/collector/".concat(e),method:"delete"})}function ke(e){return Object(ye["a"])({url:"/collector/management/collector",method:"post",data:e||{}})}function we(e){return Object(ye["a"])({url:"/collector/management/collector",method:"put",data:e||{}})}function Oe(e,t){return Object(ye["a"])({url:"/collector/management/collector/".concat(e,"/").concat(t),method:"put"})}function Se(e){return Object(ye["a"])({url:"/collector/management/combo/source-device-types",method:"get",params:e||{}})}function xe(e){return Object(ye["a"])({url:"/collector/management/combo/filter-strategies",method:"get",params:e||{}})}function Ce(e){return Object(ye["a"])({url:"/collector/management/combo/deviceIps ",method:"get",params:e||{}})}function Ae(e){return Object(ye["a"])({url:"/collector/management/combo/collection-protocols",method:"get",params:e||{}})}function Ne(e){return Object(ye["a"])({url:"/collector/management/collector/".concat(e),method:"get"})}function qe(e){return Object(ye["a"])({url:"/collector/management/combo/agents",method:"get",params:e||{}})}function Ie(e){return Object(ye["a"])({url:"/collector/management/uploadlog",method:"post",data:e||{}},"upload")}function _e(e){return Object(ye["a"])({url:"/collector/management/importLogRecord",method:"get",params:e||{}})}function je(e){return Object(ye["a"])({url:"/collector/management/collector-checkip",method:"post",data:e||{}})}function Te(e){return Object(ye["a"])({url:"/collector/management/addDevType",method:"post",data:e||{}})}function Fe(){return Object(ye["a"])({url:"/collector/management/combo/manufact",method:"get"})}function De(){return Object(ye["a"])({url:"/collector/management/combo/category",method:"get"})}var Re={components:{CustomDialog:c["a"],BasicComp:b,SyslogComp:w,JdbcComp:N,SshComp:F,NetflowComp:L,WmiComp:M,FtpComp:X,NetbiosComp:re,KafkaComp:de,AddLogSourceDialog:he},props:{visible:{required:!0,type:Boolean},title:{required:!0,type:String},width:{type:String,default:"800"},actions:{type:Boolean,default:!0},readonly:{type:Boolean,default:!1},form:{required:!0,type:Object},validate:{type:Boolean,default:!0},deviceTypeOption:{required:!0,type:Array},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},agentOption:{required:!0,type:Array},innerTypeOption:{required:!0,type:Array}},data:function(){return{dialogLoading:!1,dialogVisible:this.visible,options:{codeOption:[{label:"自动",value:"auto"},{label:"UTF-8",value:"utf8"},{label:"GBK",value:"gbk"}],manufact:[],category:[],logSource:[]},leaf:[],dialog:{add:{visible:!1,model:{}}}}},computed:{rules:function(){return this.validate?this.form.rules:null}},watch:{visible:function(e){this.dialogVisible=e,e&&this.getLogSourceCombo()},dialogVisible:function(e){this.$emit("update:visible",e)}},mounted:function(){this.initOptions()},methods:{clickCancelDialog:function(){this.$refs.dialogTemplate.end(),this.dialogVisible=!1},clickSubmitForm:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var o,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o=e.validateForm(),a="tip.confirm.submit",!o){t.next=6;break}return e.dialogLoading=!0,t.next=6,je(e.form.model).then((function(t){e.dialogLoading=!1,e.accessFlag=t,1===e.accessFlag?a="tip.confirm.existAccessmode":2===e.accessFlag?a="tip.confirm.existAsset":4===e.accessFlag&&(Object(pe["a"])({i18nCode:"tip.add.assetLimit",type:"info"}),o=!1)}));case 6:o?e.$confirm(e.$t(a),e.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){e.$emit("on-submit",e.form.model,e.leaf),e.clickCancelDialog()})):Object(pe["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1})),e.$refs.dialogTemplate.end();case 8:case"end":return t.stop()}}),t)})))()},validateForm:function(){var e=this.form.model.protId,t=!1,o=this.$refs.basicRef.validateForm();switch(e){case"103":t=this.$refs.jdbcRef.validateForm();break;case"104":t=this.$refs.sshRef.validateForm();break;case"105":t=this.$refs.wmiRef.validateForm();break;case"106":t=this.$refs.netflowRef.validateForm();break;case"107":case"108":t=this.$refs.ftpRef.validateForm(),this.leaf=this.$refs.ftpRef.$refs.cascader.getCheckedNodes(!0);break;case"109":t=this.$refs.netbiosRef.validateForm();break;case"110":t=this.$refs.kafkaRef.validateForm(),this.leaf=this.$refs.kafkaRef.$refs.cascader.getCheckedNodes(!0);break;default:t=this.$refs.syslogRef.validateForm(),this.leaf=this.$refs.syslogRef.$refs.cascader.getCheckedNodes(!0);break}return o&&t},changeAccessMode:function(e){this.$emit("on-change",{protId:e,collectorName:this.form.model.collectorName,codeWay:this.form.model.codeWay})},clickAddLogSource:function(){this.getManufactCombo(),this.dialog.add.visible=!0,this.dialog.add.model={manufact:"",categoryId:"",typeName:"",desc:""}},addLogSourceSubmit:function(e){var t=this;Te(e).then((function(e){1===e?Object(pe["a"])({i18nCode:"tip.add.success",type:"success"},(function(){t.getLogSourceCombo()})):2===e?Object(pe["a"])({i18nCode:"tip.add.repeat",type:"error"}):Object(pe["a"])({i18nCode:"tip.add.error",type:"error"})}))},initOptions:function(){this.getManufactCombo(),this.getCategoryCombo()},getManufactCombo:function(){var e=this;Fe().then((function(t){e.options.manufact=t}))},getCategoryCombo:function(){var e=this;De().then((function(t){e.options.category=t}))},getLogSourceCombo:function(){var e=this;Se().then((function(t){e.options.logSource=t}))}}},Qe=Re,ze=Object(f["a"])(Qe,n,s,!1,null,null,null),Ke=ze.exports,Le=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("custom-dialog",{ref:"dialogTemplate",attrs:{visible:e.visible,title:e.title,width:e.width,loading:e.dialogLoading},on:{"on-close":e.clickCancelDialog,"on-submit":e.clickSubmitForm}},[o("el-form",{ref:"addForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[[o("basic-comp",{ref:"basicRef",attrs:{form:e.form,"inner-type-option":e.innerTypeOption,"agent-option":e.agentOption,"source-page":"update"},on:{"on-change-accessmode":e.changeAccessMode}})],"101"===e.form.model.protId||"102"===e.form.model.protId?[o("syslog-comp",{ref:"syslogRef",attrs:{form:e.form,"device-type-option":e.options.logSource,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"},on:{"on-add-logsource":e.clickAddLogSource}})]:e._e(),"103"===e.form.model.protId?[o("jdbc-comp",{ref:"jdbcRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"}})]:e._e(),"104"===e.form.model.protId?[o("ssh-comp",{ref:"sshRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"}})]:e._e(),"106"===e.form.model.protId?[o("netflow-comp",{ref:"netflowRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"}})]:e._e(),"105"===e.form.model.protId?[o("wmi-comp",{ref:"wmiRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"}})]:e._e(),"107"===e.form.model.protId||"108"===e.form.model.protId?[o("ftp-comp",{ref:"ftpRef",attrs:{form:e.form,"device-type-option":e.options.logSource,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"},on:{"on-add-logsource":e.clickAddLogSource}})]:e._e(),"109"===e.form.model.protId?[o("netbios-comp",{ref:"netbiosRef",attrs:{form:e.form,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"}})]:e._e(),"110"===e.form.model.protId?o("section",[o("kafka-comp",{ref:"kafkaRef",attrs:{form:e.form,"device-type-option":e.options.logSource,"filter-option":e.filterOption,"collection-address-option":e.collectionAddressOption,"source-page":"update"},on:{"on-add-logsource":e.clickAddLogSource}})],1):e._e()],2),o("add-log-source-dialog",{attrs:{visible:e.dialog.add.visible,model:e.dialog.add.model,options:e.options,"source-page":"update"},on:{"update:visible":function(t){return e.$set(e.dialog.add,"visible",t)},"on-submit":e.addLogSourceSubmit}})],1)},Be=[],We={components:{CustomDialog:c["a"],BasicComp:b,SyslogComp:w,JdbcComp:N,SshComp:F,NetflowComp:L,WmiComp:M,FtpComp:X,NetbiosComp:re,KafkaComp:de,AddLogSourceDialog:he},props:{visible:{required:!0,type:Boolean},title:{required:!0,type:String},width:{type:String,default:"800"},actions:{type:Boolean,default:!0},readonly:{type:Boolean,default:!1},form:{required:!0,type:Object},validate:{type:Boolean,default:!0},deviceTypeOption:{required:!0,type:Array},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},agentOption:{required:!0,type:Array},innerTypeOption:{required:!0,type:Array}},data:function(){return{dialogLoading:!1,dialogVisible:this.visible,options:{codeOption:[{label:"自动",value:"auto"},{label:"UTF-8",value:"utf8"},{label:"GBK",value:"gbk"}],manufact:[],category:[],logSource:[]},leaf:[],dialog:{add:{visible:!1,model:{}}}}},computed:{rules:function(){return this.validate?this.form.rules:null}},watch:{visible:function(e){this.dialogVisible=e,e&&this.getLogSourceCombo()},dialogVisible:function(e){this.$emit("update:visible",e)}},mounted:function(){this.initOptions()},methods:{clickCancelDialog:function(){this.$refs.dialogTemplate.end(),this.dialogVisible=!1},clickSubmitForm:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var o,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o=e.validateForm(),a="tip.confirm.submit",!o){t.next=6;break}return e.dialogLoading=!0,t.next=6,je(e.form.model).then((function(t){e.dialogLoading=!1,e.accessFlag=t,1===e.accessFlag?a="tip.confirm.existAccessmode":2===e.accessFlag?a="tip.confirm.existAsset":4===e.accessFlag&&(Object(pe["a"])({i18nCode:"tip.add.assetLimit",type:"info"}),o=!1)}));case 6:o?e.$confirm(e.$t(a),e.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){e.$emit("on-submit",e.form.model,e.leaf),e.clickCancelDialog()})):Object(pe["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1})),e.$refs.dialogTemplate.end();case 8:case"end":return t.stop()}}),t)})))()},validateForm:function(){var e=this.form.model.protId,t=!1,o=this.$refs.basicRef.validateForm();switch(e){case"103":t=this.$refs.jdbcRef.validateForm();break;case"104":t=this.$refs.sshRef.validateForm();break;case"105":t=this.$refs.wmiRef.validateForm();break;case"106":t=this.$refs.netflowRef.validateForm();break;case"107":case"108":t=this.$refs.ftpRef.validateForm(),this.leaf=this.$refs.ftpRef.$refs.cascader.getCheckedNodes(!0);break;case"109":t=this.$refs.netbiosRef.validateForm();break;case"110":t=this.$refs.kafkaRef.validateForm(),this.leaf=this.$refs.kafkaRef.$refs.cascader.getCheckedNodes(!0);break;default:t=this.$refs.syslogRef.validateForm(),this.leaf=this.$refs.syslogRef.$refs.cascader.getCheckedNodes(!0);break}return o&&t},changeAccessMode:function(e){this.$emit("on-change",{protId:e,collectorName:this.form.model.collectorName,codeWay:this.form.model.codeWay})},clickAddLogSource:function(){this.getManufactCombo(),this.dialog.add.visible=!0,this.dialog.add.model={manufact:"",categoryId:"",typeName:"",desc:""}},addLogSourceSubmit:function(e){var t=this;Te(e).then((function(e){1===e?Object(pe["a"])({i18nCode:"tip.add.success",type:"success"},(function(){t.getLogSourceCombo()})):2===e?Object(pe["a"])({i18nCode:"tip.add.repeat",type:"error"}):Object(pe["a"])({i18nCode:"tip.add.error",type:"error"})}))},initOptions:function(){this.getManufactCombo(),this.getCategoryCombo()},getManufactCombo:function(){var e=this;Fe().then((function(t){e.options.manufact=t}))},getCategoryCombo:function(){var e=this;De().then((function(t){e.options.category=t}))},getLogSourceCombo:function(){var e=this;Se().then((function(t){e.options.logSource=t}))}}},Ee=We,Ve=Object(f["a"])(Ee,Le,Be,!1,null,null,null),Pe=Ve.exports,Me=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("custom-dialog",{ref:"dialogTemplate",attrs:{visible:e.visible,title:e.$t("dialog.title.logImport",[e.title]),width:"60%"},on:{"on-close":e.clickCancel,"on-submit":e.clickSubmit}},[o("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[o("el-tab-pane",{staticStyle:{height:"272px"},attrs:{label:e.$t("collector.management.dialog.columns.importConfig"),name:"first"}},[o("el-form",{ref:"logForm",attrs:{model:e.form.model,rules:e.rules,"label-width":"120px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"IP",label:e.$t("collector.management.dialog.columns.ip")}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:"采集地址"},model:{value:e.form.model.IP,callback:function(t){e.$set(e.form.model,"IP",t)},expression:"form.model.IP"}},e._l(e.collectionAddressOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.label}})})),1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"codeWay",label:e.$t("collector.management.dialog.columns.codeWay")}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.codeWay")},model:{value:e.form.model.codeWay,callback:function(t){e.$set(e.form.model,"codeWay",t)},expression:"form.model.codeWay"}},e._l(e.options.codeOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"propertyKind",label:e.$t("collector.management.dialog.columns.propertyKind")}},[o("el-cascader",{ref:"cascader",attrs:{filterable:"",clearable:"",options:e.deviceTypeOption,placeholder:e.$t("collector.management.placeholder.propertyKind"),props:{expandTrigger:"hover"}},model:{value:e.form.model.propertyKind,callback:function(t){e.$set(e.form.model,"propertyKind",t)},expression:"form.model.propertyKind"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"strategy",label:e.$t("collector.management.dialog.columns.strategy")}},[o("el-select",{attrs:{filterable:"",clearable:"",placeholder:e.$t("collector.management.placeholder.strategy")},model:{value:e.form.model.strategy,callback:function(t){e.$set(e.form.model,"strategy",t)},expression:"form.model.strategy"}},e._l(e.filterOption,(function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{prop:"files",label:e.$t("collector.management.dialog.columns.logFile")}},[o("el-upload",{directives:[{name:"has",rawName:"v-has",value:"upload",expression:"'upload'"}],ref:"upload",staticClass:"header-button-upload",attrs:{action:"#",headers:{"Content-Type":"multipart/form-data"},"auto-upload":"","show-file-list":!0,limit:1,accept:".txt","file-list":e.form.model.files,"on-exceed":e.handleExceed,"on-change":e.onUploadFileChange,"http-request":e.submitUploadFile,"on-remove":e.handleRemove,"before-upload":e.beforeUploadValidate},on:{click:e.clickUploadTable}},[o("el-input",{staticStyle:{cursor:"pointer"},attrs:{placeholder:e.$t("collector.management.placeholder.logFile"),"suffix-icon":"el-icon-folder-opened"}})],1)],1)],1)],1)],1)],1),o("el-tab-pane",{attrs:{label:e.$t("collector.management.dialog.columns.historyImport"),name:"second"}},[o("main",{staticClass:"table-body-main"},[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.table.loading,expression:"table.loading"}],attrs:{data:e.table.data,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"240"}},e._l(e.tableColoums,(function(t,a){return o("el-table-column",{key:a,attrs:{prop:t,label:e.$t("collector.management.dialog.columns."+t),"show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(a){return[o("p","propertyKind"===t?[e._v(e._s(a.row.categoryName)+" - "+e._s(a.row.typeName))]:[e._v(" "+e._s(a.row[t])+" ")])]}}],null,!0)})})),1)],1),o("footer",{staticClass:"table-footer"},[e.pagination.visible?o("el-pagination",{attrs:{small:"",background:"",align:"right",layout:"total, sizes, prev, pager, next, jumper","current-page":e.pagination.pageNum,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,total:e.pagination.total},on:{"size-change":e.tableSizeChange,"current-change":e.tableCurrentChange}}):e._e()],1)])],1)],1)},Ue=[],Ze=(o("baa5"),o("a434"),o("b0c0"),o("b64b"),{components:{CustomDialog:c["a"]},props:{visible:{required:!0,type:Boolean},title:{required:!0,type:String},validate:{type:Boolean,default:!0},form:{required:!0,type:Object},filterOption:{required:!0,type:Array},collectionAddressOption:{required:!0,type:Array},deviceTypeOption:{required:!0,type:Array}},data:function(){return{dialogVisible:this.visible,activeName:"first",table:{loading:!1,data:[]},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0},uploadFile:{},options:{codeOption:[{label:"自动",value:"auto"},{label:"UTF-8",value:"utf8"},{label:"GBK",value:"gbk"}]},tableColoums:["ipAddress","propertyKind","fileName","createTime"]}},computed:{rules:function(){return this.validate?this.form.rules:null}},watch:{visible:function(e){this.dialogVisible=e,e&&this.initData()},dialogVisible:function(e){this.$emit("update:visible",e)}},mounted:function(){this.queryTableData()},methods:{initData:function(){this.activeName="first",this.uploadFile={}},clickCancel:function(){this.$refs.dialogTemplate.end(),this.dialogVisible=!1},handleExceed:function(e,t){var o=this.$t("collector.management.dialog.exceed",[e.length,e.length,t.length]);this.$message.warning(o)},onUploadFileChange:function(e){this.form.model.files.push(e)},submitUploadFile:function(e){if(e.file&&this.form.model.files.length>0){var t=new FormData;t.append("name","upload"),t.append("file",e.file),this.uploadFile=t}},handleRemove:function(e,t){this.form.model.files.splice(0,1)},beforeUploadValidate:function(e){if(this.form.model.files.length>0){var t=e.name.substring(e.name.lastIndexOf(".txt")+1),o="txt"===t;if(!o)return Object(pe["a"])({i18nCode:"tip.upload.typeError",type:"warning"}),o}},clickUploadTable:function(){this.form.model.files=[],this.$refs.upload.submit()},handleClick:function(e,t){"second"===this.activeName&&this.queryTableData()},clickSubmit:function(){var e=this;this.$refs.logForm.validate((function(t){t?e.$confirm(e.$t("tip.confirm.submit"),e.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){e.handleImportItems(),e.$emit("on-submit",e.uploadFile),e.$refs.logForm.resetFields()})):Object(pe["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1}))})),this.$refs.dialogTemplate.end()},handleImportItems:function(){var e=this,t=this.$refs.cascader.getCheckedNodes(!0);this.buildTreeData(this.form.model,t),this.form.model.propertyKind="",Object.keys(this.form.model).map((function(t){e.uploadFile.append(t,e.form.model[t])}))},buildTreeData:function(e,t){var o=Object(l["a"])(t,1),a=o[0],r=Object(l["a"])(a.pathLabels,2),i=r[0],n=r[1],s=Object(l["a"])(a.path,2),c=s[0],d=s[1];Object.assign(e,{categoryName:i,categoryID:c,typeName:n,typeId:d})},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.queryTableData()},tableCurrentChange:function(e){this.pagination.pageNum=e,this.queryTableData()},queryTableData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.table.loading=!0,this.pagination.visible=!1,_e(t).then((function(t){t&&(e.table.data=t.rows,e.pagination.total=t.total,e.pagination.pageNum=t.pageNum,e.pagination.pageSize=t.pageSize),e.table.loading=!1,e.pagination.visible=!0}))}}}),Ge=Ze,Je=(o("2084"),Object(f["a"])(Ge,Me,Ue,!1,null,"3ef810e7",null)),He=Je.exports,Xe=o("13c3"),Ye=o("c54a"),et=o("21f4"),tt={name:"CollectorManagement",components:{AddDialog:Ke,UpdateDialog:Pe,LogImportDialog:He},data:function(){var e=this,t=function(t,o,a){""===o?a(new Error(e.$t("validate.empty"))):Object(Ye["e"])(o)?a():a(new Error(e.$t("validate.ip.incorrect")))},o=function(t,o,a){""===o?a(new Error(e.$t("validate.empty"))):Object(Ye["n"])(o)?a():a(new Error(e.$t("validate.comm.port")))},a=function(t,o,a){""===o||Object(et["b"])(o)?a(new Error(e.$t("validate.empty"))):Object(Ye["k"])(o)?a():a(new Error(e.$t("validate.address.incorrect")))},r=function(t,o,a){""===o?a(new Error(e.$t("validate.empty"))):Object(Ye["e"])(o)?a(new Error(e.$t("collector.management.error.collectorName"))):a()};return{title:this.$t("collector.management.header"),data:{loading:!1,table:[],selected:[],debounce:{query:null,resetQueryDebounce:null}},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0},option:{tableOption:["collectorName","ip","protName","agentIp","typeName"],filterOption:[],collectionAddressOption:[],useStateOption:[{label:this.$t("collector.management.label.useState.on"),value:"1"},{label:this.$t("collector.management.label.useState.off"),value:"0"}],innerTypeOption:[],deviceTypeOption:[],agentOption:[]},show:{seniorQueryShow:!1},flag:{protName:""},dialog:{addDialog:{visibility:!1,title:this.$t("collector.management.dialog.title.add"),form:{model:{collectorName:"",protId:"101",ip:"",kafkaAddress:"",codeWay:"",agentId:"",logType:"",propertyKind:[],domain:"",describe:"",dbType:"",dbInst:"",port:"",userName:"",password:"",strategy:"",isAsset:1},info:{collectorName:{key:"collectorName",label:this.$t("collector.management.dialog.columns.collectorName")},protId:{key:"protId",label:this.$t("collector.management.dialog.columns.innerType")},strategy:{key:"strategy",label:this.$t("collector.management.dialog.columns.strategy")},codeWay:{key:"codeWay",label:this.$t("collector.management.dialog.columns.codeWay")},agentIp:{key:"agentId",label:this.$t("collector.management.dialog.columns.agentIp")},logType:{key:"logType",label:this.$t("collector.management.dialog.columns.logType")},ip:{key:"ip",label:this.$t("collector.management.dialog.columns.ip")},kafkaAddress:{key:"kafkaAddress",label:this.$t("collector.management.dialog.columns.kafkaAddress")},propertyKind:{key:"propertyKind",label:this.$t("collector.management.dialog.columns.propertyKind")},describe:{key:"describe",label:this.$t("collector.management.dialog.columns.describe")},dbType:{key:"dbType",label:this.$t("collector.management.dialog.columns.dbType")},dbInst:{key:"dbInst",label:this.$t("collector.management.dialog.columns.dbInst")},domain:{key:"domain",label:this.$t("collector.management.dialog.columns.domain")},port:{key:"port",label:this.$t("collector.management.dialog.columns.port")},userName:{key:"userName",label:this.$t("collector.management.dialog.columns.userName")},password:{key:"password",label:this.$t("collector.management.dialog.columns.password")},directory:{key:"directory",label:this.$t("collector.management.dialog.columns.directory")},topic:{key:"topic",label:this.$t("collector.management.dialog.columns.topic")},isAsset:{key:"isAsset",label:this.$t("collector.management.dialog.columns.isAsset")}},rules:{protId:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],collectorName:[{required:!0,validator:r,trigger:"blur"}],codeWay:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],agentId:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],propertyKind:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],ip:[{required:!0,validator:t,trigger:"blur"}],kafkaAddress:[{required:!0,validator:a,trigger:"blur"}],dbType:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],userName:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],password:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],dbInst:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],port:[{required:!0,validator:o,trigger:"blur"}],directory:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],topic:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}]}}},updateDialog:{visibility:!1,title:this.$t("collector.management.dialog.title.update"),colOption:[[[{key:"ip",label:this.$t("collector.management.dialog.columns.ip")},{key:"port",label:this.$t("collector.management.dialog.columns.port")},{key:"userName",label:this.$t("collector.management.dialog.columns.userName")},{key:"password",label:this.$t("collector.management.dialog.columns.password")}]],[[{key:"ip",label:this.$t("collector.management.dialog.columns.ip")},{key:"port",label:this.$t("collector.management.dialog.columns.port")}]],[[{key:"ip",label:this.$t("collector.management.dialog.columns.ip")},{key:"domain",label:this.$t("collector.management.dialog.columns.domain")},{key:"userName",label:this.$t("collector.management.dialog.columns.userName")},{key:"password",label:this.$t("collector.management.dialog.columns.password")}]],[[{key:"ip",label:this.$t("collector.management.dialog.columns.ip")}]],[[{key:"userName",label:this.$t("collector.management.dialog.columns.userName")},{key:"password",label:this.$t("collector.management.dialog.columns.password")}]]],form:{model:{devId:"",collectorName:"",IP:"",codeWay:"",agentId:"",logType:"",propertyKind:[],describe:"",password:"",userName:"",domain:"",port:"",dbType:"",dbInst:"",strategy:""},info:{collectorName:{key:"collectorName",label:this.$t("collector.management.dialog.columns.collectorName")},protId:{key:"protId",label:this.$t("collector.management.dialog.columns.innerType")},ip:{key:"ip",label:this.$t("collector.management.dialog.columns.ip")},kafkaAddress:{key:"kafkaAddress",label:this.$t("collector.management.dialog.columns.kafkaAddress")},codeWay:{key:"codeWay",label:this.$t("collector.management.dialog.columns.codeWay")},agentIp:{key:"agentId",label:this.$t("collector.management.dialog.columns.agentIp")},logType:{key:"logType",label:this.$t("collector.management.dialog.columns.logType")},propertyKind:{key:"propertyKind",label:this.$t("collector.management.dialog.columns.propertyKind")},describe:{key:"describe",label:this.$t("collector.management.dialog.columns.describe")},password:{key:"password",label:this.$t("collector.management.dialog.columns.password")},userName:{key:"userName",label:this.$t("collector.management.dialog.columns.userName")},domain:{key:"domain",label:this.$t("collector.management.dialog.columns.domain")},port:{key:"port",label:this.$t("collector.management.dialog.columns.port")},dbType:{key:"dbType",label:this.$t("collector.management.dialog.columns.dbType")},dbInst:{key:"dbInst",label:this.$t("collector.management.dialog.columns.dbInst")},strategy:{key:"strategy",label:this.$t("collector.management.dialog.columns.strategy")},directory:{key:"directory",label:this.$t("collector.management.dialog.columns.directory")},topic:{key:"topic",label:this.$t("collector.management.dialog.columns.topic")},isAsset:{key:"isAsset",label:this.$t("collector.management.dialog.columns.isAsset")}},rules:{collectorName:[{required:!0,validator:r,trigger:"blur"}],protId:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],codeWay:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],agentId:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],propertyKind:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],ip:[{required:!0,validator:t,trigger:"blur"}],kafkaAddress:[{required:!0,validator:a,trigger:"blur"}],dbType:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],dbInst:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],port:[{required:!0,validator:o,trigger:"blur"}],userName:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],password:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],directory:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],topic:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}]}}},logImportDialog:{visible:!1,form:{model:{},rules:{IP:[{required:!0,validator:t,trigger:"blur"}],codeWay:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],propertyKind:[{required:!0,message:this.$t("validate.empty"),trigger:"change"}],files:[{required:!0,message:this.$t("validate.choose"),trigger:"change"}]}}}},query:{inputVal:"",seniorQuery:{collectorName:"",IP:"",useState:"",protId:""}}}},mounted:function(){this.init()},methods:{init:function(){this.initOption(),this.initDebounce(),this.queryTableData()},initDebounce:function(){var e=this;this.data.debounce.query=Object(Xe["a"])((function(){var t={};t=e.show.seniorQueryShow?Object.assign({},e.query.seniorQuery,{pageSize:e.pagination.pageSize,pageNum:e.pagination.pageNum}):{pageSize:e.pagination.pageSize,pageNum:e.pagination.pageNum,inputVal:e.query.inputVal},e.queryTableData(t)}),500),this.data.debounce.resetQueryDebounce=Object(Xe["a"])((function(){e.pagination.pageNum=1,e.query.seniorQuery={collectorName:"",IP:"",useState:"",protId:""},setTimeout((function(){e.queryTableData()}),150)}),500)},initOption:function(){var e=this;Ae().then((function(t){e.option.innerTypeOption=t})),Se().then((function(t){e.option.deviceTypeOption=t})),xe().then((function(t){e.option.filterOption=t})),Ce().then((function(t){e.option.collectionAddressOption=t})),qe().then((function(t){e.option.agentOption=t}))},queryTableData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.data.loading=!0,this.pagination.visible=!1,ve(t).then((function(t){t&&(e.data.table=t.rows,e.pagination.total=t.total,e.pagination.pageNum=t.pageNum,e.pagination.pageSize=t.pageSize),e.data.loading=!1,e.pagination.visible=!0}))},pageQuery:function(e){this.validatorIp()&&(e&&(this.pagination.pageNum=1),this.data.debounce.query())},validatorIp:function(){var e=this.query.seniorQuery.IP||"";return!(""!==e&&!Object(Ye["e"])(e))||(Object(pe["a"])({i18nCode:"validate.ip.incorrect",type:"error"}),!1)},addCollector:function(e){var t=this;ke(e).then((function(e){1===e?Object(pe["a"])({i18nCode:"tip.add.success",type:"success"},(function(){t.queryTableData()})):2===e?Object(pe["a"])({i18nCode:"tip.add.repeat",type:"error"}):3===e?Object(pe["a"])({i18nCode:"tip.add.innerType",type:"error"}):4===e?Object(pe["a"])({i18nCode:"tip.add.maxCount",type:"error"}):5===e?Object(pe["a"])({i18nCode:"tip.add.innerTypeRepeat",type:"error"}):7===e?Object(pe["a"])({i18nCode:"tip.add.logSourceExist",type:"error"}):Object(pe["a"])({i18nCode:"tip.add.error",type:"error"})}))},updateCollector:function(e){var t=this;we(e).then((function(e){1===e?Object(pe["a"])({i18nCode:"tip.update.success",type:"success"},(function(){t.pageQuery()})):2===e?Object(pe["a"])({i18nCode:"tip.update.repeat",type:"error"}):3===e?Object(pe["a"])({i18nCode:"tip.update.innerType",type:"error"}):7===e?Object(pe["a"])({i18nCode:"tip.update.logSourceExist",type:"error"}):Object(pe["a"])({i18nCode:"tip.update.error",type:"error"})}))},deleteCollector:function(e){var t=this;$e(e).then((function(o){o?Object(pe["a"])({i18nCode:"tip.delete.success",type:"success"},(function(){t.query={inputVal:"",seniorQuery:{collectorName:"",IP:"",useState:"",protId:""}};var o=[t.pagination.pageNum,e.split(",")],a=o[0],r=o[1];r.length===t.data.table.length&&(t.pagination.pageNum=1===a?1:a-1),t.queryTableData()})):Object(pe["a"])({i18nCode:"tip.delete.error",type:"error"})}))},updateStatus:function(e,t){var o=this;Oe(e,t).then((function(e){e?"1"===t?Object(pe["a"])({i18nCode:"tip.enable.success",type:"success"},(function(){o.pageQuery()})):Object(pe["a"])({i18nCode:"tip.disable.success",type:"success"},(function(){o.pageQuery()})):Object(pe["a"])({i18nCode:"tip.update.error",type:"error"})}))},clickQueryButton:function(){this.query.inputVal="",this.show.seniorQueryShow=!this.show.seniorQueryShow,this.initDebounce(),this.resetSeniorQuery()},clickDeleteButton:function(e){var t=this,o=e.devId;this.$confirm(this.$t("tip.confirm.batchDelete"),this.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){t.deleteCollector(o)}))},clickUpButton:function(){this.show.seniorQueryShow=!this.show.seniorQueryShow,this.initDebounce(),this.resetSeniorQuery()},clickLogImportButton:function(){this.initOption(),this.dialog.logImportDialog.visible=!0,this.dialog.logImportDialog.form.model={IP:"",codeWay:"auto",propertyKind:[],strategy:"",files:[]}},clickAddButton:function(){this.dialog.addDialog.visibility=!0,this.dialog.addDialog.form.model={collectorName:"",protId:"101",ip:"",kafkaAddress:"",propertyKind:[],codeWay:"auto",logType:"",describe:"",dbType:"",dbInst:"",port:"",userName:"",password:"",isAsset:1}},clickSubmitAdd:function(e,t){var o=this;return Object(i["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return"101"!==e.protId&&"102"!==e.protId&&"107"!==e.protId&&"108"!==e.protId&&"110"!==e.protId||o.buildTreeData(e,t),a.next=3,o.addCollector(e);case 3:case"end":return a.stop()}}),a)})))()},clickUpdateButton:function(e){var t=this;Ne(e.devId).then((function(e){var o=e.typeId,a=e.categoryID,r=Object.assign({},e,{propertyKind:[a,o]});t.dialog.updateDialog.form.model=r})),this.flag.protName=String(e.protName).toLowerCase(),"Kafka"===e.protName?(this.dialog.updateDialog.form.rules.password=[{required:!1}],this.dialog.updateDialog.form.rules.userName=[{required:!1}]):(this.dialog.updateDialog.form.rules.password=[{required:!0,message:this.$t("validate.empty")}],this.dialog.updateDialog.form.rules.userName=[{required:!0,message:this.$t("validate.empty")}]),this.dialog.updateDialog.visibility=!0},clickSubmitUpdate:function(e,t){"101"!==e.protId&&"102"!==e.protId&&"107"!==e.protId&&"108"!==e.protId&&"110"!==e.protId||this.buildTreeData(e,t),this.updateCollector(e)},tableSelectsChange:function(e){this.data.selected=e},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.pageQuery()},tableCurrentChange:function(e){this.pagination.pageNum=e,this.pageQuery()},toggleStatus:function(e){this.updateStatus(e.devId,e.useState)},resetSeniorQuery:function(){this.data.debounce.resetQueryDebounce()},buildTreeData:function(e,t){var o=Object(l["a"])(t,1),a=o[0],r=Object(l["a"])(a.pathLabels,2),i=r[0],n=r[1],s=Object(l["a"])(a.path,2),c=s[0],d=s[1];Object.assign(e,{categoryName:i,categoryID:c,typeName:n,typeId:d})},batchRun:function(){var e=[];this.data.selected.map((function(t){return e.push(t.devId)})),0===e.length?Object(pe["a"])({i18nCode:"collector.management.label.runSuccess",type:"info"}):this.updateStatus(e.toString(),"1")},batchStop:function(){var e=[];this.data.selected.map((function(t){return e.push(t.devId)})),0===e.length?Object(pe["a"])({i18nCode:"collector.management.label.runError",type:"info"}):this.updateStatus(e.toString(),"0")},batchDelete:function(){var e=this,t=[];this.data.selected.map((function(e){return t.push(e.devId)})),0===t.length?Object(pe["a"])({i18nCode:"tip.delete.prompt",type:"error"}):this.$confirm(this.$t("tip.confirm.batchDelete"),this.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){$e(t.toString()).then((function(o){o?Object(pe["a"])({i18nCode:"tip.delete.success",type:"success"},(function(){e.query.inputVal="",e.query.seniorQuery={collectorName:"",IP:"",useState:"",protId:""};var o=[e.pagination.pageNum,t],a=o[0],r=o[1];r.length===e.data.table.length&&(e.pagination.pageNum=1===a?1:a-1),e.queryTableData()})):Object(pe["a"])({i18nCode:"tip.delete.error",type:"error"})}))}))},handleCommand:function(e){switch(e){case"run":this.batchRun();break;case"stop":this.batchStop();break;case"delete":this.batchDelete();break}},changeAccess:function(e){var t=e.protId,o=e.collectorName,a=e.codeWay;this.dialog.addDialog.form.model={collectorName:o,protId:t,codeWay:a,ip:"",kafkaAddress:"",propertyKind:[],describe:"",dbType:"",dbInst:"",port:"",userName:"",password:"",isAsset:1},"110"===t?(this.dialog.addDialog.form.rules.password=[{required:!1}],this.dialog.addDialog.form.rules.userName=[{required:!1}]):(this.dialog.addDialog.form.rules.password=[{required:!0,message:this.$t("validate.empty")}],this.dialog.addDialog.form.rules.userName=[{required:!0,message:this.$t("validate.empty")}])},clickLogImportSubmit:function(e){var t=this;Ie(e).then((function(e){e>0?Object(pe["a"])({i18nCode:t.$t("tip.import.success"),type:"success"}):Object(pe["a"])({i18nCode:"tip.import.error",type:"error"})})).catch((function(e){console.error(e)}))}}},ot=tt,at=Object(f["a"])(ot,a,r,!1,null,null,null);t["default"]=at.exports},a1cb:function(e,t,o){},a434:function(e,t,o){"use strict";var a=o("23e7"),r=o("23cb"),l=o("a691"),i=o("50c4"),n=o("7b0b"),s=o("65f0"),c=o("8418"),d=o("1dde"),u=o("ae40"),m=d("splice"),p=u("splice",{ACCESSORS:!0,0:0,1:2}),f=Math.max,g=Math.min,b=9007199254740991,h="Maximum allowed length exceeded";a({target:"Array",proto:!0,forced:!m||!p},{splice:function(e,t){var o,a,d,u,m,p,y=n(this),v=i(y.length),$=r(e,v),k=arguments.length;if(0===k?o=a=0:1===k?(o=0,a=v-$):(o=k-2,a=g(f(l(t),0),v-$)),v+o-a>b)throw TypeError(h);for(d=s(y,a),u=0;u<a;u++)m=$+u,m in y&&c(d,u,y[m]);if(d.length=a,o<a){for(u=$;u<v-a;u++)m=u+a,p=u+o,m in y?y[p]=y[m]:delete y[p];for(u=v;u>v-a+o;u--)delete y[u-1]}else if(o>a)for(u=v-a;u>$;u--)m=u+a-1,p=u+o-1,m in y?y[p]=y[m]:delete y[p];for(u=0;u<o;u++)y[u+$]=arguments[u+2];return y.length=v-a+o,d}})},ab13:function(e,t,o){var a=o("b622"),r=a("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(o){try{return t[r]=!1,"/./"[e](t)}catch(a){}}return!1}},c54a:function(e,t,o){"use strict";o.d(t,"l",(function(){return a})),o.d(t,"m",(function(){return r})),o.d(t,"b",(function(){return l})),o.d(t,"c",(function(){return i})),o.d(t,"a",(function(){return n})),o.d(t,"j",(function(){return s})),o.d(t,"q",(function(){return c})),o.d(t,"d",(function(){return d})),o.d(t,"f",(function(){return u})),o.d(t,"g",(function(){return m})),o.d(t,"e",(function(){return p})),o.d(t,"n",(function(){return f})),o.d(t,"k",(function(){return g})),o.d(t,"p",(function(){return b})),o.d(t,"h",(function(){return h})),o.d(t,"i",(function(){return y})),o.d(t,"o",(function(){return v}));o("ac1f"),o("466d"),o("1276");function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o="";switch(t){case 0:o=/^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/;break;case 1:o=/^(?![_.\-])(?!.*?[_.\-]$)[a-zA-Z0-9_.\-\u4e00-\u9fa5]+$/;break;case 2:o=/^(?![_./\-])(?!.*?[_./\-]$)[a-zA-Z0-9_./\-\u4e00-\u9fa5]+$/;break;case 3:o=/^(?![_./\-\s])(?!.*?[_./\-\s]$)[a-zA-Z0-9_./\-\s\u4e00-\u9fa5]+$/;break;default:o=/^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/;break}return o.test(e)}function r(e){var t=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\[\].<>/?\-%]).{0,}$/;return t.test(e)}function l(e){var t=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return t.test(e)}function i(e){var t=/^([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;return t.test(e)}function n(e){var t=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return t.test(e)}function s(e){for(var t=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,o=e.split(","),a=0;a<o.length;a++)if(!t.test(o[a]))return!1;return!0}function c(e){var t=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return t.test(e)}function d(e){var t=/^(\d{2,5}-)?\d{6,9}(-\d{2,4})?$/;return t.test(e)}function u(e){var t=/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;return t.test(e)}function m(e){var t=/:/.test(e)&&e.match(/:/g).length<8&&/::/.test(e)?1===e.match(/::/g).length&&/^::$|^(::)?([\da-f]{1,4}(:|::))*[\da-f]{1,4}(:|::)?$/i.test(e):/^([\da-f]{1,4}:){7}[\da-f]{1,4}$/i.test(e);return t}function p(e){return u(e)||m(e)}function f(e){var t=/^([0-9]|[1-9][0-9]{0,4})$/;return t.test(e)}function g(e){for(var t=/^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,o=e.split(","),a=0;a<o.length;a++)if(!t.test(o[a]))return!1;return!0}function b(e){var t=/^[^ ]+$/;return t.test(e)}function h(e){var t=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\.[A-Fa-f0-9]{4}){2}$/;return t.test(e)}function y(e){var t=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return t.test(e)}function v(e){var t=/[^\u4E00-\u9FA5]/;return t.test(e)}},caad:function(e,t,o){"use strict";var a=o("23e7"),r=o("4d64").includes,l=o("44d2"),i=o("ae40"),n=i("indexOf",{ACCESSORS:!0,1:0});a({target:"Array",proto:!0,forced:!n},{includes:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),l("includes")},d0af:function(e,t,o){"use strict";function a(e){if(Array.isArray(e))return e}o.d(t,"a",(function(){return n}));o("a4d3"),o("e01a"),o("d28b"),o("d3b7"),o("3ca3"),o("ddb0");function r(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var o=[],a=!0,r=!1,l=void 0;try{for(var i,n=e[Symbol.iterator]();!(a=(i=n.next()).done);a=!0)if(o.push(i.value),t&&o.length===t)break}catch(s){r=!0,l=s}finally{try{a||null==n["return"]||n["return"]()}finally{if(r)throw l}}return o}}var l=o("dde1");function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function n(e,t){return a(e)||r(e,t)||Object(l["a"])(e,t)||i()}},d81d:function(e,t,o){"use strict";var a=o("23e7"),r=o("b727").map,l=o("1dde"),i=o("ae40"),n=l("map"),s=i("map");a({target:"Array",proto:!0,forced:!n||!s},{map:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})},e1d2:function(e,t,o){},ebd6:function(e,t,o){"use strict";var a=o("6c01"),r=o.n(a);r.a}}]);