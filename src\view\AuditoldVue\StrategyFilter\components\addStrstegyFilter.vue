<template>
  <div>
    <el-drawer :visible.sync="visible" :title="title" size="800px" direction="rtl" @close="onDrawerClose">
      <div style="padding: 20px;">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" v-loading="loading">
          <!-- 地址类型选择 -->
          <el-form-item label="地址类型" prop="ipType">
            <el-radio-group v-model="form.ipType" @change="handleIpTypeChange">
              <el-radio :label="0">单个地址</el-radio>
              <el-radio :label="1">一对地址</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 过滤地址1 -->
          <el-form-item label="过滤地址1" prop="firstIp">
            <div style="display: flex; align-items: center; width: 95%;">
              <el-input v-model="form.firstIp" placeholder="请输入IP/MAC地址" @blur="validEvent" style="flex: 1;" />
              <el-tooltip
                :content="
                  form.ipType === 0
                    ? '1.当协议为二层协议时，只能填MAC。\n2.当协议为三层协议时，可以IP可以MAC'
                    : '1.地址1与地址2必须同为IP或同为MAC\n2.当协议为二层协议时，只能填MAC。\n3.当协议为三层协议时，可以IP可以MAC'
                "
                placement="top"
                effect="light"
              >
                <i class="el-icon-info" style="margin-left: 8px; color: #909399; cursor: pointer;"></i>
              </el-tooltip>
            </div>
          </el-form-item>

          <!-- 过滤地址2 - 仅在一对地址模式下显示 -->
          <el-form-item v-if="form.ipType === 1" label="过滤地址2" prop="secondIp">
            <div style="display: flex; align-items: center; width: 95%;">
              <el-input v-model="form.secondIp" placeholder="请输入IP/MAC地址" @blur="validEvent" style="flex: 1;" />
              <el-tooltip
                content="1.地址1与地址2必须同为IP或同为MAC\n2.当协议为二层协议时，只能填MAC。\n3.当协议为三层协议时，可以IP可以MAC"
                placement="top"
                effect="light"
              >
                <i class="el-icon-info" style="margin-left: 8px; color: #909399; cursor: pointer;"></i>
              </el-tooltip>
            </div>
          </el-form-item>

          <!-- 过滤协议 -->
          <el-form-item label="过滤协议" prop="protocolName">
            <el-input v-model="form.protocolName" placeholder="请选择协议" readonly style="width: 95%;">
              <template slot="append">
                <el-button @click="handleSelectProtocols" style="height:28px">选择协议</el-button>
              </template>
            </el-input>
          </el-form-item>

          <!-- 隐藏字段 -->
          <el-form-item style="display: none;">
            <el-input v-model="form.id" />
          </el-form-item>
          <el-form-item style="display: none;">
            <el-input v-model="form.protocolId" />
          </el-form-item>
        </el-form>

        <div style="text-align: center; margin-top: 40px;">
          <el-button type="primary" @click="handleSubmit">保存</el-button>
          <el-button style="margin-left: 20px;" @click="onDrawerClose">关闭</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 协议选择模态框 -->
    <protocol-select-modal
      ref="protocolSelectModalRef"
      type="radio"
      :default-protocol-ids="protocolIds"
      :default-protocol-names="protocolNames"
      :protocol-list="protocolList"
      @saveData="saveData"
    />
  </div>
</template>

<script>
import { tacticsAdd, tacticsUpdate } from '@api/auditold/strategyFilter'
import { tacticsPages } from '@api/auditold/strategyProtocol'
import ProtocolSelectModal from '../../StrategyCollection/components/protocolSelectModal'

export default {
  name: 'AddStrategyFilter',
  components: {
    ProtocolSelectModal,
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '',
      form: {
        id: '',
        ipType: 0,
        firstIp: '',
        secondIp: '',
        protocolId: '',
        protocolName: '',
      },
      protocolIds: [],
      protocolNames: [],
      protocolList: [], // 协议列表数据
      rules: {},
    }
  },
  created() {
    // 在created钩子中设置验证规则，这样可以访问this
    this.rules = {
      ipType: [{ required: true, message: '请选择地址类型', trigger: 'change' }],
      firstIp: [
        { required: true, message: '请输入过滤地址1', trigger: 'blur' },
        { validator: this.validatorIP, trigger: 'blur' },
      ],
      secondIp: [{ validator: this.validatorIP2, trigger: 'blur' }],
      protocolName: [{ required: true, message: '请选择过滤协议', trigger: 'blur' }],
    }
  },
  methods: {
    // 加载协议数据
    async loadProtocolData() {
      try {
        const res = await tacticsPages({
          pageIndex: 1,
          pageSize: 1000, // 获取所有协议数据
        })
        if (res.retcode === 0) {
          this.protocolList = res.data.rows || []
        } else {
          this.$message.error('获取协议列表失败：' + res.msg)
        }
      } catch (error) {
        console.error('获取协议列表失败:', error)
        this.$message.error('获取协议列表失败')
      }
    },

    async showDrawer(record = {}) {
      this.visible = true

      // 预加载协议数据
      await this.loadProtocolData()

      if (record.id) {
        this.title = '编辑过滤策略'
        setTimeout(() => {
          this.form = {
            id: record.id,
            ipType: record.ipType || 0,
            firstIp: record.firstIp || '',
            secondIp: record.secondIp || '',
            protocolId: record.protocolId || '',
            protocolName: record.protocolName || '',
          }

          // 设置协议数据
          if (record.protocolId) {
            this.protocolIds = record.protocolId.split(',').map(Number)
          } else {
            this.protocolIds = []
          }

          if (record.protocolName) {
            this.protocolNames = record.protocolName.split(',')
          } else {
            this.protocolNames = []
          }
        })
      } else {
        this.title = '新增过滤策略'
        this.resetForm()
      }
    },

    resetForm() {
      this.form = {
        id: '',
        ipType: 0,
        firstIp: '',
        secondIp: '',
        protocolId: '',
        protocolName: '',
      }
      this.protocolIds = []
      this.protocolNames = []
    },

    handleIpTypeChange() {
      // 当地址类型改变时，清空第二个地址并重新验证
      if (this.form.ipType === 0) {
        this.form.secondIp = ''
      }
      this.validEvent()
    },

    // IP/MAC地址验证器
    validatorIP(_, value, callback) {
      const IP = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const Mac = /[A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}/

      if (value && !IP.test(value) && !Mac.test(value)) {
        return callback(new Error('请输入正确格式的过滤地址'))
      }

      if (value && this.form.secondIp) {
        if (IP.test(value) !== IP.test(this.form.secondIp)) {
          callback(new Error('请输入同IP/MAC过滤地址'))
        } else if (Mac.test(value) !== Mac.test(this.form.secondIp)) {
          callback(new Error('请输入同IP/MAC过滤地址'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    // 第二个地址验证器
    validatorIP2(_, value, callback) {
      const IP = /^(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/
      const Mac = /[A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}[/\s:-][A-Fa-f0-9]{2}/

      if (value && this.form.ipType === 1) {
        if (!IP.test(value) && !Mac.test(value)) {
          return callback(new Error('请输入正确格式的过滤地址'))
        }

        if (value && this.form.firstIp) {
          if (IP.test(value) !== IP.test(this.form.firstIp)) {
            callback(new Error('请输入同IP/MAC过滤地址'))
          } else if (Mac.test(value) !== Mac.test(this.form.firstIp)) {
            callback(new Error('请输入同IP/MAC过滤地址'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback()
      }
    },

    // 触发校验
    validEvent() {
      this.$refs.form && this.$refs.form.validate(() => {})
    },

    onDrawerClose() {
      this.visible = false
      this.loading = false
      this.resetForm()
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            let res
            if (this.title.indexOf('新增') !== -1) {
              res = await tacticsAdd(this.form)
            } else {
              res = await tacticsUpdate(this.form)
            }

            if (res.retcode === 0) {
              this.$message.success('操作成功')
              this.$emit('getSourceData')
              this.onDrawerClose()
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error('操作失败')
          } finally {
            this.loading = false
          }
        }
      })
    },

    // 打开协议选择弹框
    handleSelectProtocols() {
      this.$refs.protocolSelectModalRef.showModal()
    },

    // 保存选中的协议数据
    saveData(obj) {
      this.form.protocolId = obj.ids[0]
      this.form.protocolName = obj.names[0]
      this.protocolIds = obj.ids
      this.protocolNames = obj.names
    },
  },
}
</script>

<style scoped lang="scss">
.el-tooltip {
  white-space: pre-line;
}
</style>
