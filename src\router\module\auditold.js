export default [
  {
    path: '/audit/device/list',
    name: 'AuditOldDeviceList',
    component: () => import('@/view/AuditoldVue/DeviceList'),
    meta: { title: '设备列表', icon: 'soc-icon-point' },
  },
  {
    path: '/audit/device/groupmanagement',
    name: 'AuditOldDeviceGroupManagement',
    component: () => import('@/view/asset/Hostguardiangroup/GroupManagement'),
    meta: { title: '设备分组', icon: 'soc-icon-point' },
  },
  {
    path: '/audit/strategy/strategy-protocol',
    name: 'AuditOldStrategyProtocol',
    component: () => import('@/view/AuditoldVue/StrategyProtocol'),
    meta: { title: '监测审计协议集', icon: 'soc-icon-point' },
  },
  {
    path: '/audit/strategy/strategy-collection',
    name: 'AuditOldStrategyCollection',
    component: () => import('@/view/AuditoldVue/StrategyCollection'),
    meta: { title: '监测审计采集策略', icon: 'soc-icon-point' },
  },
  {
    path: '/audit/strategy/strategy-filter',
    name: 'AuditOldStrategyFilter',
    component: () => import('@/view/AuditoldVue/StrategyFilter'),
    meta: { title: '监测审计过滤策略', icon: 'soc-icon-point' },
  },
  {
    path: '/audit/strategy/strategyAudit-record',
    name: 'AuditOldStrategyAuditRecord',
    component: () => import('@/view/AuditoldVue/StrategyAuditRecord'),
    meta: { title: '策略下发记录', icon: 'soc-icon-point' },
  },
  {
    path: '/audit/alarm-log',
    name: 'AlarmLog',
    component: () => import('@/view/asset/FirewallVue/StrategyRecord'),
    meta: { title: '告警日志', icon: 'soc-icon-point' },
  },
]
