(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54f25af3"],{"078a":function(e,t,a){"use strict";var n=a("2b0e"),l=(a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319"),{bind:function(e,t,a){var n=[e.querySelector(".el-dialog__header"),e.querySelector(".el-dialog")],l=n[0],o=n[1];l.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var i=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();l.onmousedown=function(e){var t=[e.clientX-l.offsetLeft,e.clientY-l.offsetTop,o.offsetWidth,o.offsetHeight,document.body.clientWidth,document.body.clientHeight],n=t[0],r=t[1],s=t[2],c=t[3],u=t[4],d=t[5],v=[o.offsetLeft,u-o.offsetLeft-s,o.offsetTop,d-o.offsetTop-c],h=v[0],f=v[1],p=v[2],y=v[3],b=[i(o,"left"),i(o,"top")],g=b[0],m=b[1];g.includes("%")?(g=+document.body.clientWidth*(+g.replace(/%/g,"")/100),m=+document.body.clientHeight*(+m.replace(/%/g,"")/100)):(g=+g.replace(/px/g,""),m=+m.replace(/px/g,"")),document.onmousemove=function(e){var t=e.clientX-n,l=e.clientY-r;-t>h?t=-h:t>f&&(t=f),-l>p?l=-p:l>y&&(l=y),o.style.cssText+=";left:".concat(t+g,"px;top:").concat(l+m,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),o=function(e){e.directive("el-dialog-drag",l)};window.Vue&&(window["el-dialog-drag"]=l,n["default"].use(o)),l.elDialogDrag=o;t["a"]=l},"1f14":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("header",{staticClass:"table-header"},[a("section",{staticClass:"table-header-main"},[a("section",{staticClass:"table-header-search"},[a("section",{directives:[{name:"show",rawName:"v-show",value:!e.show.seniorQueryShow,expression:"!show.seniorQueryShow"}],staticClass:"table-header-search-input"},[a("el-input",{attrs:{placeholder:e.$t("tip.placeholder.query",[e.$t("repository.cnvd.fuzzyQuery")]),clearable:""},on:{change:function(t){return e.pageQuery("e")}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.pageQuery("e")}},model:{value:e.query.inputVal,callback:function(t){e.$set(e.query,"inputVal","string"===typeof t?t.trim():t)},expression:"query.inputVal"}},[a("i",{staticClass:"el-input__icon soc-icon-search",attrs:{slot:"prefix"},on:{click:function(t){return e.inputQuery("e")}},slot:"prefix"})])],1),a("section",{staticClass:"table-header-search-button"},[e.show.seniorQueryShow?e._e():a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:function(t){return e.pageQuery("e")}}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.clickQueryButton}},[e._v(" "+e._s(e.$t("button.search.exact"))+" "),a("i",{staticClass:"el-icon--right",class:e.show.seniorQueryShow?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),a("section",{staticClass:"table-header-button"})]),a("section",{staticClass:"table-header-extend"},[a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.show.seniorQueryShow,expression:"show.seniorQueryShow"}],staticClass:"table-header-query"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{clearable:"",placeholder:e.$t("repository.cnvd.table.cnvdId")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.cnvdId,callback:function(t){e.$set(e.query.seniorQuery,"cnvdId","string"===typeof t?t.trim():t)},expression:"query.seniorQuery.cnvdId"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{clearable:"",placeholder:e.$t("repository.cnvd.table.cnvdTitle")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.cnvdTitle,callback:function(t){e.$set(e.query.seniorQuery,"cnvdTitle","string"===typeof t?t.trim():t)},expression:"query.seniorQuery.cnvdTitle"}})],1),a("el-col",{attrs:{span:10}},[a("el-date-picker",{attrs:{clearable:"",type:"datetimerange","value-format":"yyyy-MM-dd HH:mm:ss","start-placeholder":e.$t("repository.cnvd.table.startDate"),"end-placeholder":e.$t("repository.cnvd.table.endDate")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.releaseDate,callback:function(t){e.$set(e.query.seniorQuery,"releaseDate",t)},expression:"query.seniorQuery.releaseDate"}})],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-select",{attrs:{clearable:"",placeholder:e.$t("repository.cnvd.table.cnvdLevel")},on:{change:function(t){return e.pageQuery("e")}},model:{value:e.query.seniorQuery.cnvdLevel,callback:function(t){e.$set(e.query.seniorQuery,"cnvdLevel",t)},expression:"query.seniorQuery.cnvdLevel"}},e._l(e.option.levelOption,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{align:"right",offset:15,span:4}},[a("el-button",{on:{click:function(t){return e.pageQuery("e")}}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetSeniorQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")]),a("el-button",{ref:"shrinkButton",on:{click:e.clickUpButton}},[a("i",{staticClass:"soc-icon-scroller-top-all"})])],1)],1)],1)])],1)]),a("main",{staticClass:"table-body"},[a("header",{staticClass:"table-body-header"},[a("h2",{staticClass:"table-body-title"},[e._v(" "+e._s(e.$t("repository.cnvd.header"))+" ")])]),a("main",{directives:[{name:"loading",rawName:"v-loading",value:e.data.loading,expression:"data.loading"}],staticClass:"table-body-main"},[a("el-table",{directives:[{name:"show",rawName:"v-show",value:e.btnRef,expression:"btnRef"},{name:"el-table-scroll",rawName:"v-el-table-scroll",value:e.scrollTable,expression:"scrollTable"}],ref:"auditTable",attrs:{"infinite-scroll-disabled":"disableScroll",data:e.data.table,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"}},[e._l(e.option.columnOption,(function(t,n){return a("el-table-column",{key:n,attrs:{prop:t,label:e.$t("repository.cnvd.table."+t),"show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(n){return["cnvdLevel"===t?a("level-tag",{attrs:{level:e.levelEnum(n.row.cnvdLevel)}}):a("p",[e._v(" "+e._s(n.row[t])+" ")])]}}],null,!0)})})),a("el-table-column",{attrs:{fixed:"right",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"el-button--blue",on:{click:function(a){return e.clickDetailButton(t.row)}}},[e._v(" "+e._s(e.$t("button.detail"))+" ")])]}}])})],2)],1)]),a("footer",{staticClass:"table-footer infinite-scroll"},[a("section",{staticClass:"infinite-scroll-nomore"},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.data.nomore,expression:"data.nomore"}]},[e._v(e._s(e.$t("validate.data.nomore")))]),a("i",{directives:[{name:"show",rawName:"v-show",value:e.data.totalLoading,expression:"data.totalLoading"}],staticClass:"el-icon-loading"})]),a("section",{staticClass:"infinite-scroll-total"},[a("b",[e._v(e._s(e.$t("event.original.total")+":"))]),a("span",[e._v(e._s(e.data.total))])])]),a("detail-dialog",{attrs:{visible:e.dialog.detailDialog.visible,title:e.dialog.detailDialog.title,form:e.dialog.detailDialog.form,"column-bottom":e.dialog.detailDialog.columnBottom,"column-top":e.dialog.detailDialog.columnTop},on:{"update:visible":function(t){return e.$set(e.dialog.detailDialog,"visible",t)}}})],1)},l=[],o=(a("d3b7"),a("25f0"),a("d0ff")),i=a("746c"),r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("custom-dialog",{ref:"dialogTemplate",attrs:{visible:e.visible,title:e.title,width:e.width},on:{"on-close":e.clickCancelDialog}},[a("section",[a("el-form",{ref:"formTemplate",attrs:{model:e.form.model,"label-width":"120px"}},[a("el-form",{ref:"formTemplate",attrs:{model:e.form.model,"label-width":"120px"}},[a("el-row",e._l(e.columnTop,(function(t,n){return a("el-col",{key:n,attrs:{span:12}},[a("el-form-item",{attrs:{prop:t.key,label:t.label}},[["cnvdLevel"===t.key?a("level-tag",{attrs:{level:e.levelEnum(e.form.model[t.key])}}):a("p",[e._v(" "+e._s(e.form.model[t.key])+" ")])]],2)],1)})),1),e._l(e.columnBottom,(function(t,n){return a("el-row",{key:n},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{prop:t.key,label:t.label}},[e._v(" "+e._s(e.form.model[t.key])+" ")])],1)],1)}))],2)],1)],1),e.actions?e._e():a("template",{slot:"action"},[a("fragment")],1)],2)},s=[],c=a("d465"),u=a("8986"),d=(a("f7b5"),{components:{CustomDialog:c["a"],levelTag:u["a"]},props:{visible:{required:!0,type:Boolean},title:{required:!0,type:String},form:{required:!0,type:Object},width:{type:String,default:"1000"},actions:{type:Boolean,default:!1},columnTop:{type:Array},columnBottom:{type:Array}},data:function(){return{dialogVisible:this.visible}},computed:{rules:function(){return this.validate?this.form.rules:null},levelEnum:function(){return function(e){var t={"严重":"0","高":"1","中":"2","低":"3","一般":"4"};return t[e]}}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit("update:visible",e)}},methods:{clickCancelDialog:function(){this.$refs.dialogTemplate.end(),this.dialogVisible=!1}}}),v=d,h=a("2877"),f=Object(h["a"])(v,r,s,!1,null,null,null),p=f.exports,y=a("13c3"),b=a("4020");function g(e){return Object(b["a"])({url:"/knowledge/vulnerability/cnvd",method:"get",params:e||{}})}function m(e){return Object(b["a"])({url:"/knowledge/vulnerability/cnvd/".concat(e),method:"get"})}function w(e){return Object(b["a"])({url:"/knowledge/vulnerability/cnvd/total",method:"get",params:e||{}})}var k={name:"RepositoryCnvd",directives:{elTableScroll:i["a"]},components:{DetailDialog:p,levelTag:u["a"]},data:function(){return{btnRef:!0,data:{loading:!1,table:[],total:0,nomore:!1,totalLoading:!1,debounce:{query:null,resetQueryDebounce:null}},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0},option:{levelOption:[{label:this.$t("level.serious"),value:"严重"},{label:this.$t("level.high"),value:"高"},{label:this.$t("level.middle"),value:"中"},{label:this.$t("level.low"),value:"低"},{label:this.$t("level.general"),value:"一般"}],columnOption:["cnvdId","cnvdTitle","cnvdLevel","affectedProducts","releaseDate","cnvdDesc"]},dialog:{detailDialog:{visible:!1,title:this.$t("repository.cnvd.dialog.detailTitle"),columnTop:[{key:"cnvdId",label:this.$t("repository.cnvd.table.cnvdId")},{key:"cnvdTitle",label:this.$t("repository.cnvd.table.cnvdTitle")},{key:"cnvdLevel",label:this.$t("repository.cnvd.table.cnvdLevel")},{key:"rollOutFlag",label:this.$t("repository.cnvd.table.rollOutFlag")},{key:"releaseDate",label:this.$t("repository.cnvd.table.releaseDate")},{key:"relateThreat",label:this.$t("repository.cnvd.table.relateThreat")}],columnBottom:[{key:"affectedProducts",label:this.$t("repository.cnvd.table.affectedProducts")},{key:"cnvdDesc",label:this.$t("repository.cnvd.table.cnvdDesc")},{key:"cnvdReference",label:this.$t("repository.cnvd.table.cnvdReference")},{key:"authInfo",label:this.$t("repository.cnvd.table.authInfo")},{key:"cnvdResolve",label:this.$t("repository.cnvd.table.cnvdResolve")},{key:"cnvdProvider",label:this.$t("repository.cnvd.table.cnvdProvider")}],form:{model:{cnvdId:"",cnvdTitle:"",cnvdLevel:"",rollOutFlag:"",releaseDate:"",relateThreat:"",affectedProducts:"",cnvdDesc:"",cnvdReference:"",authInfo:"",cnvdResolve:"",cnvdProvider:""}}}},query:{inputVal:"",seniorQuery:{cnvdId:"",cnvdTitle:"",cnvdLevel:"",releaseDate:""}},show:{seniorQueryShow:!1}}},computed:{levelEnum:function(){return function(e){var t={"严重":"0","高":"1","中":"2","低":"3","一般":"4"};return t[e]}},disableScroll:function(){return this.data.loading}},mounted:function(){this.init()},updated:function(){this.data.table.length>0&&this.$refs.auditTable.doLayout()},methods:{init:function(){this.queryTableData(),this.initDebounce(),this.getTotal()},initDebounce:function(){var e=this;this.data.debounce.query=Object(y["a"])((function(){var t={};t=e.show.seniorQueryShow?Object.assign({},e.query.seniorQuery,{releaseDate:e.query.seniorQuery.releaseDate,pageSize:e.pagination.pageSize}):{pageSize:e.pagination.pageSize,fuzzyField:e.query.inputVal},e.queryTableData(t),e.getTotal(t)}),500),this.data.debounce.resetQueryDebounce=Object(y["a"])((function(){e.query.seniorQuery={cnvdId:"",cnvdTitle:"",cnvdLevel:"",releaseDate:""},e.pagination.pageNum=1,setTimeout((function(){e.queryTableData(),e.getTotal()}),150)}),500)},queryTableData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:this.pagination.pageSize};this.data.loading=!0,this.data.nomore=!1,this.btnRef=!1,g(t).then((function(t){e.data.table=t,e.data.loading=!1,t.length>=20?e.data.nomore=!1:e.data.nomore=!0,e.$nextTick((function(){e.data.table.length>0&&e.$refs.auditTable.doLayout()})),e.btnRef=!0}))},pageQuery:function(e){e&&(this.pagination.pageNum=1),this.data.debounce.query()},clickQueryButton:function(){this.query.inputVal="",this.show.seniorQueryShow=!this.show.seniorQueryShow,this.resetSeniorQuery(),this.initDebounce()},clickUpButton:function(){this.show.seniorQueryShow=!this.show.seniorQueryShow,this.resetSeniorQuery(),this.initDebounce()},clickDetailButton:function(e){var t=this;this.dialog.detailDialog.visible=!0,m(e.cnvdId).then((function(e){t.dialog.detailDialog.form.model=e}))},resetSeniorQuery:function(){this.data.debounce.resetQueryDebounce()},scrollTable:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.data.nomore||(t={pageSize:this.pagination.pageSize,cnvdTitle:this.query.seniorQuery.cnvdTitle,cnvdLevel:this.query.seniorQuery.cnvdLevel,fuzzyField:this.query.inputVal,releaseDate:this.query.seniorQuery.releaseDate.toString(),SerialNumber:this.data.table.length>0?this.data.table[this.data.table.length-1]["cnvdId"]:""},this.data.loading=!0,g(t).then((function(t){var a;t.length<20&&(e.data.nomore=!0),(a=e.data.table).push.apply(a,Object(o["a"])(t)),e.data.loading=!1})))},getTotal:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:20};this.data.totalLoading=!0,w(t).then((function(t){e.data.total=t,e.data.totalLoading=!1}))}}},T=k,Q=Object(h["a"])(T,n,l,!1,null,null,null);t["default"]=Q.exports},2532:function(e,t,a){"use strict";var n=a("23e7"),l=a("5a34"),o=a("1d80"),i=a("ab13");n({target:"String",proto:!0,forced:!i("includes")},{includes:function(e){return!!~String(o(this)).indexOf(l(e),arguments.length>1?arguments[1]:void 0)}})},"5a34":function(e,t,a){var n=a("44e7");e.exports=function(e){if(n(e))throw TypeError("The method doesn't accept regular expressions");return e}},"746c":function(e,t,a){"use strict";var n=a("2b0e"),l=(a("4160"),a("9883")),o=a.n(l),i="ElInfiniteScroll",r="[el-table-infinite-scroll]: ",s=".el-table__body-wrapper";function c(e,t,a){var n,l=e.context;["disabled","delay","immediate"].forEach((function(e){e="infinite-scroll-"+e,n=t.getAttribute(e),null!==n&&a.setAttribute(e,l[n]||n)}));var o="infinite-scroll-distance";n=t.getAttribute(o),n=l[n]||n,a.setAttribute(o,n<1?1:n)}var u={inserted:function(e,t,a,l){var u=e.querySelector(s);u||console.error("".concat(r," 找不到 ").concat(s," 容器")),u.style.overflowY="auto",n["default"].nextTick((function(){e.style.height||(u.style.height="590px"),c(a,e,u),o.a.inserted(u,t,a,l),e[i]=u[i]}))},update:function(e,t,a){c(a,e,e.querySelector(s))},unbind:function(e){e&&e.container&&o.a.unbind(e)}},d=function(e){e.directive("el-table-scroll",u)};window.Vue&&(window["el-table-scroll"]=u,n["default"].use(d)),u.elTableScroll=d;t["a"]=u},ab13:function(e,t,a){var n=a("b622"),l=n("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[l]=!1,"/./"[e](t)}catch(n){}}return!1}},caad:function(e,t,a){"use strict";var n=a("23e7"),l=a("4d64").includes,o=a("44d2"),i=a("ae40"),r=i("indexOf",{ACCESSORS:!0,1:0});n({target:"Array",proto:!0,forced:!r},{includes:function(e){return l(this,e,arguments.length>1?arguments[1]:void 0)}}),o("includes")}}]);