(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-618f87fa"],{"078a":function(e,t,a){"use strict";var n=a("2b0e"),i=(a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319"),{bind:function(e,t,a){var n=[e.querySelector(".el-dialog__header"),e.querySelector(".el-dialog")],i=n[0],o=n[1];i.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();i.onmousedown=function(e){var t=[e.clientX-i.offsetLeft,e.clientY-i.offsetTop,o.offsetWidth,o.offsetHeight,document.body.clientWidth,document.body.clientHeight],n=t[0],l=t[1],s=t[2],c=t[3],u=t[4],d=t[5],p=[o.offsetLeft,u-o.offsetLeft-s,o.offsetTop,d-o.offsetTop-c],f=p[0],h=p[1],g=p[2],b=p[3],m=[r(o,"left"),r(o,"top")],v=m[0],y=m[1];v.includes("%")?(v=+document.body.clientWidth*(+v.replace(/%/g,"")/100),y=+document.body.clientHeight*(+y.replace(/%/g,"")/100)):(v=+v.replace(/px/g,""),y=+y.replace(/px/g,"")),document.onmousemove=function(e){var t=e.clientX-n,i=e.clientY-l;-t>f?t=-f:t>h&&(t=h),-i>g?i=-g:i>b&&(i=b),o.style.cssText+=";left:".concat(t+v,"px;top:").concat(i+y,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),o=function(e){e.directive("el-dialog-drag",i)};window.Vue&&(window["el-dialog-drag"]=i,n["default"].use(o)),i.elDialogDrag=o;t["a"]=i},"212a":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"iframe-wrapper"},[a("iframe",{ref:"frameDOM",style:{width:e.width,height:e.height},attrs:{src:e.frameUrl,frameborder:"0"}})])},i=[],o={props:{width:{type:String,default:"100%"},height:{type:String,default:"100%"},frameUrl:{required:!0,type:String}},data:function(){return{loading:!0}},mounted:function(){this.onload()},methods:{onload:function(){var e=this,t=this.$refs.frameDOM;t.attachEvent?t.attachEvent("onload",(function(){e.loading=!1})):t.onload=function(){e.loading=!1}},reload:function(){this.$refs.frameDOM.contentWindow.location.reload(!0)}}},r=o,l=(a("fde8"),a("2877")),s=Object(l["a"])(r,n,i,!1,null,"6d9f4d83",null),c=s.exports;t["a"]=c},2532:function(e,t,a){"use strict";var n=a("23e7"),i=a("5a34"),o=a("1d80"),r=a("ab13");n({target:"String",proto:!0,forced:!r("includes")},{includes:function(e){return!!~String(o(this)).indexOf(i(e),arguments.length>1?arguments[1]:void 0)}})},"5a34":function(e,t,a){var n=a("44e7");e.exports=function(e){if(n(e))throw TypeError("The method doesn't accept regular expressions");return e}},"5e19":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("header",{staticClass:"table-header"},[a("section",{staticClass:"table-header-main"},[a("section",{staticClass:"table-header-search"},[a("section",{staticClass:"table-header-search-input"},[a("el-input",{attrs:{"prefix-icon":"soc-icon-search",clearable:"",placeholder:e.$t("tip.placeholder.query",[e.$t("report.instance.table.name")])},on:{change:e.clickQueryReportInstanceTable},model:{value:e.data.fuzzyField,callback:function(t){e.$set(e.data,"fuzzyField",t)},expression:"data.fuzzyField"}})],1),a("section",{staticClass:"table-header-search-button"},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.clickQueryReportInstanceTable}},[e._v(" "+e._s(e.$t("button.query"))+" ")])],1)])])]),a("main",{staticClass:"table-body"},[a("header",{staticClass:"table-body-header"},[a("h2",{staticClass:"table-body-title"},[e._v(" "+e._s(e.$t("report.instance.name"))+" ")])]),a("main",{staticClass:"table-body-main"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.data.loading,expression:"data.loading"}],ref:"reportInstanceTable",attrs:{data:e.data.table,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"current-change":e.reportInstanceTableRowChange,"selection-change":e.reportInstanceTableSelectsChange}},[a("el-table-column",{attrs:{type:"expand","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.taskInstance.length>0?a("ul",{staticClass:"instance-detail-wrapper"},e._l(t.row.taskInstance,(function(n,i){return a("li",{key:i},[a("section",[e._v(e._s(n.instanceTime))]),a("section",[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],staticClass:"el-button--blue",on:{click:function(t){return e.clickLookReportInstance(n)}}},[e._v(" "+e._s(e.$t("button.look"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticClass:"el-button--red",on:{click:function(a){return e.clickDeleteReportInstance(t.row,n)}}},[e._v(" "+e._s(e.$t("button.delete"))+" ")])],1)])})),0):a("p",[e._v(" "+e._s(e.$t("tip.data.empty"))+" ")])]}}])}),a("el-table-column",{attrs:{label:e.$t("report.instance.table.name"),prop:"taskName","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:e.$t("report.instance.table.type"),prop:"taskType","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:e.$t("report.instance.table.amount"),prop:"taskAmount","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{width:"113",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticClass:"el-button--red",on:{click:function(a){return e.clickClearReportInstance(t.row)}}},[e._v(" "+e._s(e.$t("button.clear"))+" ")])]}}])})],1)],1)]),a("footer",{staticClass:"table-footer"},[a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.pagination.pageNum,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.reportInstanceTableSizeChange,"current-change":e.reportInstanceTableCurrentChange}})],1),a("preview-dialog",{attrs:{visible:e.dialog.visible,title:e.dialog.title,width:"70%",url:e.dialog.url},on:{"update:visible":function(t){return e.$set(e.dialog,"visible",t)}}})],1)},i=[],o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("custom-dialog",{ref:"dialogTemp",attrs:{visible:e.visible,title:e.title,width:e.width},on:{"on-close":e.clickCancelDialog}},[a("html-frame",{attrs:{"frame-url":e.url,height:"400px"}}),a("template",{slot:"action"},[a("fragment")],1)],2)},r=[],l=a("d465"),s=a("212a"),c={components:{CustomDialog:l["a"],HtmlFrame:s["a"]},props:{visible:{required:!0,type:Boolean},title:{type:String,default:""},width:{type:String,default:"600"},url:{type:String,default:""}},data:function(){return{dialogVisible:this.visible}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit("update:visible",e)}},methods:{clickCancelDialog:function(){this.$refs.dialogTemp.end(),this.dialogVisible=!1}}},u=c,d=a("2877"),p=Object(d["a"])(u,o,r,!1,null,null,null),f=p.exports,h=a("f7b5"),g=a("13c3"),b=(a("99af"),a("4020"));function m(e,t){return Object(b["a"])({url:"/reportmanagement/instance/".concat(e,"/").concat(t),method:"delete"})}function v(e){return Object(b["a"])({url:"/reportmanagement/instance",method:"get",params:e||{}})}var y={name:"ReportInstance",components:{PreviewDialog:f},data:function(){return{data:{loading:!1,debounce:null,table:[],selected:[],fuzzyField:""},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,currentRow:{}},dialog:{visible:!1,title:this.$t("report.instance.dialog.title"),url:""}}},mounted:function(){this.initDebounceQuery(),this.getReportInstanceTableData()},methods:{clickQueryReportInstanceTable:function(){this.data.debounce()},clickClearReportInstance:function(e){this.deleteReportInstance(e.taskId)},clickLookReportInstance:function(e){var t="";this.dialog.url=t+e.instanceUrl,this.dialog.visible=!0},clickDeleteReportInstance:function(e,t){this.deleteReportInstance(e.taskId,t.instanceId)},reportInstanceTableRowChange:function(e){this.pagination.currentRow=e},reportInstanceTableSelectsChange:function(e){this.data.selected=e},reportInstanceTableSizeChange:function(e){this.pagination.pageSize=e,this.getReportInstanceTableData()},reportInstanceTableCurrentChange:function(e){this.pagination.pageNum=e,this.getReportInstanceTableData()},initDebounceQuery:function(){var e=this;this.data.debounce=Object(g["a"])((function(){e.pagination.pageNum=1,e.getReportInstanceTableData()}),500)},deleteReportInstance:function(e,t){var a=this;t||(t=-1),this.$confirm(t?this.$t("tip.confirm.delete"):this.$t("tip.confirm.clear"),this.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){m(e,t).then((function(e){e?Object(h["a"])({i18nCode:"tip.delete.success",type:"success"},(function(){a.getReportInstanceTableData()})):Object(h["a"])({i18nCode:"tip.delete.error",type:"error"})}))}))},getReportInstanceTableData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{fuzzyField:this.data.fuzzyField,pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.pagination.visible=!1,this.data.loading=!0,v(t).then((function(t){e.data.table=t.rows,e.pagination.total=t.total,e.pagination.visible=!0,e.data.loading=!1}))}}},w=y,C=(a("c9fa"),Object(d["a"])(w,n,i,!1,null,"5e44ecf3",null));t["default"]=C.exports},"606a":function(e,t,a){},"807e":function(e,t,a){},ab13:function(e,t,a){var n=a("b622"),i=n("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[i]=!1,"/./"[e](t)}catch(n){}}return!1}},c9fa:function(e,t,a){"use strict";var n=a("606a"),i=a.n(n);i.a},caad:function(e,t,a){"use strict";var n=a("23e7"),i=a("4d64").includes,o=a("44d2"),r=a("ae40"),l=r("indexOf",{ACCESSORS:!0,1:0});n({target:"Array",proto:!0,forced:!l},{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),o("includes")},fde8:function(e,t,a){"use strict";var n=a("807e"),i=a.n(n);i.a}}]);