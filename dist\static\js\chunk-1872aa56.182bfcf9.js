(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1872aa56"],{"1f93":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"i",(function(){return o})),n.d(t,"g",(function(){return l})),n.d(t,"c",(function(){return r})),n.d(t,"f",(function(){return c})),n.d(t,"h",(function(){return s})),n.d(t,"n",(function(){return u})),n.d(t,"m",(function(){return p})),n.d(t,"k",(function(){return f})),n.d(t,"l",(function(){return d})),n.d(t,"b",(function(){return m})),n.d(t,"o",(function(){return h})),n.d(t,"j",(function(){return b})),n.d(t,"e",(function(){return g})),n.d(t,"d",(function(){return v}));var i=n("4020");function a(e){return Object(i["a"])({url:"/event/original/accessControlLog",method:"get",params:e||{}})}function o(e){return Object(i["a"])({url:"/event/original/networkOperationLog",method:"get",params:e||{}})}function l(e){return Object(i["a"])({url:"/event/original/industrialControlOperationLog",method:"get",params:e||{}})}function r(e){return Object(i["a"])({url:"/event/original/fileTransferLog",method:"get",params:e||{}})}function c(e){return Object(i["a"])({url:"/event/original/industrialControlFileTransferLog",method:"get",params:e||{}})}function s(e){return Object(i["a"])({url:"/event/original/kvmOperationLog",method:"get",params:e||{}})}function u(e){return Object(i["a"])({url:"/event/original/udiskWebTransmission",method:"get",params:e||{}})}function p(e){return Object(i["a"])({url:"/event/original/udiskWebMapTransmission",method:"get",params:e||{}})}function f(e){return Object(i["a"])({url:"/event/original/serialPort",method:"get",params:e||{}})}function d(e){return Object(i["a"])({url:"/event/original/serialPortConsole",method:"get",params:e||{}})}function m(e){return Object(i["a"])({url:"/event/original/downFile",method:"get",params:e||{}},"download")}function h(e){return Object(i["a"])({url:"/event/serialport/combo/workmode",method:"get",params:e||{}})}function b(e){return Object(i["a"])({url:"/event/original/getProtocols",method:"get",params:e||{}})}function g(e){return Object(i["a"])({url:"/event/original/getVideoUrl",method:"get",params:e||{}})}function v(){return Object(i["a"])({url:"/platform/all",method:"get"})}},"269b":function(e,t,n){"use strict";var i=n("fa98"),a=n.n(i);a.a},3648:function(e,t,n){},"483d":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-select",{staticClass:"platform",staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"来源平台"},on:{change:e.handleChange},model:{value:e.platformValue.domainToken,callback:function(t){e.$set(e.platformValue,"domainToken",t)},expression:"platformValue.domainToken"}},e._l(e.platformOption,(function(e,t){return n("el-option",{key:t,attrs:{label:e.platformName,value:e.domainToken}})})),1)},a=[],o=n("1f93"),l={props:{platformValue:{required:!0,type:Object}},data:function(){return{platformOption:[]}},mounted:function(){var e=this;Object(o["d"])().then((function(t){e.platformOption=t}))},methods:{handleChange:function(){this.$emit("change",this.platformValue)}}},r=l,c=n("2877"),s=Object(c["a"])(r,i,a,!1,null,"7b618a7a",null);t["a"]=s.exports},a42c:function(e,t,n){"use strict";var i=n("3648"),a=n.n(i);a.a},baa0:function(e,t,n){},fa98:function(e,t,n){},fec0:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.clickTabSwitch},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},[n("el-tab-pane",{attrs:{label:"U盘web传输审计",name:"1"}},[1==e.tabName?n("UWeb"):e._e()],1),n("el-tab-pane",{attrs:{label:"U盘映射传输审计",name:"2"}},[2==e.tabName?n("UMapping"):e._e()],1)],1)],1)},a=[],o=(n("b0c0"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"router-wrap-table"},[n("table-header",{attrs:{condition:e.query},on:{"update:condition":function(t){e.query=t},"on-change":e.changeQueryTable}}),n("table-body",{attrs:{"title-name":e.title,"table-loading":e.table.loading,"table-data":e.table.data}}),n("table-footer",{attrs:{pagination:e.pagination},on:{"update:pagination":function(t){e.pagination=t},"size-change":e.tableSizeChange,"page-change":e.tablePageChange}})],1)}),l=[],r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("header",{staticClass:"table-header"},[n("section",{staticClass:"table-header-main"},[n("section",{staticClass:"table-header-search"},[n("section",{staticClass:"table-header-search-button"},[n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],attrs:{type:"primary"},on:{click:e.clickExactQuery}},[e._v(" 高级查询 "),n("i",{staticClass:"el-icon--right",class:e.filterCondition.senior?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),n("section",{staticClass:"table-header-button"})]),n("section",{staticClass:"table-header-extend"},[n("el-collapse-transition",[n("div",{directives:[{name:"show",rawName:"v-show",value:e.filterCondition.senior,expression:"filterCondition.senior"}],staticClass:"table-header-query"},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"访问IP"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.accessIp,callback:function(t){e.$set(e.filterCondition.form,"accessIp",t)},expression:"filterCondition.form.accessIp"}})],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"文件名"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.fileName,callback:function(t){e.$set(e.filterCondition.form,"fileName",t)},expression:"filterCondition.form.fileName"}})],1),n("el-col",{attrs:{span:6}},[n("el-select",{attrs:{clearable:"",placeholder:"检出病毒"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.virusName,callback:function(t){e.$set(e.filterCondition.form,"virusName",t)},expression:"filterCondition.form.virusName"}},e._l(e.virusNameOption,(function(e,t){return n("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-col",{attrs:{span:6}},[n("el-select",{attrs:{clearable:"",placeholder:"方向"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.transferDirection,callback:function(t){e.$set(e.filterCondition.form,"transferDirection",t)},expression:"filterCondition.form.transferDirection"}},e._l(e.transferDirectionOption,(function(e,t){return n("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"审批人"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.auditAccount,callback:function(t){e.$set(e.filterCondition.form,"auditAccount",t)},expression:"filterCondition.form.auditAccount"}})],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"运维人员"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.operatorAccount,callback:function(t){e.$set(e.filterCondition.form,"operatorAccount",t)},expression:"filterCondition.form.operatorAccount"}})],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"工作票号"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.workTicketNumber,callback:function(t){e.$set(e.filterCondition.form,"workTicketNumber",t)},expression:"filterCondition.form.workTicketNumber"}})],1),n("el-col",{attrs:{span:6}},[n("el-date-picker",{attrs:{clearable:"",type:"datetimerange","value-format":"yyyy-MM-dd HH:mm:ss","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:e.changeQueryCondition},model:{value:e.time,callback:function(t){e.time=t},expression:"time"}})],1),n("el-col",{attrs:{span:6}},[n("PlatformSelect",{attrs:{platformValue:e.filterCondition.form},on:{"update:platformValue":function(t){return e.$set(e.filterCondition,"form",t)},"update:platform-value":function(t){return e.$set(e.filterCondition,"form",t)},change:e.changeQueryCondition}})],1),n("el-col",{attrs:{span:18,align:"right"}},[n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.changeQueryCondition}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")]),n("el-button",{ref:"shrinkButton",on:{click:e.clickUpButton}},[n("i",{staticClass:"soc-icon-scroller-top-all"})])],1)],1)],1)])],1)])},c=[],s=n("13c3"),u=n("483d"),p={props:{condition:{required:!0,type:Object}},components:{PlatformSelect:u["a"]},data:function(){return{filterCondition:this.condition,debounce:null,time:"",virusNameOption:[{label:"是",value:1},{label:"否",value:0}],transferDirectionOption:[{label:"下载至U盘",value:"下载至U盘"},{label:"上传至服务器",value:"上传至服务器"}]}},watch:{condition:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:condition",e)}},mounted:function(){this.initDebounceQuery()},methods:{initDebounceQuery:function(){var e=this;this.debounce=Object(s["a"])((function(){e.$emit("on-change")}),400)},changeQueryCondition:function(){this.filterCondition.form.startTime=this.time?this.time[0]:"",this.filterCondition.form.endTime=this.time?this.time[1]:"",this.debounce()},clickExactQuery:function(){this.filterCondition.senior=!this.filterCondition.senior,this.resetQuery()},clickUpButton:function(){this.filterCondition.senior=!1,this.resetQuery()},resetQuery:function(){this.time="",this.filterCondition.form={accessIp:"",fileName:"",virusName:"",usbDevice:"",transferDirection:"",auditAccount:"",operatorAccount:"",workTicketNumber:"",domainToken:""},this.changeQueryCondition()},clickAdd:function(){this.$emit("on-add")},clickBatchDelete:function(){this.$emit("on-batch-delete")}}},f=p,d=(n("a42c"),n("2877")),m=Object(d["a"])(f,r,c,!1,null,"4375c4dc",null),h=m.exports,b=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("main",{staticClass:"table-body"},[n("main",{staticClass:"table-body-main"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"calc(100% + 50px)"}},[n("el-table-column",{attrs:{width:"80",type:"index",label:"序号",align:"center"}}),n("el-table-column",{attrs:{prop:"time",label:"时间","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"accessIp",label:"访问IP","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"transferDirection",label:"方向","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"filePath",label:"文件路径","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"fileName",label:"文件名","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"fileType",label:"文件类型","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"fileSize",label:"文件大小","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"virusCount",label:"病毒数量","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"virusName",label:"病毒名称","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"backupPath",label:"备份路径","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"sourceDevice",label:"来源设备","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"domainName",label:"来源平台","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"workTicketNumber",label:"工作票号","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"operatorAccount",label:"运维人员","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"auditAccount",label:"审批人","show-overflow-tooltip":""}})],1)],1)])},g=[],v={props:{titleName:{required:!0,type:String},tableLoading:{required:!0,type:Boolean},tableData:{required:!0,type:Array}},methods:{clickSelectRows:function(e){this.$emit("on-select",e)},clickDetail:function(e){this.$emit("on-detail",e)},clickUpdate:function(e){this.$emit("on-update",e)},clickDelete:function(e){this.$emit("on-delete",e)}}},C=v,y=Object(d["a"])(C,b,g,!1,null,null,null),w=y.exports,k=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"table-footer"},[e.filterCondition.visible?n("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.filterCondition.pageNum,"page-sizes":[10,20,50,100],"page-size":e.filterCondition.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filterCondition.total},on:{"size-change":e.clickSize,"current-change":e.clickPage}}):e._e()],1)},N=[],$={props:{pagination:{required:!0,type:Object}},data:function(){return{filterCondition:this.pagination}},watch:{pagination:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:pagination",e)}},methods:{clickSize:function(e){this.$emit("size-change",e)},clickPage:function(e){this.$emit("page-change",e)}}},T=$,O=Object(d["a"])(T,k,N,!1,null,null,null),Q=O.exports,x=n("1f93"),q={components:{TableHeader:h,TableBody:w,TableFooter:Q},data:function(){return{title:"",query:{senior:!1,form:{accessIp:"",fileName:"",virusName:"",usbDevice:"",transferDirection:"",auditAccount:"",operatorAccount:"",workTicketNumber:"",startTime:"",endTime:"",domainToken:""}},table:{loading:!1,data:[]},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0}}},mounted:function(){this.queryTableData()},methods:{changeQueryTable:function(e){"turn-page"!==e&&(this.pagination.pageNum=1);var t=this.handleQueryParams();this.queryTableData(t)},handleQueryParams:function(){var e={};return this.query.senior&&(e=Object.assign(e,this.query.form)),e},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.changeQueryTable()},tablePageChange:function(e){this.pagination.pageNum=e,this.changeQueryTable("turn-page")},queryTableData:function(e){var t=this;e=Object.assign({},e,{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum}),this.table.loading=!0,this.pagination.visible=!1,Object(x["n"])(e).then((function(e){e&&(t.table.data=e.rows,t.pagination.total=e.total,t.pagination.pageNum=e.pageNum,t.pagination.pageSize=e.pageSize),t.table.loading=!1,t.pagination.visible=!0}))}}},j=q,D=Object(d["a"])(j,o,l,!1,null,null,null),z=D.exports,_=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"router-wrap-table"},[n("table-header",{attrs:{condition:e.query},on:{"update:condition":function(t){e.query=t},"on-change":e.changeQueryTable}}),n("table-body",{attrs:{"title-name":e.title,"table-loading":e.table.loading,"table-data":e.table.data}}),n("table-footer",{attrs:{pagination:e.pagination},on:{"update:pagination":function(t){e.pagination=t},"size-change":e.tableSizeChange,"page-change":e.tablePageChange}})],1)},S=[],A=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("header",{staticClass:"table-header"},[n("section",{staticClass:"table-header-main"},[n("section",{staticClass:"table-header-search"},[n("section",{staticClass:"table-header-search-button"},[n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],attrs:{type:"primary"},on:{click:e.clickExactQuery}},[e._v(" 高级查询 "),n("i",{staticClass:"el-icon--right",class:e.filterCondition.senior?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),n("section",{staticClass:"table-header-button"})]),n("section",{staticClass:"table-header-extend"},[n("el-collapse-transition",[n("div",{directives:[{name:"show",rawName:"v-show",value:e.filterCondition.senior,expression:"filterCondition.senior"}],staticClass:"table-header-query"},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"访问IP"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.accessIp,callback:function(t){e.$set(e.filterCondition.form,"accessIp",t)},expression:"filterCondition.form.accessIp"}})],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"文件名"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.fileName,callback:function(t){e.$set(e.filterCondition.form,"fileName",t)},expression:"filterCondition.form.fileName"}})],1),n("el-col",{attrs:{span:6}},[n("el-select",{attrs:{clearable:"",placeholder:"检出病毒"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.virusName,callback:function(t){e.$set(e.filterCondition.form,"virusName",t)},expression:"filterCondition.form.virusName"}},e._l(e.virusNameOption,(function(e,t){return n("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-col",{attrs:{span:6}},[n("el-select",{attrs:{clearable:"",placeholder:"方向"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.transferDirection,callback:function(t){e.$set(e.filterCondition.form,"transferDirection",t)},expression:"filterCondition.form.transferDirection"}},e._l(e.transferDirectionOption,(function(e,t){return n("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"审批人"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.auditAccount,callback:function(t){e.$set(e.filterCondition.form,"auditAccount",t)},expression:"filterCondition.form.auditAccount"}})],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"运维人员"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.operatorAccount,callback:function(t){e.$set(e.filterCondition.form,"operatorAccount",t)},expression:"filterCondition.form.operatorAccount"}})],1),n("el-col",{attrs:{span:6}},[n("el-input",{attrs:{clearable:"",placeholder:"工作票号"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.workTicketNumber,callback:function(t){e.$set(e.filterCondition.form,"workTicketNumber",t)},expression:"filterCondition.form.workTicketNumber"}})],1),n("el-col",{attrs:{span:6}},[n("el-date-picker",{attrs:{clearable:"",type:"datetimerange","value-format":"yyyy-MM-dd HH:mm:ss","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:e.changeQueryCondition},model:{value:e.time,callback:function(t){e.time=t},expression:"time"}})],1),n("el-col",{attrs:{span:6}},[n("PlatformSelect",{attrs:{platformValue:e.filterCondition.form},on:{"update:platformValue":function(t){return e.$set(e.filterCondition,"form",t)},"update:platform-value":function(t){return e.$set(e.filterCondition,"form",t)},change:e.changeQueryCondition}})],1),n("el-col",{attrs:{span:18,align:"right"}},[n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.changeQueryCondition}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")]),n("el-button",{ref:"shrinkButton",on:{click:e.clickUpButton}},[n("i",{staticClass:"soc-icon-scroller-top-all"})])],1)],1)],1)])],1)])},P=[],U={props:{condition:{required:!0,type:Object}},components:{PlatformSelect:u["a"]},data:function(){return{filterCondition:this.condition,debounce:null,time:"",virusNameOption:[{label:"是",value:1},{label:"否",value:0}],transferDirectionOption:[{label:"下载至U盘",value:"下载至U盘"},{label:"上传至服务器",value:"上传至服务器"}]}},watch:{condition:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:condition",e)}},mounted:function(){this.initDebounceQuery()},methods:{initDebounceQuery:function(){var e=this;this.debounce=Object(s["a"])((function(){e.$emit("on-change")}),400)},changeQueryCondition:function(){this.filterCondition.form.startTime=this.time?this.time[0]:"",this.filterCondition.form.endTime=this.time?this.time[1]:"",this.debounce()},clickExactQuery:function(){this.filterCondition.senior=!this.filterCondition.senior,this.resetQuery()},clickUpButton:function(){this.filterCondition.senior=!1,this.resetQuery()},resetQuery:function(){this.time="",this.filterCondition.form={accessIp:"",fileName:"",virusName:"",usbDevice:"",transferDirection:"",auditAccount:"",operatorAccount:"",workTicketNumber:"",domainToken:""},this.changeQueryCondition()},clickAdd:function(){this.$emit("on-add")},clickBatchDelete:function(){this.$emit("on-batch-delete")}}},I=U,E=(n("269b"),Object(d["a"])(I,A,P,!1,null,"49a59538",null)),B=E.exports,L=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("main",{staticClass:"table-body"},[n("main",{staticClass:"table-body-main"},[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"calc(100% + 50px)"}},[n("el-table-column",{attrs:{width:"80",type:"index",label:"序号",align:"center"}}),n("el-table-column",{attrs:{prop:"time",label:"时间","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"accessIp",label:"访问IP","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"transferDirection",label:"方向","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"filePath",label:"文件路径","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"fileName",label:"文件名","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"fileType",label:"文件类型","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"fileSize",label:"文件大小","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"virusCount",label:"病毒数量","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"virusName",label:"病毒名称","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"backupPath",label:"备份路径","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"sourceDevice",label:"来源设备","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"domainName",label:"来源平台","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"workTicketNumber",label:"工作票号","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"operatorAccount",label:"运维人员","show-overflow-tooltip":""}}),n("el-table-column",{attrs:{prop:"auditAccount",label:"审批人","show-overflow-tooltip":""}})],1)],1)])},V=[],M={props:{titleName:{required:!0,type:String},tableLoading:{required:!0,type:Boolean},tableData:{required:!0,type:Array}},methods:{clickSelectRows:function(e){this.$emit("on-select",e)},clickDetail:function(e){this.$emit("on-detail",e)},clickUpdate:function(e){this.$emit("on-update",e)},clickDelete:function(e){this.$emit("on-delete",e)}}},H=M,F=Object(d["a"])(H,L,V,!1,null,null,null),W=F.exports,J=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"table-footer"},[e.filterCondition.visible?n("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.filterCondition.pageNum,"page-sizes":[10,20,50,100],"page-size":e.filterCondition.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filterCondition.total},on:{"size-change":e.clickSize,"current-change":e.clickPage}}):e._e()],1)},R=[],G={props:{pagination:{required:!0,type:Object}},data:function(){return{filterCondition:this.pagination}},watch:{pagination:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:pagination",e)}},methods:{clickSize:function(e){this.$emit("size-change",e)},clickPage:function(e){this.$emit("page-change",e)}}},K=G,X=Object(d["a"])(K,J,R,!1,null,null,null),Y=X.exports,Z={components:{TableHeader:B,TableBody:W,TableFooter:Y},data:function(){return{title:"",query:{senior:!1,form:{accessIp:"",fileName:"",virusName:"",usbDevice:"",transferDirection:"",auditAccount:"",operatorAccount:"",workTicketNumber:"",startTime:"",endTime:"",domainToken:""}},table:{loading:!1,data:[]},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0}}},mounted:function(){this.queryTableData()},methods:{changeQueryTable:function(e){"turn-page"!==e&&(this.pagination.pageNum=1);var t=this.handleQueryParams();this.queryTableData(t)},handleQueryParams:function(){var e={};return this.query.senior&&(e=Object.assign(e,this.query.form)),e},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.changeQueryTable()},tablePageChange:function(e){this.pagination.pageNum=e,this.changeQueryTable("turn-page")},queryTableData:function(e){var t=this;e=Object.assign({},e,{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum}),this.table.loading=!0,this.pagination.visible=!1,Object(x["m"])(e).then((function(e){e&&(t.table.data=e.rows,t.pagination.total=e.total,t.pagination.pageNum=e.pageNum,t.pagination.pageSize=e.pageSize),t.table.loading=!1,t.pagination.visible=!0}))}}},ee=Z,te=Object(d["a"])(ee,_,S,!1,null,null,null),ne=te.exports,ie={components:{UWeb:z,UMapping:ne},data:function(){return{tabName:"1"}},methods:{clickTabSwitch:function(e){this.tabName=e.name}}},ae=ie,oe=(n("ff98"),Object(d["a"])(ae,i,a,!1,null,"36771ad4",null));t["default"]=oe.exports},ff98:function(e,t,n){"use strict";var i=n("baa0"),a=n.n(i);a.a}}]);