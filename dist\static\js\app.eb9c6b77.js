(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["app"],{0:function(e,t,n){e.exports=n("56d7")},"0378":function(e,t,n){"use strict";n.r(t),t["default"]={validate:{upload:{empty:"请上传文件",license:"只能上传lic或license文件",excel:"只能上传xlsx或xls文件",word:"只能上传doc或docx文件",ppt:"只能上传ppt或pptx文件",pdf:"只能上传pdf文件",sql:"只能上传sql文件"},data:{empty:"暂无数据",nomore:"没有更多了..."},empty:"此项不能为空",length:{space:"输入的内容不能含有空格",maxTen:"输入内容的长度不能大于10个字符"},comm:{url:"您输入的URL不合法",email:"您输入的邮箱不合法",cellphone:"您输入的移动电话号码不合法",telephone:"您输入的固定电话号码不合法",port:"您输入的端口号不合法",pwd:"只允许输入字母、数字和符号"},mac:{incorrect:"该MAC地址不合法",empty:"MAC不能为空"},ip:{compare:"终止IP应大于起始IP",incorrect:"IP地址不合法",segment:"IP段不同",range:"端口格式不正确，只能输入1~65535之间的整数",domain:"输入项IP或域名不合法",empty:"IP不能为空",error:"请输入同一类型的合法IP!",checkRange:"检测到输入项存在非法IP，或者IP范围类型不统一",rangeExist:"起始IP、终止IP必须同时存在"},port:{incorrect:"端口格式不合法"},address:{incorrect:"地址格式不合法"},form:{empty:"{0}不能为空",error:"表单校验失败",warning:"表单校验未通过",lessOne:"请至少选择一项"},date:{compare:"终止时间应该大于起始时间"},name:{empty:"用户名不能为空",chinese:"只能输入汉字",english:"只允许输入字母“_”“-”“ ”，且“_”“-”“ ”只能位于中间位置",rule:"只允许输入字母、数字和“_”“-”“.”符号，且“_”“-”“.”只能位于中间位置",level0:"只允许输入字母、汉字、数字和“_”“-”符号，且“_”“-”只能位于中间位置",level1:"只允许输入字母、汉字、数字和“_”“-”“.”符号，且“_”“-”“.”只能位于中间位置",level2:"只允许输入字母、汉字、数字和“_”“-”“.”“/”符号，且“_”“-”“.”“/”只能位于中间位置",level3:"只允许输入字母、汉字、数字和“_”“-”“.”“/”“ ”符号，且“_”“-”“.”“/”“ ”只能位于中间位置"},username:{empty:"用户名不能为空",rule:"只允许输入字母、数字和“_”“-”“.”符号，且“_”“-”“.”只能位于中间位置"},account:{empty:"账号不能为空"},password:{empty:"输入密码不能为空",rule:"密码至少分别包括一个大写字母小写字母数字和特殊字符",size:"密码位数不得少于8位，不多于20位",old:{empty:"请输入旧密码"},new:{empty:"请输入新密码",compare:"新密码不能与旧密码相同"},confirm:{empty:"请确认新密码",compare:"两次输入的密码不一致"}},captcha:{empty:"校验码不能为空",rule:"只允许输入字母和数字"},choose:"请先选择后再提交",onlyNumber:"只能输入数字",none:"此项不能为空或包含空格",number:{compare:"终止数字应该大于起始数字"},nameInput:{rule:"只允许输入汉字、字母、数字、“_”“-”“.”符号，且“_”“-”和“.”只能位于中间位置。"},telephone:"输入的电话格式不合法",email:{empty:"邮箱不能为空",incorrect:"输入的邮箱不合法"},faxNo:"输入的传真号不合法",monitor:{pollDate:"请输入3-60之间的数值",useRate:"请输入1-100之间的数值",times:"请输入1-9之间的数值",port:"端口格式不正确，只能输入1~65535之间的整数。"},chooseItem:{empty:"选择的信息项不能为空"},filterCondition:"至少选择一个采集器过滤条件",actualLog:{existAttention:"该内容存在于关注项中，请解除关注后再忽略。"}}}},"048b":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"VisualizationDashboard",path:"/visualization/dashboard",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-756e8394")]).then(n.bind(null,"4521"))}},{name:"VisualizationManagement",path:"/visualization/management",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-5b6903b6")]).then(n.bind(null,"8523"))}},{name:"ComplianceAudit",path:"/visualization/compliance-audit",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-65a65b49")]).then(n.bind(null,"63cf"))}},{name:"ActualTimeLogMonitor",path:"/visualization/actual-time-log-monitor",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-6ffb7473")]).then(n.bind(null,"8931"))}},{name:"overview",path:"/visualization/overview",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-20134cd2")]).then(n.bind(null,"b295"))}}]},"076b":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{path:"/asset/host-guardian-system-overview",name:"HostGuardianManagement",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-3e856590"),n.e("chunk-5c888fbf")]).then(n.bind(null,"862e"))},meta:{title:"主机卫士管理",requiresAuth:!0}},{path:"/asset/host-guardian-system-status",name:"hostGuardian-systemStatus",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-3e856590"),n.e("chunk-55564c2b")]).then(n.bind(null,"8c3b"))},meta:{title:"系统状态",requiresAuth:!0}},{path:"/asset/host-guardian-system-log",name:"hostGuardian-systemLog",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-625cecf8")]).then(n.bind(null,"68c3"))},meta:{title:"系统日志",requiresAuth:!0}},{path:"/asset/host-guardian-backup-recovery",name:"hostGuardian-backupRecovery",component:function(){return n.e("chunk-72fc193a").then(n.bind(null,"932f"))},meta:{title:"备份恢复",requiresAuth:!0}},{path:"/asset/host-guardian-terminal-list",name:"terminalManagement2-terminalList",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-75fd107c"),n.e("chunk-341bd5cc")]).then(n.bind(null,"92bf"))},meta:{title:"终端列表",requiresAuth:!0}},{path:"/asset/host-guardian-base-strategy",name:"hostGuardian-baseStrategy",component:function(){return Promise.all([n.e("chunk-9e9c9b90"),n.e("chunk-75fd107c"),n.e("chunk-9da964da")]).then(n.bind(null,"16f0"))},meta:{title:"安全策略",requiresAuth:!0}},{path:"/asset/host-guardian-job-management",name:"hostGuardian-jobManagement",component:function(){return Promise.all([n.e("chunk-a806134c"),n.e("chunk-3868608a")]).then(n.bind(null,"6757"))},meta:{title:"任务监控",requiresAuth:!0}},{path:"/asset/host-guardian-terminal-operation-audit",name:"hostGuardian-terminalOperationAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-ea74f3dc")]).then(n.bind(null,"97e4"))},meta:{title:"终端操作审计",requiresAuth:!0}},{path:"/asset/host-guardian-whitelist-audit",name:"hostGuardian-whitelistAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-d446122c")]).then(n.bind(null,"00a0"))},meta:{title:"白名单审计",requiresAuth:!0}},{path:"/asset/host-guardian-external-link-audit",name:"hostGuardian-externalLinkAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-3f36ea4b")]).then(n.bind(null,"c880"))},meta:{title:"违规外联审计",requiresAuth:!0}},{path:"/asset/host-guardian-document-protection-audit",name:"hostGuardian-documentProtectionAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-7614e036")]).then(n.bind(null,"4a78"))},meta:{title:"文件防护审计",requiresAuth:!0}},{path:"/asset/host-guardian-registry-protection-audit",name:"hostGuardian-registryProtectionAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-66ca3c87")]).then(n.bind(null,"2df2"))},meta:{title:"注册表防护审计",requiresAuth:!0}},{path:"/asset/host-guardian-network-protection-audit",name:"hostGuardian-networkProtectionAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-b6420960")]).then(n.bind(null,"6ab4"))},meta:{title:"网络防护审计",requiresAuth:!0}},{path:"/asset/host-guardian-process-safe-audit",name:"hostGuardian-processSafeAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-5f87ffbe")]).then(n.bind(null,"5b20"))},meta:{title:"进程防护审计",requiresAuth:!0}},{path:"/asset/host-guardian-peripheral-control-audit",name:"hostGuardian-peripheralControlAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-6dceb9d0")]).then(n.bind(null,"1625"))},meta:{title:"外设管控审计",requiresAuth:!0}},{path:"/asset/host-guardian-host-health-audit",name:"hostGuardian-hostHealthAudit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a806134c"),n.e("chunk-9aafca22")]).then(n.bind(null,"1ce4"))},meta:{title:"主机健康审计",requiresAuth:!0}},{path:"/asset/host-guardian-system-user",name:"hostGuardian-systemuser",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-97520b06")]).then(n.bind(null,"84f0"))},meta:{title:"用户管理",requiresAuth:!0}},{path:"/asset/host-guardian-system-log-setting",name:"hostGuardian-systemlogsetting",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-61b21f26")]).then(n.bind(null,"0545"))},meta:{title:"日志设置",requiresAuth:!0}},{path:"/asset/host-guardian-system-backup",name:"hostGuardian-systembackup",component:function(){return n.e("chunk-72fc193a").then(n.bind(null,"932f"))},meta:{title:"备份恢复",requiresAuth:!0}},{path:"/asset/host-guardian-system-log",name:"hostGuardian-systemlog",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-625cecf8")]).then(n.bind(null,"68c3"))},meta:{title:"系统日志",requiresAuth:!0}},{path:"/asset/host-guardian-system-setting",name:"hostGuardian-systemsetting",component:function(){return n.e("chunk-6ead155e").then(n.bind(null,"511b"))},meta:{title:"系统设置",requiresAuth:!0}},{path:"/asset/host-guardian-system-status",name:"hostGuardian-systemstatus",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-3e856590"),n.e("chunk-55564c2b")]).then(n.bind(null,"8c3b"))},meta:{title:"系统状态",requiresAuth:!0}}]},"09ea":function(e,t,n){n("99af"),n("4de4"),n("a630"),n("a15b"),n("fb6a"),n("a9e3"),n("c35a"),n("d3b7"),n("4d63"),n("ac1f"),n("25f0"),n("6062"),n("3ca3"),n("466d"),n("5319"),n("1276"),n("498a"),n("ddb0");var a=n("26267"),r=n("62f5");Object.assign(HTMLElement.prototype,{getFirstChild:function(){if(this.firstElementChild)return this.firstElementChild;var e=this.firstChild;while(e&&1!==e.nodeType)e=e.nextSibling;return e},getLastChild:function(){if(this.lastElementChild)return this.lastElementChild;var e=this.lastChild;while(e&&1!==e.nodeType)e=e.previousSibling;return e},getChildren:function(){for(var e=[],t=this.children.length;t--;)8!==this.children[t].nodeType&&e.unshift(this.children[t]);return e},getSiblings:function(){var e=this.parentNode.childNodes,t=[];for(var n in e)1===e[n].nodeType&&e[n]!==this&&t.push(e[n]);return t},getScrollWidth:function(){var e=[document.createElement("div"),{width:"100px",height:"100px",overflowY:"scroll"}],t=e[0],n=e[1],a="";for(var r in n)t.style[r]=n[r];return this.appendChild(t),a=t.offsetWidth-t.clientWidth,t.remove(),a},getOuterWidth:function(){var e=[Number.parseFloat(this.css("margin-left")),Number.parseFloat(this.css("margin-right")),this.offsetWidth],t=e[0],n=e[1],a=e[2];return t+n+a},getOuterHeight:function(){var e=[Number.parseFloat(this.css("margin-top")),Number.parseFloat(this.css("margin-bottom")),this.offsetHeight],t=e[0],n=e[1],a=e[2];return t+n+a},css:function(){var e;if(arguments.length>0){if(e=arguments[0],2===arguments.length&&(e={},e[arguments[0]]=arguments[1]),"string"===typeof e)return window.getComputedStyle(this,null)[e];if("object"===r(e))for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.style[t]=e[t])}return this},attr:function(){var e;return 2===arguments.length?(e={},e[arguments[0]]=arguments[1],this.setAttribute(arguments[0],e[arguments[0]]),this):1===arguments.length?(e=arguments[0],this.getAttribute(e)):void 0},rmAttr:function(e){return this.attributes.removeNamedItem(e)},hasClass:function(e){return!!this.className.match(new RegExp("(^|\\s+)"+e+"(\\s+|$)"))},addClass:function(e){return""===this.className?this.className=e:this.hasClass(e)||(this.className+=" "+e),this},rmClass:function(e){if(this.hasClass(e))return this.className=this.className.replace(new RegExp("(^|\\s+)"+e+"(\\s+|$)")," "),this},isNotEmpty:function(){var e;return e="input"===this.localName||"textarea"===this.localName?null!==this.value&&""!==this.value&&this.value:null!==this.innerHTML&&""!==this.innerHTML&&this.innerHTML,e},empty:function(){return"input"===this.localName||"textarea"===this.localName?(this.value="",this.setAttribute("value","")):this.innerHTML="",this},animate:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2e3,a=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=16,c={},i={},s=null,u=0,l=0,m={Linear:function(e,t,n,a){return n*e/a+t},Quadratic:{easeIn:function(e,t,n,a){return n*(e/=a)*e+t},easeOut:function(e,t,n,a){return-n*(e/=a)*(e-2)+t},easeInOut:function(e,t,n,a){return(e/=a/2)<1?n/2*e*e+t:-n/2*(--e*(e-2)-1)+t}},Cubic:{easeIn:function(e,t,n,a){return n*(e/=a)*e*e+t},easeOut:function(e,t,n,a){return n*((e=e/a-1)*e*e+1)+t},easeInOut:function(e,t,n,a){return(e/=a/2)<1?n/2*e*e*e+t:n/2*((e-=2)*e*e+2)+t}},Quartic:{easeIn:function(e,t,n,a){return n*(e/=a)*e*e*e+t},easeOut:function(e,t,n,a){return-n*((e=e/a-1)*e*e*e-1)+t},easeInOut:function(e,t,n,a){return(e/=a/2)<1?n/2*e*e*e*e+t:-n/2*((e-=2)*e*e*e-2)+t}},Quintic:{easeIn:function(e,t,n,a){return n*(e/=a)*e*e*e*e+t},easeOut:function(e,t,n,a){return n*((e=e/a-1)*e*e*e*e+1)+t},easeInOut:function(e,t,n,a){return(e/=a/2)<1?n/2*e*e*e*e*e+t:n/2*((e-=2)*e*e*e*e+2)+t}},Sinusoidal:{easeIn:function(e,t,n,a){return-n*Math.cos(e/a*(Math.PI/2))+n+t},easeOut:function(e,t,n,a){return n*Math.sin(e/a*(Math.PI/2))+t},easeInOut:function(e,t,n,a){return-n/2*(Math.cos(Math.PI*e/a)-1)+t}},Exponential:{easeIn:function(e,t,n,a){return 0===e?t:n*Math.pow(2,10*(e/a-1))+t},easeOut:function(e,t,n,a){return e===a?t+n:n*(1-Math.pow(2,-10*e/a))+t},easeInOut:function(e,t,n,a){return 0===e?t:e===a?t+n:(e/=a/2)<1?n/2*Math.pow(2,10*(e-1))+t:n/2*(2-Math.pow(2,-10*--e))+t}},Circular:{easeIn:function(e,t,n,a){return-n*(Math.sqrt(1-(e/=a)*e)-1)+t},easeOut:function(e,t,n,a){return n*Math.sqrt(1-(e=e/a-1)*e)+t},easeInOut:function(e,t,n,a){return(e/=a/2)<1?-n/2*(Math.sqrt(1-e*e)-1)+t:n/2*(Math.sqrt(1-(e-=2)*e)+1)+t}},Elastic:{easeIn:function(e,t,n,a,r,o){var c;return 0===e?t:1===(e/=a)?t+n:(o||(o=.3*a),!r||r<Math.abs(n)?(r=n,c=o/4):c=o/(2*Math.PI)*Math.asin(n/r),-r*Math.pow(2,10*(e-=1))*Math.sin((e*a-c)*(2*Math.PI)/o)+t)},easeOut:function(e,t,n,a,r,o){var c;return 0===e?t:1===(e/=a)?t+n:(o||(o=.3*a),!r||r<Math.abs(n)?(r=n,c=o/4):c=o/(2*Math.PI)*Math.asin(n/r),r*Math.pow(2,-10*e)*Math.sin((e*a-c)*(2*Math.PI)/o)+n+t)},easeInOut:function(e,t,n,a,r,o){var c;return 0===e?t:2===(e/=a/2)?t+n:(o||(o=a*(.3*1.5)),!r||r<Math.abs(n)?(r=n,c=o/4):c=o/(2*Math.PI)*Math.asin(n/r),e<1?r*Math.pow(2,10*(e-=1))*Math.sin((e*a-c)*(2*Math.PI)/o)*-.5+t:r*Math.pow(2,-10*(e-=1))*Math.sin((e*a-c)*(2*Math.PI)/o)*.5+n+t)}},Back:{easeIn:function(e,t,n,a,r){return void 0===r&&(r=1.70158),n*(e/=a)*e*((r+1)*e-r)+t},easeOut:function(e,t,n,a,r){return void 0===r&&(r=1.70158),n*((e=e/a-1)*e*((r+1)*e+r)+1)+t},easeInOut:function(e,t,n,a,r){return void 0===r&&(r=1.70158),(e/=a/2)<1?n/2*(e*e*((1+(r*=1.525))*e-r))+t:n/2*((e-=2)*e*((1+(r*=1.525))*e+r)+2)+t}},Bounce:{easeIn:function(e,t,n,a){return n-m.Bounce.easeOut(a-e,0,n,a)+t},easeOut:function(e,t,n,a){return(e/=a)<1/2.75?n*(7.5625*e*e)+t:e<2/2.75?n*(7.5625*(e-=1.5/2.75)*e+.75)+t:e<2.5/2.75?n*(7.5625*(e-=2.25/2.75)*e+.9375)+t:n*(7.5625*(e-=2.625/2.75)*e+.984375)+t},easeInOut:function(e,t,n,a){return e<a/2?.5*m.Bounce.easeIn(2*e,0,n,a)+t:.5*m.Bounce.easeOut(2*e-a,0,n,a)+.5*n+t}}},d=function a(){if(clearInterval(t.timer),l+=o,l<=n){for(var u in e){var m=s(l,c[u],i[u],n);"opacity"===u?(t.style[u]=m/100,t.style.filter="alpha(opacity=".concat(m,")")):t.style[u]=m+"px"}t.timer=window.setInterval(a,o)}else{for(var d in e)"opacity"===d?(t.style[d]=e[d]/100,t.style.filter="alpha(opacity=".concat(e[d],")")):t.style[d]=e[d]+"px";"function"===typeof r&&r.call(t),t.timer=null,t.rmClass("animating")}};if(this.addClass("animating"),"number"===typeof a)switch(a){case 1:s=m.Exponential.easeOut;break;case 2:s=m.Elastic.easeOut;break;case 3:s=m.Bounce.easeOut;break;case 4:s=m.Linear;break;default:break}else if("string"===typeof a)switch(a){case"Exponential":s=m.Exponential.easeOut;break;case"Elastic":s=m.Elastic.easeOut;break;case"Bounce":s=m.Bounce.easeOut;break;case"Linear":s=m.Linear;break;default:break}else"function"===typeof a?r=a:a instanceof Array?2===a.length?s=m[a[0]][a[1]]:1===a.length&&(s=m.Linear):s=m.Exponential.easeOut;for(var p in e){var h=0,f=0;u=0,"opacity"===p?(h=Math.random(100*parseFloat(this.css(p))),f=e[p]-h,void 0===h&&(this.style[p]="alpha(opacity:100)",this.style.opacity=1)):"backgroundColor"===p||"display"===p||(h=parseInt(this.css(p)),f=e[p]-h),f&&(u++,c[p]=h,i[p]=f)}if(0===u){if("function"!==typeof r)return;r.call(this)}d()},show:function(){var e,t=function(e){var t="",n=document.createElement(e);return document.body.appendChild(n),t=n.css("display"),n.remove(),t};return"none"===this.style.display&&(this.style.display=""),""===this.style.display&&"none"===getComputedStyle(this)["display"]&&(e=t(this.nodeName)),"none"!==this.style.display&&""!==this.style.display||(this.style.display=e),this},hide:function(){if("none"!==this.css("display"))return this.css("display","none"),this},toggle:function(){"none"===this.css("display")?this.show():this.hide()}}),Object.assign(String.prototype,{isNotEmpty:function(){var e=null===this||"undefined"===typeof this||""===this.trim();return!e&&this.trim()},isEmpty:function(e){return"undefined"===typeof e||null===e||""===e},colorToHex:function(){var e=/^(rgb|RGB)/;if(e.test(this)){var t="#",n=this.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");for(var a in n){var r=Number(n[a]).toString(16);"0"===r&&(r+=r),t+=r}return 7===t.length?t:t.slice(0,7)}return String(this)},colorToRgb:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/,n=this.toLowerCase();if(t.test(n)){if(4===n.length){for(var a="#",r=1;r<=4;r++)a+=n.slice(r,r+1).concat(n.slice(r,r+1));n=a}for(var o=[],c=1;c<7;c+=2)o.push(parseInt("0x"+n.slice(c,c+2)));return"rgba(".concat(o.join(","),", ").concat(e,")")}return n},colorReverse:function(){var e="0x"+this.colorToHex().replace(/#/g,""),t="000000"+(16777215-e).toString(16);return"#"+t.substring(t.length-6,t.length)},encrypt:function(){for(var e=String.fromCharCode(this.charCodeAt(0)+this.length),t=1;t<this.length;t++)e+=String.fromCharCode(this.charCodeAt(t)+this.charCodeAt(t-1));return encodeURIComponent(e)},decrypt:function(){for(var e=decodeURIComponent(this),t=String.fromCharCode(e.charCodeAt(0)-e.length),n=1;n<e.length;n++)t+=String.fromCharCode(e.charCodeAt(n)-t.charCodeAt(n-1));return t}}),Object.assign(Array.prototype,{unique:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;if(e){for(var n=[],r={},o=0;o<this.length;o++)r[this[o][t]]||(n.push(this[o]),r[this[o][t]]=!0);return n}return Array.from(new Set(a(this)))}})},1:function(e,t){},"13c3":function(e,t,n){"use strict";function a(e){var t,n,a,r,o,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500,i=arguments.length>2?arguments[2]:void 0,s=function s(){var u=+new Date-r;u<c&&u>0?t=setTimeout(s,c-u):(t=null,i||(o=e.apply(a,n),t||(a=n=null)))};return function(){for(var n=arguments.length,u=new Array(n),l=0;l<n;l++)u[l]=arguments[l];a=this,r=+new Date;var m=i&&!t;return t||(t=setTimeout(s,c)),m&&(o=e.apply(a,u),a=u=null),o}}function r(e,t,n,a){var r=document.createElement("canvas"),o=r.getContext("2d");t.appendChild(r),r.width=200,r.height=150,r.style.display="none",o.rotate(-20*Math.PI/180),o.font=n||"16px Microsoft JhengHei",o.fillStyle=a||"rgba(180, 180, 180, 0.5)",o.textAlign="left",o.textBaseline="Middle",o.fillText(e,r.width/10,r.height/2),t.style.backgroundImage='url("'.concat(r.toDataURL("image/png"),'")')}function o(){return["#37a2da","#ffdb5c","#fb7293","#a7d691","#9d96f5","#67e0e3","#ff9f7f","#32c5e9","#fdfa4e","#ee6666","#8378ea","#ffc227","#8fcde5","#91cc75","#e062ae","#96bfff","#fd9d75","#73c0de","#fbd379","#62b58e","#9a60b4","#f18585","#768dd1","#fac858","#92e1ff","#a7d691","#e690d1","#3ba272","#fd5151","#5470c6","#fc8452","#5ed7d3","#febe13","#ea7ccc","#85f67a"]}n.d(t,"a",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return o}))},2:function(e,t){},"2af1":function(e,t,n){"use strict";n.r(t),t["default"]={dialog:{title:{add:"{0}添加",addTask:"{0}添加任务",update:"{0}修改",query:"{0}查询",detail:"{0}详情",grant:"{0}授权",config:"{0}设置",th:"{0}自定义列",upload:"{0}导入",addAll:"{0}批量添加",changeAll:"{0}批量转化",change:"{0}转化",updateAll:"{0}批量修改",IP:"{0}IP范围",show:"{0}展示",handle:"{0}处置",logImport:"{0}日志导入",forward:"{0}转发",copy:"{0}复制",customQuery:"{0}自定义查询",packageUpload:"解析规则包导入",network:"{0}配置",centerConfig:"{0}中心配置",rule:"{0}规则",strategyConfig:"{0}策略配置"}}}},3:function(e,t){},3072:function(e,t,n){"use strict";n.r(t);var a={pageSize:20,theme:"light"},r={SET_PAGE_SIZE:function(e,t){e.pageSize=t},SET_THEME:function(e,t){e.theme=t}},o={updatePageSize:function(e,t){var n=e.commit;n("SET_PAGE_SIZE",t)},switchTheme:function(e,t){var n=e.commit;n("SET_THEME",t)}};t["default"]={namespaced:!0,state:a,mutations:r,actions:o}},3196:function(e,t,n){},"35dc":function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={cnvd:{header:"CNVD漏洞库",table:{cnvdId:"漏洞编号",cnvdTitle:"漏洞名称",cnvdLevel:"漏洞等级",relateThreat:"相关威胁",cnvdProvider:"提供商",releaseDate:"发布日期",handel:"操作",affectedProducts:"受影响产品",cnvdDesc:"描述",cnvdReference:"参考",authInfo:"认证信息",cnvdResolve:"解决方案",rollOutFlag:"相关标识",startDate:"发布开始日期",endDate:"发布结束日期"},dialog:{detailTitle:"CNVD漏洞库详情"},placeholder:{keyword:"漏洞编号/漏洞名称"},fuzzyQuery:"漏洞编号/漏洞名称/描述"}},o={cve:{header:"CVE漏洞库",table:{name:"漏洞编号",status:"漏洞状态",description:"漏洞描述",phase:"漏洞阶段",handel:"操作",comments:"备注",votes:"表决",references:"参考"},dialog:{detailTitle:"CVE漏洞库详情"},fuzzyQuery:"漏洞编号/漏洞描述"}},c={threatLibrary:{header:"威胁情报库",type:{ip:"威胁情报IP",domain:"威胁情报域名",url:"威胁情报网址",email:"威胁情报邮箱",hash:"威胁情报Hash"},table:{threatItem:"威胁项",threatIp:"威胁IP",threatType:"威胁类型",frequency:"发生次数",createTime:"创建时间",firstTime:"首次时间",firstStartTime:"首次开始时间",firstEndTime:"首次结束时间",lastTime:"末次时间",lastStartTime:"末次开始时间",lastEndTime:"末次结束时间",record:"记录",action:"动作标签",objectType:"对象类型",historyUrl:"历史网址",malware:"恶意软件标签",country:"国家",province:"省份",city:"城市",organization:"组织",operator:"运营商",code:"代码",longitude:"经度",latitude:"纬度",sha1:"SHA1值",sha265:"SHA256值",hash:"MD5 HASH值",hashType:"Hash类型",source:"发生来源",threatLevel:"威胁级别",activeTime:"激活时间",confidence:"置信度",subscriptionChannel:"订阅频道",attackProtocol:"攻击使用协议"},tabPanel:{info:"信息",confidence:"置信度"},fuzzyQuery:"威胁项/威胁类型"}};t["default"]={repository:Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c)}},"38e2":function(e,t,n){"use strict";n.r(t);n("99af"),n("d3b7");var a=n("a18c"),r={routers:[],addRouters:[]},o={SET_ROUTES:function(e,t){e.addRouters=t,e.routers=a["c"].concat(t)}},c={generateRoutes:function(e){var t=e.commit;return new Promise((function(e){var n=a["b"]||[];t("SET_ROUTES",n),e(n)}))}};t["default"]={namespaced:!0,state:r,mutations:o,actions:c}},"3bd7":function(e,t,n){"use strict";n.r(t);var a={terminalGroupsData:{id:-1,name:"全部终端"},terminalComputerName:""},r={setTerminalGroupsData:function(e,t){e.terminalGroupsData=t},setTerminalComputerName:function(e,t){e.terminalComputerName=t}},o={updateTerminalGroupsData:function(e,t){var n=e.commit;n("setTerminalGroupsData",t)},updateTerminalComputerName:function(e,t){var n=e.commit;n("setTerminalComputerName",t)}};t["default"]={namespaced:!0,state:a,mutations:r,actions:o}},4:function(e,t){},4020:function(e,t,n){"use strict";n("99af"),n("c975"),n("a9e3"),n("d3b7"),n("ac1f"),n("5319");var a=n("bc3a"),r=n.n(a),o=n("4360"),c=n("a18c"),i=n("a47e"),s=n("f7b5"),u=n("f907"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",a=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),l=a.NODE_ENV,m=a.VUE_APP_IS_MOCK,d=a.VUE_APP_BASE_API,p="true"===m?"":d;"production"===l&&(p="");var h={baseURL:p,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===l&&(h.timeout=n),t){case"upload":h.headers["Content-Type"]="multipart/form-data",h["processData"]=!1,h["contentType"]=!1;break;case"download":h["responseType"]="blob";break;case"eventSource":break;default:break}var f=r.a.create(h);return f.interceptors.request.use((function(e){var t=o["a"].getters.token;return""!==t&&(e.headers["access_token"]=t),e}),(function(e){Object(s["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:e,print:!0}),Promise.reject("response-err:"+e)})),f.interceptors.response.use((function(e){var n=void 0===e.headers["code"]?200:Number(e.headers["code"]),a=function(){Object(s["a"])({i18nCode:"logout.message",type:"error"},(function(){c["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),c["a"].replace({path:"/login"}))}))},r=function(){var t=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",a=arguments.length>2?arguments[2]:void 0,r="";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(r="error"),e.data.code>=2e3&&e.data.code<3e3&&(r="warning"),Object(s["a"])({i18nCode:"ajax.".concat(n,".").concat(t),type:r}),Promise.reject("response-err-status:".concat(a||u["a"][n][t]," \nerr-question: ").concat(i["a"].t("ajax.".concat(n,".").concat(t))))};switch(e.data.code){case u["a"].exception.system:t("system");break;case u["a"].exception.server:t("server");break;case u["a"].exception.session:a();break;case u["a"].exception.access:a();break;case u["a"].exception.certification:t("certification");break;case u["a"].exception.auth:t("auth"),c["a"].replace({path:"/401"});break;case u["a"].exception.token:t("token");break;case u["a"].exception.param:t("param");break;case u["a"].exception.idempotency:t("idempotency");break;case u["a"].exception.ip:t("ip"),o["a"].dispatch("user/reset"),c["a"].replace({path:"/login"});break;case u["a"].exception.upload:t("upload");break;case u["a"].attack.xss:t("xss","attack");break;default:t("code","exception",-1);break}};switch(t){case"upload":if(n===u["a"].success)return e.data.data;r();break;case"download":if(n===u["a"].success)return{data:e.data,fileName:decodeURI(e.headers["file-name"])};r();break;default:if(e.data.code===u["a"].success)return e.data.data;r();break}}),(function(e){var n=function(){Object(s["a"])({i18nCode:"logout.message",type:"error"},(function(){c["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),c["a"].replace({path:"/login"}))}))};return"upload"===t?(Object(s["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),403==e.response.status&&n(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(i["a"].t("ajax.service.upload")))):(Object(s["a"])({i18nCode:"ajax.service.timeout",type:"error"}),403==e.response.status&&n(),Promise.reject("response-err-status:".concat(e," \nerr-question: ").concat(i["a"].t("ajax.service.timeout"))))})),f(e)};t["a"]=l},4360:function(e,t,n){"use strict";var a=n("2b0e"),r=n("2f62"),o={routers:function(e){return e.router.routers},addRouters:function(e){return e.router.addRouters},theme:function(e){return e.system.theme},pageSize:function(e){return e.system.pageSize},token:function(e){return e.user.token},publicKey:function(e){return e.user.publicKey},aesKey:function(e){return e.user.aesKey},systemName:function(e){return e.user.systemName},mode:function(e){return e.user.mode},userID:function(e){return e.user.userID},defaultMenu:function(e){return e.user.defaultMenu},homePath:function(e){return e.user.homePath},actions:function(e){return e.user.actions},status:function(e){return e.websocket.status},assetDiscover:function(e){return e.websocket.assetDiscover},managementNetwork:function(e){return e.websocket.managementNetwork},actualLog:function(e){return e.websocket.actualLog},actualTrend:function(e){return e.websocket.actualTrend},terminalGroupsData:function(e){return e.hostguardian.terminalGroupsData},terminalComputerName:function(e){return e.hostguardian.terminalComputerName}},c=o,i=n("921c");a["default"].use(r["a"]);var s=Object(i["b"])(n("f96b")),u=new r["a"].Store({modules:s,getters:c});t["a"]=u},"44be":function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={management:{title:"监控器",props:{monitorName:"监控器名称",status:"状态",monitorType:"监控器类型",monitorTypeName:"监控器类型",pollDate:"轮询时间",edName:"被监控设备名称",agentId:"代理服务器",agentIp:"代理服务器",monitorTemplate:"监控器模板",edIp:"被监控设备IP",agentStatus:"代理状态",monitorEnabled:"监控器状态",createUser:"创建人",createDate:"创建时间",pollUnit:"轮询时间",stateChange:"启用停用",health:"健康度",healthTip:{unableConnect:"服务无法连通",noData:"无数据",normal:"正常"}},status:{on:"启用",off:"停用",normal:"正常",abnormal:"异常",online:"在线",offline:"下线"},placeholder:{startTime:"开始时间",endTime:"结束时间"},config:{asset:{title:"设备信息",edName:"设备名称",edIp:"IP地址",assetType:"设备类型",domaAbbr:"区域简称",createDate:"创建时间"},cpu:{cpuUseRate:"CPU使用率阈值(%)",cpuTimes:"连续达到阈值次数"},memory:{memoryUseRate:"内存使用率阈值(%)",memoryTimes:"连续达到阈值次数"},disk:{diskUseRate:"磁盘使用率阈值(%)"},snmp:{template:"SNMP模板",version:"版本",port:"端口",readCommunity:"读COMMUNITY",writeCommunity:"写COMMUNITY",monitorTemplate:"监控器模板",authWay:"认证方式",authPwd:"认证口令",encryptionWay:"加密方式",encryptionPwd:"加密口令",context:"Context ID",contextName:"Context Name",snmpUserName:"用户名",securityLevel:"安全级别"},template:{attributeName:"属性名称",oid:"OID",mark:"变量标识",expression:"表达式",displayType:"展示类型",templateName:"模板名称",unit:"单位",submit:"提交",cancel:"取消",notes:"备注",configAttribute:"配置属性",displayAttribute:"显示属性",Type:"图表/文本",monitorTemplate:"监控器模板",addTemplate:"新增监控器模板",templateNameTip:"注：建议命名格式为厂商/设备类型。"},thresholdConfiguration:{attributeName:"属性名称",thresholdType:"阈值类型",attribute:"属性",operator:"操作符",threshold:"阈值"},configuration:{attributeName:"属性名称",thresholdName:"阈值名称",operator:"操作符",thresholdType:"阈值类型",threshold:"阈值",description:"事件描述",Type:"性能/故障"}},dialog:{title:{configuration:"配置",template:"模板",IndicatorThresholdConfiguration:"指标阈值配置",delete:"删除",add:"添加",MonitorTemplate:"监控器模板"}},view:{basic:{title:"基本信息",monitorName:"监控器名称",monitorTypeName:"监控器类型",edName:"被监控设备名称",edIp:"IP",currentMemory:"当前内存",currentCpu:"当前CPU",currentDisk:"当前磁盘"},cpu:{cpuId:"CPU",usage:"使用率（%）"},disk:{diskName:"磁盘卷",allDisk:"磁盘总量（G）",restDisk:"剩余磁盘空间（G）",usage:"使用率（%）"},perf:{title:"性能事件",perfName:"事件名称",currentStatus:"当前状态",perfStatus:"状态",perfClassName:"事件类型",perfLevel:"事件等级",edName:"设备名称",domaName:"隶属区域",enterDate:"发生时间",recoveryDate:"恢复时间",perfModule:"事件描述",perfSolution:"解决方案",basicDetail:"基本详情",perfDetail:"历史性能"},fault:{title:"故障事件",faultName:"事件名称",currentStatus:"当前状态",faultStatus:"状态",faultClassName:"事件类型",faultLevel:"事件等级",edName:"设备名称",domaName:"隶属区域",enterDate:"发生时间",recoveryDate:"恢复时间",faultModule:"事件描述",faultSolution:"解决方案",basicDetail:"基本详情",faultDetail:"历史故障"},snmp:{title:"模板信息",attribute:"属性名称",expression:"表达式",displayUnits:"展示单位",displayType:"展示类型",monitorTemplate:"监控器模板"}}}};t["default"]={monitor:Object(a["a"])({},r)}},"4bea":function(e,t,n){"use strict";n.r(t),t["default"]={system:{help:{handbook:"帮助手册"},error:{session:{invalid:"您还没有登录或session已失效,请重新<a href='{0}'>登录</a>"},noPermissions:"您没有使用该功能的权限,请<a href='mailto:{0}'>联系</a>管理员",operation:"检测到业务错误",xssAttack:"检测到XSS跨站脚本攻击",csrfAttack:"检测到CSRF跨站请求伪造攻击",unauthAccess:"检测到非法的访问请求类型",userAbnormal:"账号信息异常",passwordReplace:"新密码与当前密码相同,请更换密码",passwordFault:"旧密码不正确,请重新输入",returnLogin:"点击返回登录页面",manager:"请联系管理员",denied:"非常抱歉您没有访问权限",exception:"非常抱歉您访问的页面存在异常",404:"非常抱歉您访问的页面不存在"}}}},5:function(e,t){},"56d7":function(e,t,n){"use strict";n.r(t);var a={};n.r(a),n.d(a,"timeAgo",(function(){return f})),n.d(a,"numberFormatter",(function(){return b})),n.d(a,"toThousandFilter",(function(){return g})),n.d(a,"uppercaseFirst",(function(){return y}));n("4de4"),n("4160"),n("b64b"),n("159b"),n("e260"),n("e6cf"),n("cca6"),n("a79d");var r=n("2b0e"),o=n("5c96"),c=n.n(o),i=(n("0fae"),n("3f08")),s=(n("8e1f"),n("a18c")),u=n("4360"),l=n("a47e");r["default"].directive("copy",{bind:function(e,t){var n=t.value;e.$value=n,e.handler=function(){e.$value||console.warn("没有需要复制的内容");var t=document.createElement("textarea");t.readOnly="readonly",t.style.position="absolute",t.style.left="-99999px",t.value=e.$value,document.body.appendChild(t),t.select();var n=document.execCommand("Copy");n&&console.info("复制成功"),document.body.removeChild(t)},e.addEventListener("click",e.handler)},componentUpdated:function(e,t){var n=t.value;e.$value=n},unbind:function(e){e.removeEventListener("click",e.handler)}}),r["default"].directive("debounce",{inserted:function(e,t){var n=null;e.addEventListener("click",(function(){null!==n&&(clearTimeout(n),n=null),n=setTimeout((function(){t.value()}),1e3)}))}});n("a9e3"),n("25eb");r["default"].directive("drag",{inserted:function(e){e.style.cursor="move",e.style.position="absolute",e.onmousedown=function(t){var n=t.pageX-e.offsetLeft,a=t.pageY-e.offsetTop;document.onmousemove=function(t){var r=[document.querySelector("header"),document.querySelector(".router-container")],o=r[0],c=r[1],i=document.body.clientWidth-Number.parseInt(e.css("width"))-Number.parseInt(c.css("margin-left"))-Number.parseInt(c.css("margin-right")),s=document.body.clientHeight-Number.parseInt(e.css("height"))-Number.parseInt(c.css("margin-top"))-Number.parseInt(c.css("margin-bottom"))-Number.parseInt(o.css("height")),u=t.pageX-n,l=t.pageY-a;u<0?u=0:u>i&&(u=i),l<0?l=0:l>s&&(l=s),e.style.left="".concat(u,"px"),e.style.top="".concat(l,"px")},document.onmouseup=function(e){document.onmousemove=document.onmouseup=null}}}});n("ac1f"),n("5319");var m=function(e,t){return e.tagName.toLowerCase()===t?e:e.querySelector(t)},d=function(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!0),e.dispatchEvent(n)};r["default"].directive("emoji",{bind:function(e,t,n){var a=/[^\u4E00-\u9FA5|^\ufe30-\uffa0|\d|a-zA-Z|\r\n\s,.?!:;"'…—&$=@#%^~<>()\-+/*{}[\]]|\s/g,r=m(e,"input")||m(e,"textarea");e.$input=r,r.handler=function(){var e=r.value;r.value=e.replace(a,""),d(r,"input")},r.addEventListener("keyup",r.handler)},unbind:function(e){e.$input.removeEventListener("click",e.$input.handler)}}),r["default"].directive("focus",{inserted:function(e){var t=e.tagName.toLowerCase();"input"===t||"textarea"===t?e.focus():null!==e.querySelector("input")?e.querySelector("input").focus():null!==e.querySelector("textarea")&&e.querySelector("textarea").focus()}}),r["default"].directive("has",{inserted:function(e,t){var n=function(e){var t=u["a"].getters.actions?u["a"].getters.actions:[];for(var n in t)if(t[n]===e)return!0;return!1};n(t["value"])||e.parentNode.removeChild(e)}}),r["default"].directive("longpress",{bind:function(e,t,n){"function"!==typeof t.value&&console.error("返回值必须为一个函数");var a=null;e.handler=function(e){t.value(e)};var r=function(){null!==a&&(clearTimeout(a),a=null)},o=function(t){"click"===t.type&&0!==t.button||null===a&&(a=setTimeout((function(){e.handler()}),2e3))};e.addEventListener("mousedown",o),e.addEventListener("touchstart",o),e.addEventListener("click",r),e.addEventListener("mouseout",r),e.addEventListener("touchend",r),e.addEventListener("touchcancel",r)},componentUpdated:function(e,t){var n=t.value;e.$value=n},unbind:function(e){e.removeEventListener("click",e.handler)}});var p=n("13c3");r["default"].directive("watermark",{bind:function(e,t){Object(p["c"])(t.value.text,e,t.value.font,t.value.textColor)}}),r["default"].directive("intersect",{inserted:function(e,t){var n={root:null,rootMargin:"0px",threshold:.1},a=function(e,n){e.forEach((function(e){e.isIntersecting?(t.value(!0),"once"===t.arg&&n.unobserve(e.target)):t.value(!1)}))},r=new IntersectionObserver(a,n);r.observe(e)}});n("fb6a"),n("b6802"),n("d3b7"),n("25f0");function h(e,t){return 1===e?e+t:e+t+"s"}function f(e){var t=Date.now()/1e3-Number(e);return t<3600?h(~~(t/60)," minute"):t<86400?h(~~(t/3600)," hour"):h(~~(t/86400)," day")}function b(e,t){for(var n=[{value:1e18,symbol:"E"},{value:1e15,symbol:"P"},{value:1e12,symbol:"T"},{value:1e9,symbol:"G"},{value:1e6,symbol:"M"},{value:1e3,symbol:"k"}],a=0;a<n.length;a++)if(e>=n[a].value)return(e/n[a].value+.1).toFixed(t).replace(/\.0+$|(\.[0-9]*[1-9])0+$/,"$1")+n[a].symbol;return e.toString()}function g(e){return(+e||0).toString().replace(/^-?\d+/g,(function(e){return e.replace(/(?=(?!\b)(\d{3})+$)/g,",")}))}function y(e){return e.charAt(0).toUpperCase()+e.slice(1)}n("09ea"),n("c975"),n("96cf");var v=n("c964"),k=n("a78e"),T=n.n(k),I=n("323e"),P=n.n(I),N=(n("a5d8"),["/login"]);T.a.get("store")&&u["a"].replaceState(Object.assign({},u["a"].state,JSON.parse(T.a.get("store")))),P.a.configure({showSpinner:!1}),s["a"].beforeEach(function(){var e=Object(v["a"])(regeneratorRuntime.mark((function e(t,n,a){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:P.a.start(),r=u["a"].getters.mode,"online"===r?"/login"===t.path?(a(),P.a.done()):a():-1!==N.indexOf(t.path)?a():(a("/login?redirect=".concat(t.path)),P.a.done());case 3:case"end":return e.stop()}}),e)})));return function(t,n,a){return e.apply(this,arguments)}}()),s["a"].afterEach((function(e){P.a.done()}));n("9221");var S=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["offline"===e.$store.getters.mode?"app-login-bg":"app-main-bg"],attrs:{id:"app"}},[e.isReloadAlive?n("router-view"):e._e()],1)},w=[],E={name:"App",provide:function(){return{reload:this.reload}},data:function(){return{isReloadAlive:!0,active:"login"}},computed:{theme:function(){return this.$store.getters.theme}},watch:{theme:function(){this.setTheme()}},mounted:function(){this.setTheme(),this.refreshSaveStore()},methods:{setTheme:function(){document.documentElement.setAttribute("data-theme",this.$store.getters.theme),n("b0fd")("./".concat(this.$store.getters.theme,"/element-ui/index.scss"))},reload:function(){var e=this;this.isReloadAlive=!1,this.$nextTick((function(){e.isReloadAlive=!0}))},refreshSaveStore:function(){var e=this;document.querySelector("title").innerText=this.$store.getters.systemName,window.addEventListener("beforeunload",(function(){T.a.set("store",JSON.stringify(e.$store.state))}))}}},C=E,O=(n("5c0b"),n("2877")),A=Object(O["a"])(C,S,w,!1,null,null,null),D=A.exports;r["default"].config.productionTip=!1,r["default"].use(c.a,{size:"small"}),r["default"].use(i["a"].Plugin),Object.keys(a).forEach((function(e){r["default"].filter(e,a[e])})),new r["default"]({el:"#app",router:s["a"],store:u["a"],i18n:l["a"],render:function(e){return e(D)}})},"58bd":function(e,t,n){"use strict";n.r(t);var a={websocket:null,status:!1,assetDiscover:[],managementNetwork:{},actualLog:[],actualTrend:[]},r={WEBSOCKET_INIT:function(e,t){e.status=!1,e.websocket=new WebSocket(t.url,[t.token]),e.websocket.onopen=function(){console.warn("WebSocket is open"),e.status=!0},e.websocket.onmessage=function(t){var n=JSON.parse(t.data);"heart"===n.topic&&e.websocket.send(JSON.stringify({topic:"heart",action:3,message:1})),"asset_discover"===n.topic&&(e.assetDiscover=n),"check_network_card_push"===n.topic&&(e.managementNetwork=n),"actual_log"===n.topic&&(e.actualLog=n),"actual_trend"===n.topic&&(e.actualTrend=n)},e.websocket.onerror=function(){console.error("WebSocket is error"),e.status=!1},e.websocket.onclose=function(){console.warn("WebSocket is close"),e.status=!1}},WEBSOCKET_SEND:function(e,t){e.websocket.send(JSON.stringify(t))},WEBSOCKET_ClOSE:function(e){e.status=!1,e.websocket.close()}},o={init:function(e,t){var n=e.commit;n("WEBSOCKET_INIT",t)},send:function(e,t){var n=e.commit;n("WEBSOCKET_SEND",t)},close:function(e){var t=e.commit;t("WEBSOCKET_CLOSE")}};t["default"]={namespaced:!0,state:a,mutations:r,actions:o}},"5aa2":function(e,t,n){"use strict";n.r(t),t["default"]={level:{empty:"暂无风险等级",general:"一般",low:"低级",middle:"中级",high:"高级",serious:"严重"}}},"5c0b":function(e,t,n){"use strict";var a=n("9c0c"),r=n.n(a);r.a},6:function(e,t){},6894:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={table:{demo:"示范",label:"标准列表",index:"索引",userID:"用户ID",eName:"英文名",cName:"中文名",registryTime:"注册时间",telephone:"固定电话",cellphone:"移动电话",email:"电子邮件",address:"住 址",postcode:"邮编"}};t["default"]={demo:Object(a["a"])({},r)}},"6b7d":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"TableNormal",path:"/demo/table/normal",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-1d3ee984")]).then(n.bind(null,"b42f"))}},{name:"TableDetail",path:"/demo/table/detail",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-705935c8")]).then(n.bind(null,"f202"))}},{name:"TableTree",path:"/demo/table/tree",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-21019dc8")]).then(n.bind(null,"5678"))}},{name:"ChartBar",path:"/demo/chart/bar",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-7a1ba5bb")]).then(n.bind(null,"a43b"))}},{name:"ChartLine",path:"/demo/chart/line",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-af0f85a6")]).then(n.bind(null,"cc24"))}},{name:"ChartPie",path:"/demo/chart/pie",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-d6a687b2")]).then(n.bind(null,"a60e"))}},{name:"Fishbone",path:"/demo/chart/fishbone",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-3e856590"),n.e("chunk-82de00aa"),n.e("chunk-7e67a3f4")]).then(n.bind(null,"ed96"))}}]},"6cf0":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"AuditEvent",path:"/audit/event",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-4595239d")]).then(n.bind(null,"b9fc"))}},{name:"AuditPerson",path:"/audit/person",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-51837cbe")]).then(n.bind(null,"c1e8"))}},{name:"AuditStrategy",path:"/audit/strategy",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-29fb3250")]).then(n.bind(null,"6f1d"))}},{name:"AuditType",path:"/audit/type",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-2feb1b2c")]).then(n.bind(null,"e007"))}},{name:"BehaviorStrategy",path:"/audit/behavior-strategy",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-ec16bdca")]).then(n.bind(null,"2951"))}},{name:"AccessControl",path:"/audit/accessControl",component:function(){return n.e("chunk-98414734").then(n.bind(null,"7c7c"))}},{name:"KvmOperation",path:"/audit/kvmOperation",component:function(){return n.e("chunk-06e46a07").then(n.bind(null,"e143"))}},{name:"NetworkOperation",path:"/audit/networkOperation",component:function(){return n.e("chunk-c712795a").then(n.bind(null,"29b2"))}},{name:"SerialPortOperation",path:"/audit/serialPortOperation",component:function(){return n.e("chunk-7ac16c18").then(n.bind(null,"94f4"))}},{name:"UdiskOperation",path:"/audit/udiskOperation",component:function(){return n.e("chunk-1872aa56").then(n.bind(null,"fec0"))}}]},7:function(e,t){},7979:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={menu:{name:"菜单管理",table:{parent:"父级菜单",name:"菜单名称",location:"菜单路径",status:"菜单状态",icon:"菜单图标",description:"菜单描述",resource:"资源名称"},codeList:{show:"显示",hide:"隐藏",toolbar:"工具栏"},header:{dialogTitle:"菜单",operation:"操作",placeholder:"请选择",moveUpSelMessage:"请选择一条记录进行移动"}}},o={resource:{infoItem:{resourceToken:"资源标识",resourceName:"资源名称",resourceStatus:"资源状态",resourceStatusText:"资源状态",authority:"隶属权限",authorityText:"隶属权限",resourceDescription:"资源描述",actionName:"功能名称",actionToken:"功能标识",actionStatus:"功能状态",actionDescription:"功能描述"},authority:{system:"系统管理员",running:"运维管理员",audit:"系统审计员"},codeList:{resourceStatus:{show:"显示",hide:"隐藏"},actionStatus:{show:"显示",hide:"隐藏"}},header:{dialogTitle:"资源管理",operation:"操作",placeholder:"请选择",innerDialogTitle:"功能",tipInfo:{resourceTokenRepeat:"添加资源标识重复!",actionTokenRepeat:"添加功能标识重复！"}},placeholder:{keyword:"请输入关键字查询",resourceToken:"资源标识",resourceName:"资源名称",resourceStatus:"资源状态",authority:"隶属权限",resourceDescription:"资源描述"}}},c={role:{tipsError:"当前授权资源为工具栏菜单,请再至少选择一个非工具栏菜单",infoItem:{roleName:"角色名称",roleStatus:"角色状态",roleDescription:"角色描述",grantedUsers:"已授权",resources:"资源",grantResources:"授权资源",delete:"删除成功",update:"修改成功",userName:"用户名称"},userManagement:{userAccount:"用户账号"},header:{dialogTitle:"角色管理",placeholder:"请选择"},roleStatus:{show:"显示",hidden:"隐藏"},placeholder:{keyword:"请输入关键字查询",roleName:"角色名称",roleStatus:"角色状态",roleDescription:"角色描述"}}},i={system:{title:{email:"邮件服务",timing:"校时",databaseConfig:"数据库维护配置",databaseTable:"数据库维护列表",dataBackupTable:"数据备份列表",snapBackupConfig:"快照备份设置",snapshotBackupTable:"快照备份列表"},tab:{system:"系统配置",basic:"基本信息",diskSpace:"磁盘空间设置",database:"数据库维护",license:"License管理",backup:"数据备份",snapshot:"系统快照",threat:"情报库设置",systemAlarm:"系统告警通知"},label:{productName:"产品名称",productVersion:"产品版本",buildVersion:"build号",productModel:"产品型号",systemName:"系统名称",sshService:"SSH服务",email:"管理员邮件",defaultPassword:"默认密码",captcha:"启动验证码",accountLockEnable:"账号锁定策略",accountLockTime:"账号锁定监听时间周期",accountLockCnt:"账号锁定监听次数",accountAutoUnlockEnable:"账号自动解锁策略",accountAutoUnlockTime:"账号自动解锁时间周期",systemTimeout:"系统超时时间",accountValidEnable:"账号有效期策略",passwordOverdueEnable:"密码过期策略",passwordOverdueTime:"密码过期时间",logDeduplicate:"日志去重",cpuThreshold:"CPU阈值",memoryThreshold:"内存阈值",sendMailServer:"发送邮件服务器",receiveMailServer:"接收邮件服务器",port:"端口",senderAddress:"发件人地址",serverAuth:"服务器身份认证",user:"用户名",password:"密码",previousTime:"上次校时时间",previousMode:"上次校时方式",centerSeverTime:"中心服务器时间",configCenterSeverTime:"设置中心服务器时间",viewSeverTime:"查看服务器时间",ntpSeverConfig:"NTP服务器设置",autoValidate:"自动校验功能",settingCycle:"设定周期",settingTime:"设定时间",nextTime:"下次校时时间",systemCertification:"系统KEY认证",encryption:"加密方式",manualTiming:"手动校时",autoTiming:"自动校时",cleanHistory:"日志磁盘空间清理历史",cleanSpace:"清理磁盘空间",spaceSurpass:"空间超过",used:"已用",cleanTime:"清理时间，保留",warning:"时发布警告",monthData:"月的数据",safeguard:"时自动维护",dataRetainTime:"数据保留时间",time:"时间",description:"描述",result:"结果",license:"上传License文件",machineCode:"机器码",backupStrategy:"备份策略",backupMode:"备份方式",ftpBackup:"FTP备份",ip:"ip地址",account:"账号",path:"路径",jobStatus:"是否启用",defaultBackup:"默认（每天备份）",customBackup:"自定义",immediateBackup:"立即备份",noBackup:"不备份",backupWay:"备份方式",backupCycle:"备份周期",backupTime:"备份时间",backupRange:"备份范围",timeRange:"时间范围",all:"全部"},tip:{systemName:"可对系统名称修改",email:"请输入管理员邮箱",defaultPassword:"请输入默认密码",captcha:"验证码功能开启和关闭",test:"您还未通过测试，确定要提交吗？",accountLockEnable:"账号锁定功能开启和关闭",accountLockTime:"请输入监听时间周期单位为秒",accountLockCnt:"请输入监听次数单位为次",accountAutoUnlockEnable:"账号自动解锁功能开启和关闭",accountAutoUnlockTime:"请输入账号自动解锁时间周期单位为分钟",systemTimeoutLockTime:"请输入系统超时锁定时间单位为分钟",accountValidEnable:"账号有效期功能开启和关闭",passwordOverdueEnable:"密码过期策略功能开启和关闭",passwordOverdueTime:"请输入密码过期时间单位为天",logDeduplicate:"日志去重功能开启和关闭",cpuThreshold:"请输入CPU阈值单位为%",memoryThreshold:"请输入内存阈值单位为%",cleanHistoryMonth:"请填写过去几个月的历史日志数据",cleanHistoryDay:"请填写过去几日的历史日志数据",cleanSpace:"请填写当磁盘占用率到达多少百分比",largeData:"(数据量较大时，不建议使用)",manualTime:"请手动设置校验时间",autoTime:"请选择自动校时时间",centerServerTime:"中心服务器时间"},button:{testEmail:"测试邮件服务",testTiming:"测试校时"},threat:{emptyError:"上传错误这是空文件",structureError:"上传错误结构非法",typeError:"上传错误类型非法",nameError:"上传错误名称非法",backupCycle:"同步周期",backupTime:"同步时间开启",cuttingTitle:"情报库升级",upload:{backupTimeText:"每天零点五分自动更新",successUpload:"上传成功",Submit:"提交",choice:"请选择上传文件",title:"上传情报库",titleInput:"上传情报库文件",uploadText:"正在上传请耐心等待"}},systemAlarm:{title:"通知方式",isMail:"邮件",mailTo:"邮件地址",snmpForward:"通知服务器",recipientAddress:"收件人地址",isSound:"声、光、电",isSms:"短信",mobileUrl:"网关地址",mobileEcName:"企业名称",mobileApId:"接口账号",mobileSecretKey:"密码",mobileMobiles:"手机号码",mobileSign:"签名编码",mobileAddSerial:"扩展码"},upload:{title:"数据恢复",downLoad:"下载模板",chooseFile:"请选择文件",exceed:"当前限制选择 1 个文件，请删除后再上传",remindTip:"当导入设备名称与系统设备名称相同时，以导入数据为准",talkTip:"当导入设备名称与系统设备名称相同时，以系统数据为准",successUpload:"恢复操作完成，数据恢复后会新增记录提示!"}}},s={user:{name:"用户管理",label:{account:"用户账号",userState:"用户状态",accountState:"账号状态",passwordState:"密码状态",description:"用户描述",timeRange:"时间范围",activeCyclic:"有效登录时间"},option:{enable:"启用",disable:"禁用",normal:"正常",manualLock:"手动锁定",systemLock:"系统锁定",reset:"重置",init:"初始化"}}},u={logAudit:{name:"日志审计",label:{operator:"日志操作者",resource:"日志资源",operation:"日志操作",result:"操作结果",ip:"日志IP",date:"日志时间",forwardAddress:"转发地址",noData:"暂无数据",log:"日志"},placeholder:{ip:"IP",port:"端口"}}},l={logBackup:{name:"日志备份",label:{name:"文件名称",time:"备份时间"},dialog:{title:{config:"日志备份设置"},task:{type:"任务类型",cycle:"任务周期",exeTime:"执行时间"}}}},m={forwardServer:{id:"转发系统ID",poid:"企业节点号",coid:"OID节点号",community:"Community",type:"转发方式",reIP:"其他服务器IP",pushContent:"推送内容",pushFilter:"推送过滤",port:"端口号",startstop:"启停",remark:"描述",forwardServer:"数据外发",placeholder:{queryInput:"请输入转发方式",type:"转发方式",community:"Community",poid:"暂无企业节点号",coid:"暂无OID节点号",reIP:"其他服务器IP",remark:"描述"},delete:{running:{audit:"有审计策略正在使用该转发策略，请修改后删除",assoc:"有关联策略正在使用该转发策略，请修改后删除",aggre:"有聚合策略正在使用该转发策略，请修改后删除"}},update:{repeat:"转发策略信息重复"}}},d={systemUpgrade:{upContent:"升级内容",upOldEdition:"升级前版本",edition:"当前版本",noEdition:"暂无版本信息",fileSize:"上传文件大小不得大于100MB!",upNewEdition:"升级后版本",upResult:"升级结果",upTime:"升级时间",bag:"升级包选择",upGrade:"升级",backOff:"回退上一版本",upManagement:"系统升级管理",loadingText:"拼命升级中...升级成功后部分服务将重启...请三分钟后使用...",updatePackageNameError:"上传文件名称不合法",updatePackageCurrentError:"请上传符合当前版本的升级包",updatePackageSameError:"版本号不能相同"}},p={network:{form:{localHost:"主机名",firstDns:"首选DNS",secondDns:"备选DNS",ipAddress:"IP地址",maskCodeBit:"掩码位数",maskCode:"掩码",gateWay:"网关",bits:"位数"},title:{basic:"基本配置",network:"{0}网络配置"},tip:{configNone:"网络配置信息不能为空",manageNone:"管理口配置信息不能为空",ipRepeat:"网口地址信息不能重复",false:"IP、子网掩码和网关不匹配"}}},h={proxy:{title:"平台信息",label:{ip:"IP",status:"状态",systemStatus:"系统状态",description:"描述",updateTime:"更新时间",operationstatus:"卸载恢复"},detail:{basic:{title:"当前状况",status:"状态",updateTime:"更新时间"},history:{title:"历史状况"},cpu:{title:"CPU信息",cpuNum:"核心数",sys:"CPU系统使用率",used:"CPU用户使用率",wait:"CPU当前等待率",free:"CPU当前空闲率"},mem:{title:"内存信息",total:"内存总量（GB）",used:"已用内存",free:"剩余内存",usage:"使用率"},sys:{title:"系统信息",computerName:"服务器名称",osName:"操作系统",osArch:"系统架构",agentVersion:"代理版本"},sysFiles:{title:"系统文件信息",dirName:"盘符路径",sysTypeName:"盘符类型",typeName:"文件类型",total:"总大小",free:"剩余大小",used:"已经使用量",usage:"使用率"}},existEquipment:"该代理下存在启用的监控器、采集器信息，当前不允许删除。",network:{networkConfig:"网络配置",service:"服务配置",centerIp:"设置中心IP"}}};t["default"]={management:Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c),i),s),u),l),m),d),p),h)}},"7c6b":function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={forecastAlarm:{title:"预测告警",label:{type:"分类",infoItem:"信息项",updateTime:"入库时间",actualVal:"实际值",expectedVal:"预测值",abnormalModel:"异常模型",model:"模型名称",accurTime:"发生时间",anomalyDesc:"异常描述"},placeholder:{infoItem:"信息项",startAccurTime:"起始发生时间",endAccurTime:"结束发生时间"},detail:{actualVal:"实际值",expectedVal:"预测值",key:"异常模型",name:"模型名称",time:"发生时间"}}},o={forecastAnalysis:{title:"预测分析",label:{type:"分类",infoItem:"信息项",enterTime:"更新时间",funcEnable:"功能开启",externalSystem:"转发外系统",externalMail:"外转邮箱"},placeholder:{infoItem:"信息项"},detail:{actualVal:"实际值",expectedVal:"预测值",key:"异常模型",name:"模型名称",time:"发生时间"},chart:{baselineChart:"基线对比图",maeChart:"mae图",mapeeChart:"mapee图",maseChart:"mase图",mapeChart:"mape图",smapeChart:"smape图"}}};t["default"]={forecast:Object(a["a"])(Object(a["a"])({},r),o)}},"7cc7":function(e,t,n){"use strict";n.r(t),t["default"]={code:{status:{on:"启用",off:"停用"}}}},"7fa2":function(e,t,n){"use strict";n.r(t);n("d3b7"),n("ac1f"),n("5319");var a=n("a18c"),r=n("fd82"),o=n("cd0e"),c={token:"",publicKey:"",aesKey:"",systemName:"",mode:"offline",userID:"",defaultMenu:"",homePath:"",actions:[]},i={SET_TOKEN:function(e,t){e.token=t},SET_PUBLIC_KEY:function(e,t){e.publicKey=t},SET_AES_KEY:function(e,t){e.aesKey=t},SET_SYSTEM_NAME:function(e,t){e.systemName=t},SET_MODE:function(e,t){e.mode=t},SET_USER_ID:function(e,t){e.userID=t},SET_USER_MENU:function(e,t){e.defaultMenu=t},SET_HOME_PATH:function(e,t){e.homePath=t},SET_ACTION:function(e,t){e.actions=t}},s={saveMode:function(e,t){var n=e.commit;n("SET_MODE",t)},saveUserID:function(e,t){var n=e.commit;n("SET_USER_ID",t)},saveUserMenu:function(e,t){var n=e.commit;n("SET_USER_MENU",t)},updatePath:function(e,t){var n=e.commit;n("SET_HOME_PATH",t)},authAction:function(e,t){var n=e.commit;n("SET_ACTION",t)},saveSystemName:function(e,t){var n=e.commit;n("SET_SYSTEM_NAME",t),document.querySelector("title").innerText=t},registry:function(e){var t=e.commit;return new Promise((function(e,n){Object(r["c"])().then((function(n){t("SET_TOKEN",n.accessToken),t("SET_PUBLIC_KEY",n.publicKey),t("SET_AES_KEY",n.aesKey),t("SET_SYSTEM_NAME",n.systemName),e(n)})).catch((function(e){n(e)}))}))},login:function(e,t){e.commit;return new Promise((function(e,n){Object(r["b"])(t).then((function(t){e(t)})).catch((function(e){n(e)}))}))},mailLogin:function(e,t){e.commit;return new Promise((function(e,n){Object(r["e"])(t).then((function(t){e(t)})).catch((function(e){n(e)}))}))},logout:function(e){var t=e.commit;return new Promise((function(e,n){Object(o["c"])().then((function(n){n&&(t("SET_TOKEN",""),t("SET_PUBLIC_KEY",""),t("SET_AES_KEY",""),t("SET_SYSTEM_NAME",""),t("SET_USER_ID",""),t("SET_MODE","offline"),t("SET_USER_MENU",""),t("SET_HOME_PATH",""),t("SET_ACTION",""),a["a"].replace({path:"/login"}),e(n))})).catch((function(e){n(e)}))}))},reset:function(e){var t=e.commit;t("SET_MODE","offline")}};t["default"]={namespaced:!0,state:c,mutations:i,actions:s}},8:function(e,t){},"808d":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{path:"/guard/device-list",name:"DeviceList",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-04a0dbd0"),n.e("chunk-4d3f92b6")]).then(n.bind(null,"1c41"))},meta:{title:"设备列表",icon:"list"}},{path:"/guard/device-group",name:"DeviceGroup",component:function(){return n.e("chunk-b30294b4").then(n.bind(null,"bba6"))},meta:{title:"设备分组",icon:"group"}},{path:"/guard/upgrade-management",name:"UpgradeManagement",component:function(){return Promise.all([n.e("chunk-a806134c"),n.e("chunk-c7bfffa8")]).then(n.bind(null,"a0f9"))},meta:{title:"升级管理",icon:"upgrade"}},{path:"/guard/auth-management",name:"AuthManagement",component:function(){return Promise.all([n.e("chunk-a806134c"),n.e("chunk-04a0dbd0"),n.e("chunk-58cbb79c")]).then(n.bind(null,"2f63"))},meta:{title:"认证管理",icon:"auth"}},{path:"/guard/strategy/manage",name:"StrategyManagement",component:function(){return Promise.all([n.e("chunk-04a0dbd0"),n.e("chunk-24330f3d")]).then(n.bind(null,"9d9e"))},meta:{title:"策略管理",icon:"strategy"}},{path:"/guard/strategy-record",name:"StrategyRecord",component:function(){return n.e("chunk-1d211c5e").then(n.bind(null,"caca"))},meta:{title:"策略记录",icon:"record"}}]},"813d":function(e,t){},"81d2":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{path:"/audit/device/list",name:"AuditOldDeviceList",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-3e856590"),n.e("chunk-f353fcc8"),n.e("chunk-184a2d69")]).then(n.bind(null,"314c"))},meta:{title:"设备列表",icon:"soc-icon-point"}},{path:"/audit/device/groupmanagement",name:"AuditOldDeviceGroupManagement",component:function(){return n.e("chunk-b30294b4").then(n.bind(null,"bba6"))},meta:{title:"设备分组",icon:"soc-icon-point"}},{path:"/audit/strategy/strategy-protocol",name:"AuditOldStrategyProtocol",component:function(){return n.e("chunk-dea0fda2").then(n.bind(null,"0259"))},meta:{title:"监测审计协议集",icon:"soc-icon-point"}},{path:"/audit/strategy/strategy-collection",name:"AuditOldStrategyCollection",component:function(){return Promise.all([n.e("chunk-a806134c"),n.e("chunk-624217f0"),n.e("chunk-0e5fb27b")]).then(n.bind(null,"433e"))},meta:{title:"监测审计采集策略",icon:"soc-icon-point"}},{path:"/audit/strategy/strategy-filter",name:"AuditOldStrategyFilter",component:function(){return Promise.all([n.e("chunk-a806134c"),n.e("chunk-624217f0"),n.e("chunk-0935fe97")]).then(n.bind(null,"763b"))},meta:{title:"监测审计过滤策略",icon:"soc-icon-point"}},{path:"/audit/strategy/strategyAudit-record",name:"AuditOldStrategyAuditRecord",component:function(){return Promise.all([n.e("chunk-a806134c"),n.e("chunk-da37c52e")]).then(n.bind(null,"18d7"))},meta:{title:"策略下发记录",icon:"soc-icon-point"}}]},8225:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"MonitorManagement",path:"/monitor/management",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-bff8a636")]).then(n.bind(null,"f4e2"))}}]},"86ba":function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={original:{name:"原始日志",total:"总计",loaded:"已加载",expect:"预计还有",label:{expression:"表达式名称",type2Name:"原始日志名称",logName:"事件类型",level:"等级",srcDevice:"日志源设备",dstDevice:"发生源IP",srcIP:"源IP",dstIP:"目的IP",customName:"自定义名称"},placeholder:{srcStartIP:"源起始IP",srcEndIP:"源终止IP",dstStartIP:"目的起始IP",dstEndIP:"目的终止IP",fromStartIP:"发生源起始IP",fromEndIP:"发生源终止IP",logFormat:"日志格式",forwardServer:"转发服务器",username:"用户",targetObject:"目标对象",logRecieveTime:"接收时间",logTime:"日志时间",devId:"设备序列号"},group:{basic:"基本信息",source:"源",destination:"目的",from:"发生源",geo:"地理信息",other:"其他",log:"日志原文"},basic:{type2Name:"原始日志名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"事件等级",deviceCategoryName:"设备类别",deviceTypeName:"设备类型",time:"接收时间",logTime:"日志时间",code:"特征值",username:"用户",targetObject:"目标对象",eventDesc:"事件描述",action:"动作",resultName:"事件结果"},source:{sourceIp:"源IP",sourceAddress:"源地址",sourcePort:"源端口",sourceAsset:"源设备",sourceMac:"源MAC地址",sourceMask:"源掩码"},destination:{targetIp:"目的IP",targetAddress:"目的地址",targetPort:"目的端口",targetAsset:"目的设备",targetMac:"目的MAC地址",targetMask:"目的掩码"},from:{fromIp:"发生源IP"},geo:{sourceCountryName:"源国家",sourceCountryLongitude:"源国家经度",sourceCountryLatitude:"源国家纬度",sourceAreaName:"源区域",sourceAreaLongitude:"源区域经度",sourceAreaLatitude:"源区域纬度",targetCountryName:"目的国家",targetCountryLongitude:"目的国家经度",targetCountryLatitude:"目的国家纬度",targetAreaName:"目的区域",targetAreaLongitude:"目的区域经度",targetAreaLatitude:"目的区域纬度"},other:{protocol:"协议",receiveProtocol:"接收协议",year:"年",month:"月",day:"日",hour:"时",minute:"分",second:"秒",otherIp:"其他IP",otherPort:"其他端口"},log:{raw:"日志原文"},forward:{status:"转发状态",logFormat:"日志格式",forwardServer:"转发服务器"},fuzzyQuery:"原始日志名称/日志原文",parseRateDesc:"每千条日志解析规则平均成功耗时{0}ms"}},o={polymerizationStrategy:{strategy:"聚合策略",table:{alarmName:"策略名称",matchCriteriaList:"匹配规则",alarmTimeout:"最大限定时间(秒)",countThreshold:"最大聚合次数",eventFrequency:"事件频率(秒/条)",isAggrate:"是否聚合",handel:"操作"},label:{alarmTimeout:"最大限定时间",countThreshold:"最大聚合次数",alarmStartTimeout:"最大限定时间(起)",alarmEndTimeout:"最大限定时间(止)",countStartThreshold:"最大聚合次数(起)",countEndThreshold:"最大聚合次数(止)",forwardType:"转发类型",eventFrequency:"事件频率",isAggrate:"是否聚合"},title:{update:"聚合策略修改"},tip:{alarmTimeout:"终止限定时间必须大于起始限定时间",countThreshold:"终止聚合次数必须大于起始聚合次数"},upload:{chooseFile:"请选择文件",exceed:"当前限制选择 1 个文件，请删除后再上传",successUpload:"导入解析规则包成功"}}},c={relevance:{table:{eventName:"事件名称",policyName:"策略名称",eventLevelName:"事件等级",createDate:"产生时间",eventTypeName:"事件名称",eventCategoryName:"事件类型",updateDate:"更新时间",count:"次数",handel:"操作",typeName:"事件类型",level:"事件等级",srcIp:"源IP",dstIp:"目的IP",dateTime:"时间",raw:"日志原文",eventDesc:"事件描述"},time:{createDateStart:"产生时间起",createDateEnd:"产生时间止",updateDateStart:"更新时间起",updateDateEnd:"更新结束止"},dialog:{detailTitle:"关联事件详情",colTitle:"关联事件自定义列"},header:"关联事件",detailOriginalColumn:{type2Name:"原始事件名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"事件等级",srcIp:"源IP",dstIp:"目的IP",dateTime:"时间",raw:"日志原文"},chart:{attackCount:"攻击次数",srcIp:"源IP",dstIp:"目的IP",level:"等级"}}},i={security:{table:{type2Name:"事件名称",alarmTypeName:"事件类型",alarmCategoryName:"事件类别",alarmCategory:"事件类别",level:"事件等级",count:"聚合数量",srcIpv:"源IP",srcPort:"源端口",dstIpv:"目的IP",dstPort:"目的端口",aggrStartDate:"聚合开始时间",aggrEndDate:"聚合结束时间",fromDeviceType:"发生源设备类型",deviceTypeName:"日志源设备",protocol:"协议",advice:"处理意见",raw:"日志原文",date:"时间",fromIpv:"发生源IP",alarmDesc:"事件描述"},dialog:{detailTitle:"安全事件详情",colTitle:"安全事件自定义列"},detailColumns:{type2Name:"原始事件名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"事件等级",srcIp:"源IP",dstIp:"目的IP",dateTime:"时间",raw:"日志原文"},placeholder:{type2Name:"事件名称",inputSearch:"事件名称/事件类型/发生源IP/日志源设备",startIp:"发生源起始IP",endIp:"发生源终止IP",srcStartIp:"源起始IP",srcEndIp:"源终止IP",dstStartIp:"目的起始IP",dstEndIp:"目的终止IP"},header:"安全事件",checkBox:{type2Name:"事件名称",alarmTypeName:"事件类型",alarmCategoryName:"事件类别",level:"事件等级",count:"聚合数量",srcIpv:"源IP",srcPort:"源端口",dstIpv:"目的IP",dstPort:"目的端口",aggrStartDate:"聚合开始时间",aggrEndDate:"聚合结束时间",deviceTypeName:"日志源设备",fromIpv:"发生源IP",protocol:"协议"},panel:{original:"原始日志查询",detail:"详情信息"}}},s={relevanceStrategy:{errorDelete:"不可以删除初始化数据",relevanceStrategy:"关联规则",table:{alarmName:"规则名称",alarmType:"规则分类",description:"规则描述",updateTime:"更新时间",isSysDefault:"系统内置",state:"使用状态",handle:"操作"},tag:{custom:"自定义",default:"内置"},add:{dialogTitle:"关联规则添加"},update:{dialogTitle:"关联规则修改"},button:{and:"&& 与",or:"|| 或",addRules:"新增关联状态",config:"配置"},tip:{length:"最多只能添加五个状态",column:"请先设置归并字段",one:"只能添加一个条件",data:"请为每个关联状态添加与或条件",empty:"配置条件不能为空",conditions:"请添加至少一条可用的关联条件",type:"前后条件类型不同"},title:{incPolicyName:"名称",description:"描述",advice:"处置建议",incSubCategoryID:"事件名称",externalSystem:"转发外系统",eventLevel:"事件等级",eventDesc:"事件文本"},upload:{downLoad:"下载模板",chooseFile:"请选择文件",exceed:"当前限制选择 1 个文件，请删除后再上传",remindTip:"当导入设备名称与系统设备名称相同时，以导入数据为准",talkTip:"当导入设备名称与系统设备名称相同时，以系统数据为准",successUpload:"操作完成，已成功导入{0}条数据!"}}},u={originalLes:{originalLes:"通讯视图",none:"暂无数据",logName:"原始日志类型",srcIP:"源IP",dstIP:"目的IP",queryTime:"时间范围",srcNum:"源IP关联数量",dstNum:"目的IP关联数量",placeholder:{srcStartIP:"源起始IP",srcEndIP:"源终止IP",dstStartIP:"目的起始IP",dstEndIP:"目的终止IP"}}},l={threat:{title:"威胁事件",domainName:"网站名称",eventIpv:"源IP",eventTime:"告警时间",receiveTime:"接收时间",eventType:"事件类型",eventLevel:"事件等级",eventDesc:"事件描述",startTime:"起始告警时间",endTime:"终止告警时间",strategy:{title:"威胁情报策略配置",strategySwitch:"策略是否开启",forwardService:"转发服务"},detailColumns:{type2Name:"原始事件名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"事件等级",srcIp:"源IP",dstIp:"目的IP",dateTime:"时间",raw:"日志原文"},panel:{original:"原始日志查询",detail:"详情信息"}}},m={fault:{title:"故障事件",faultId:"事件ID",faultName:"事件名称",currentStatus:"当前状态",faultStatus:"状态",faultClass:"事件类型",faultClassName:"事件类型",faultLevel:"事件等级",edName:"设备名称",monitorName:"监控器名称",domaName:"隶属区域",enterDate:"发生时间",updateDate:"更新时间",recoveryDate:"恢复时间",faultModule:"事件描述",faultSolution:"解决方案",basicDetail:"基本详情",faultDetail:"历史故障",handleSuggest:"处置意见",handleState:"处置状态",state:{normal:"正常",abnormal:"异常",handled:"已处置",unhandle:"未处置"}}},d={perf:{title:"性能事件",perfName:"事件名称",currentStatus:"当前状态",perfStatus:"状态",perfClass:"事件类型",perfClassName:"事件类型",perfLevel:"事件等级",edName:"设备名称",monitorName:"监控器名称",domaName:"隶属区域",enterDate:"发生时间",updateDate:"更新时间",recoveryDate:"恢复时间",perfModule:"事件描述",perfSolution:"解决方案",basicDetail:"基本详情",perfDetail:"历史性能",level:{high:"高",medium:"中",low:"低"}}},p={generalLog:{title:"通用日志",total:"总计",label:{deviceName:"设备类型",insertTime:"接收时间",facilityName:"模块",severity:"级别",severityName:"级别",fromIp:"发生源IP",appName:"应用名称",procId:"PROCID",msgId:"MSGID",logTimestamp:"日志时间 ",hostName:"主机名称",structuredData:"结构数据",message:"日志原文",logMessage:"日志内容"},placeholder:{message:"日志原文",severity:"级别",deviceType:"日志源设备",facility:"日志模块",fromIp:"发生源IP",fromStartIp:"发生源起始IP",fromEndIp:"发生源终止IP",receiveDate:"接收日期"},group:{basic:"通用日志详情"},basic:{deviceName:"设备类型",insertTime:"接收时间",facilityName:"模块",severityName:"级别",fromIp:"发生源IP",appName:"应用名称",procId:"PROCID",msgId:"MSGID",logTimestamp:"日志时间 ",hostName:"主机名称",structuredData:"结构数据",message:"日志原文",logMessage:"日志内容"},parseRateDesc:"每千条日志解析规则平均失败耗时{0}ms"}},h={customParse:{title:"自定义解析",label:{patternValue:"字段值",patternKey:"多元组",patternName:"多元组",pattern:"表达式",deviceType:"日志源设备",devTypeName:"日志源设备",status:"状态",message:"日志原文",patternInfo:"划词信息",createTime:"入库时间",propDetail:"字段值的明细：",multiGroup:"多元组"},step:{first:"基本信息",second:"日志划词",third:"生成解析"},placeholder:{patternValue:"字段值",patternKey:"多元组",deviceType:"日志源设备",status:"状态",fuzzyField:"字段值"},tip:{wordTranslation:"自定义解析规则",selectContent:"鼠标选取您选择的内容",inputCharValid:"字段中只能使用字母数字，如[a-zA-X0-9_]",validKeyword:"请重新划词,划词首尾不允许为空格。",repeatKeyword:"不允许重复划词，请重新选择。",selDevType:"请先选择设备类型后勾选特征值信息。",eventTypeRequired:"请在划词中选择特征值多元组，此信息项必填。",neighborNoSelected:"相邻划词不允许选中。",multiGroupRequired:"划词中缺少多元组必填属性，请继续划词。",multiGroupNotEmpty:"多元组不能为空"}}},f={customCode:{title:"事件特征值",label:{devType:"日志源设备",devTypeName:"日志源设备",code:"特征值",eventType:"事件类型",eventTypeName:"事件类型",updateTime:"入库时间"},placeholder:{devType:"日志源设备",code:"特征值",eventType:"事件类型",fuzzyField:"特征值"}}};t["default"]={event:Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c),s),i),u),l),m),d),p),h),f)}},8704:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={strategy:{title:"告警策略",table:{strategyName:"策略名称",description:"描述",createTime:"创建时间",state:"使用状态",handle:"操作"},label:{alarmName:"审计事件名称包含",targetIp:"对象IP包含",alarmType:"审计类型",alarmLevel:"审计事件等级",systemType:"日志源设备",snmpForwardServer:"转发外系统",mailTo:"外转邮箱",description:"备注"},placeholder:{inputSearch:"策略名称/描述"}}},o={table:{header:"告警列表",dialog:{colTitle:"告警列表自定义列",option:"可选择",detailTitle:"告警列表详情",reasonTitle:"告警列表确认"},label:{name:"审计名称",level:"事件级别",auditTypeName:"审计类型",alarmStrategyName:"告警策略",auditUser:"审计人员",state:"告警状态",total:"事件总数",createTime:"产生时间",updateTime:"更新时间",reason:"确认原因",handel:"操作"},detail:{detailOriginalColumn:{type2Name:"原始日志名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"事件等级",srcIp:"源IP",dstIp:"目的IP",dateTime:"时间",raw:"日志原文"},detailSafeColumn:{type2Name:"安全事件名称",alarmTypeName:"安全事件类型",alarmCategoryName:"安全事件类别",level:"事件等级",count:"聚合数量",aggrStartDate:"聚合开始时间",deviceTypeName:"日志源设备"},detailRelevanceColumn:{policyName:"策略名称",eventTypeName:"关联事件名称",level:"事件等级",createDate:"产生时间",updateDate:"更新时间",count:"次数"},detailThreatColumn:{eventTypeName:"威胁事件类型",eventLevel:"事件等级",eventDesc:"事件描述",receiveTime:"接收时间",eventTime:"告警时间"}},state:{done:"已确认",pending:"待处理"},panel:{detail:"详情信息",source:"事件溯源",original:"原始日志"}}},c={system:{header:"系统告警",table:{alarmName:"告警名称",alarmRole:"用户",isIgnore:"告警状态",enterDate:"发生时间",alarmDesc:"告警描述"},code:{status:{untreated:"未处理",ignore:"忽略"}}}},i={abnormalBehavior:{title:"异常行为告警",label:{infoSystemName:"信息系统名称",infoSystemIp:"信息系统IP",action:"行为名称",role:"入侵者",occurTime:"发生时间",updateTime:"更新时间",anomalyType:"异常类型",status:"状态",total:"数量",desc:"描述",raw:"日志原文",occurStartTime:"发生开始时间",occurEndTime:"发生结束时间",updateStartTime:"更新开始时间",updateEndTime:"更新结束时间"}}};t["default"]={alarm:Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c),i)}},"8e1f":function(e,t,n){},"8f30":function(e,t,n){"use strict";n.r(t),t["default"]={time:{unit:{millisecond:"毫秒",second:"秒",minute:"分钟",hour:"小时",day:"日",week:"周",month:"月",quarter:"季度",halfYear:"半年",year:"年",times:"次"},cycle:{immediate:"立即执行",second:"每秒",minute:"每分",hour:"每小时",day:"每日",week:"每周",month:"每月",quarter:"每季度",halfYear:"每半年",year:"每年"},week:{sun:"周日",mon:"周一",tue:"周二",wed:"周三",thu:"周四",fri:"周五",sat:"周六"},option:{date:"日期",startDate:"开始日期",updateDate:"更新日期",endDate:"结束日期",executeDate:"执行日期",time:"时间",startTime:"开始时间",endTime:"结束时间",updateTime:"更新时间",dateRange:"时间范围",executeTime:"执行时间",startUpdateTime:"更新时间起",endUpdateTime:"更新时间止",startCreateTime:"产生时间起",endCreateTime:"产生时间止",until:"至",day:"最近一天",week:"最近一周",month:"最近一个月",quarter:"最近三个月"},many:{millisecond:"第{0}毫秒",second:"第{0}秒",minute:"第{0}分钟",hour:"第{0}小时",day:"第{0}日",week:"第{0}周",month:"第{0}月",quarter:"第{0}季度",halfYear:"第{0}半年",year:"第{0}年",times:"第{0}次"}}}},9:function(e,t){},"921c":function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return o}));n("4160"),n("baa5"),n("13d5"),n("d3b7"),n("ac1f"),n("5319"),n("159b"),n("ddb0");function a(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4?arguments[4]:void 0,o=[];try{var c=function n(a){if(o.push(a),a[t]===e)throw o;a.children&&a.children.length>0?(a.children.forEach((function(e){n(e)})),o.pop()):o.pop()};n.forEach((function(e){c(e)}))}catch(u){var i=[],s=[];return u.forEach((function(e){r?i.push(e[r]):i.push(e[t])})),s=a?i:u,s}}function r(e){var t=[],n=function e(n){n&&n.length>0&&(t.push(n[0]),n[0].children&&e(n[0].children))};return n(e),t}function o(e){return e.keys().reduce((function(t,n){var a=n.replace(/^\.\/(.*)\.\w+$/,"$1"),r=e(n);return r.default&&(t[a]=r.default),t}),{})}},9221:function(e,t,n){},"931d":function(e,t,n){"use strict";n.r(t),t["default"]={ajax:{success:"访问成功",exception:{system:"系统异常",server:"服务故障异常",session:"Session异常",access:"访问令牌异常",certification:"认证异常",auth:"访问资源权限异常",token:"访问资源功能权限异常",param:"您输入的内容有误，请检查后重新输入",idempotency:"幂等性异常",ip:"IP禁止访问",upload:"上传附件格式校验失败",code:"未检测到有效的系统返回码",mock:"缺失对应api的mock数据，请完善"},attack:{xss:"xss脚本攻击"},interaction:{error:"API异常，请检查前后端API是否正确"},interceptors:{error:"请求拦截器拦截失败"},service:{upload:"上传文件失败，可能原因文件被修改或服务器连接失败",timeout:"检测到您未连接到有效API或者连接服务器超时"}}}},"97c0":function(e,t,n){var a={"./ajax.js":"931d","./button.js":"d021","./code-transform.js":"7cc7","./code.js":"ee2a","./dialog.js":"2af1","./expression-autocomplete.js":"c733","./index.js":"9d04","./level.js":"5aa2","./system.js":"4bea","./time.js":"8f30","./tip.js":"fc0a","./validate.js":"0378"};function r(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=o,e.exports=r,r.id="97c0"},"99a9":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"ReportInstance",path:"/report/instance",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-618f87fa")]).then(n.bind(null,"5e19"))}},{name:"ReportTask",path:"/report/task",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-85cf8d6e")]).then(n.bind(null,"f488"))}},{name:"ReportShow",path:"/report/show",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-20f1c03d"),n.e("chunk-587047f1")]).then(n.bind(null,"ff1c"))}}]},"9c0c":function(e,t,n){},"9c7e":function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"ForecastAnalysis",path:"/forecast/forecast-analysis",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-417e7c69")]).then(n.bind(null,"e998"))}},{name:"ForecastAlarm",path:"/forecast/forecast-alarm",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-53a3c8d6")]).then(n.bind(null,"8cfa"))}}]},"9cf1":function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={event:{event:"审计事件",eventFrom:"事件溯源",log:"原始日志",id:"审计事件ID",eventName:"审计事件名称",level:"事件等级",auditTypeName:"审计类型",policyId:"审计策略ID",auditStrategyName:"审计策略",auditUserId:"审计人员ID",auditUser:"审计人员",auditIp:"审计对象IP",auditIpStart:"起始IP",auditIpEnd:"结束IP",total:"事件总数",createTime:"产生时间",createTimeStart:"起始产生时间",createTimeEnd:"结束产生时间",updateTime:"更新时间",secId:"安全事件ID",allCheck:"全选",none:"暂无数据",safe:{type2Name:"安全事件名称",safeEventName:"安全事件类型",eventTypeName:"安全事件类别",eventLevelName:"事件等级",srcIP:"事件源IP",srcPort:"事件源端口",dstIP:"事件目的IP",dstPort:"事件目的端口",dstAssetName:"目的设备名称",count:"聚合数量",aggrStartDate:"聚合开始时间",fromDeviceTypeName:"发生源设备类型"},link:{eventName:"关联事件名称",policyName:"策略名称",eventLevelName:"事件等级",createDate:"产生时间",updateDate:"更新时间",count:"次数"},threat:{eventType:"威胁事件类型",eventLevel:"事件等级",eventDesc:"事件描述",receiveTime:"接收时间",eventTime:"告警时间"},logList:{type2Name:"原始日志名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"等级",srcIp:"源IP",dstIp:"目的IP",dateTime:"时间"},placeholder:{inputVal:"请输入关键字搜索",eventName:"审计事件名称",level:"事件等级",auditType:"审计类型",auditUserId:"审计人员",policyId:"审计策略"}}},o={person:{person:"审计人员",accountName:"账号名称",level:"事件级别",remark:"描述",type:"审计事件类型",group:"审计组",groupList:"审计组列表",groupName:"审计组名称",placeholder:{queryInput:"请输入关键字搜索",groupName:"审计组名称",remark:"描述",groupId:"审计组",accountName:"帐号名称",typeId:"类型",level:"事件级别"},emptyText:"请添加审计组"}},c={strategy:{strategy:"审计策略",orderNumber:"顺序",safeEvent:"安全事件",linkEvent:"关联事件",threatEvent:"威胁事件",day:"每日",week:"星期",dater:"日期",policyName:"策略名称",outputEventRemark:"策略描述",systemOwn:{title:"系统内置",own:"内置",write:"自定义"},state:"使用状态",states:{on:"启用",off:"停用"},date:"时间段",eventType:"事件",dateType:"审计有效时间段",startTime:"开始时间",startDate:"开始日期",endDate:"终止日期",endTime:"终止时间",weekStart:"星期开始",weekEnd:"星期结束",outputEventName:"审计事件名称",outputEventLevel:"审计事件级别",flag:"审计事件类型",outputEventType:"审计类型",auditUser:"审计人员",forwardSystemId:"转发外系统",forwardAudit:"转发内容",forwardAuditEvent:"审计事件",placeholder:{placeholder:"请选择",inputVal:"请输入关键字搜索",policyName:"策略名称",state:"使用状态",eventTypeList:"事件",outputEventName:"审计事件名称",outputEventLevel:"事件级别",outputEventType:"审计类型",auditUser:"审计人员",outputEventRemark:"策略描述",forwardSystemId:"转发外系统",time:"星期起始日"},weekList:{mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六",sun:"星期日"}}},i={type:{Id:"审计类型ID",type:"审计类型名称",remark:"类型描述",typeName:"审计类型",placeholder:{queryInput:"请输入关键字搜索",type:"审计类型名称",remark:"描述"},delete:{strategy:"审计策略正在使用此类型，请停用后再删除",alarm:"告警策略正在使用此类型，请停用后再删除",person:"审计人员正在使用此类型，请停用后再删除"}}},s={behaviorStrategy:{title:"异常行为策略",label:{legalIp:"合法IP",systemName:"信息系统名称",systemIp:"信息系统IP",abnormalAction:"异常行为",abnormalActionStr:"异常行为",status:"状态",timeRange:"检测时间",startTime:"开始时间",endTime:"结束时间",externalSystem:"转发外系统",externalMail:"外转邮箱"},placeholder:{legalIp:"合法IP",systemName:"信息系统名称",systemIp:"信息系统IP",abnormalAction:"异常行为",status:"状态",startTime:"开始时间",endTime:"结束时间",fuzzyField:"信息系统名称/IP"}}};t["default"]={audit:Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c),i),s)}},"9d04":function(e,t,n){"use strict";n.r(t);n("4160"),n("1bf2"),n("159b");var a=n("f3f3"),r=n("921c"),o=function(e){var t=Object(r["b"])(e),n={};return Reflect.ownKeys(t).forEach((function(e){Object.assign(n,t[e])})),n},c=o(n("97c0"));t["default"]=Object(a["a"])({},c)},a18c:function(e,t,n){"use strict";n.d(t,"c",(function(){return l})),n.d(t,"b",(function(){return m}));n("99af"),n("4160"),n("c975"),n("d3b7"),n("07ac"),n("159b");var a=n("d0ff"),r=n("2b0e"),o=n("8c4f"),c=n("921c"),i=[{name:"VisualizationEarth",path:"/visualization/earth",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-3e856590"),n.e("chunk-3b0ba5dc"),n.e("chunk-070e19dc")]).then(n.bind(null,"05c0"))}},{name:"SituationAwareness",path:"/visualization/situation",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-3e856590"),n.e("chunk-617e3305"),n.e("chunk-751ec47a")]).then(n.bind(null,"58c4"))}}];r["default"].use(o["a"]);var s=Object(c["b"])(n("c9c2")),u=[];Object.values(s).forEach((function(e){-1===u.indexOf(e)&&u.push.apply(u,Object(a["a"])(e))}));var l=[{path:"/",redirect:"/layout"},{name:"mailTo",path:"/login",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-2d0d6345"),n.e("chunk-673ca78e")]).then(n.bind(null,"d5d1"))}},{name:"Page404",path:"/404",component:function(){return n.e("chunk-ce7f7a88").then(n.bind(null,"fbea"))}},{name:"Page401",path:"/401",component:function(){return n.e("chunk-ff4da0ac").then(n.bind(null,"0aa8"))}},{name:"Layout",path:"/layout",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-2d0d6345"),n.e("chunk-e61720e2")]).then(n.bind(null,"f826"))},children:[].concat(u)}].concat(Object(a["a"])(i),[{path:"*",redirect:"/404",hidden:!0}]),m=[],d=function(){return new o["a"]({mode:"history",base:"/",scrollBehavior:function(){return{y:0}},routes:l})},p=d();t["a"]=p},a47e:function(e,t,n){"use strict";n("4160"),n("1bf2"),n("159b");var a=n("f3f3"),r=n("2b0e"),o=n("a925"),c=n("b2d6"),i=n.n(c),s=n("f0d9"),u=n.n(s),l=n("c87b"),m=n.n(l),d=n("921c");r["default"].use(o["a"]);var p=function(e){var t=Object(d["b"])(e),n={};return Reflect.ownKeys(t).forEach((function(e){Object.assign(n,t[e])})),n},h=p(n("fc0e")),f={en:Object(a["a"])({},i.a),"zh-CN":Object(a["a"])(Object(a["a"])({},h),u.a),"zh-TW":Object(a["a"])({},m.a)},b=new o["a"]({locale:"zh-CN",messages:f});t["a"]=b},a56c:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"SubordinateList",path:"/platform/subordinate-list",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-2d7160bf")]).then(n.bind(null,"8a4d"))}}]},a698:function(e,t,n){},a6a3:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={networkManagement:{name:"名称",domainName:"IP类型",access:"访问控制",remark:"描述",startIP:"起始IP",endIP:"终止IP",allIpv:"IP范围",net:"网络管理",placeholder:{queryInput:"请输入关键字搜索",name:"名称",Ipv:"IP地址",remark:"描述",mac:"Mac地址"},update:{ipRepeat:"ip地址范围存在重合，请重新填写",aroundError:"IP范围错误，终止IP应大于起始IP",ipAround:"IP区间不能超过120！",error:"修改失败",nameRepeat:"名称重复"}}},o={deviceMonitor:{operate:"设备名称",careTip:"请选择关注设备或者取消关注的设备。",selectTip:"请选择一个设备或者一个车间。",selectTip1:"只能选择设备或者车间。",selectTip2:"只能选择1个车间的设备。",selectTip3:"只能选择1个设备。",selectTip4:"安全策略偏离率，异常阈值必须大于提醒阈值。",selectTip5:"系统配置偏离率，异常阈值必须大于提醒阈值。",saveSuccess:"保存成功。",contrastTip:"请选择两个设备进行比较。",sort:{first:"至于最前",last:"至于最后",moveForwardOne:"前移一位",moveBackOne:"后移一位"},tab:{device:"设备监控",monitor:"监控设置"},state:{normal:"正常",warn:"提醒",abnormal:"异常"},info:{systemInfo:"系统信息",interfaceInfo:"接口信息",securityPolicy:"安全策略",systemConfig:"系统配置"}}},c={deviceUpdate:{formatTip:"文件格式错误",placeholder:{card:"请选择板卡",fileType:"请选择文件类型"},tab:{update:"升级文件",notice:"升级通知"},updateTable:{fileName:"文件名称",inOutCard:"内外侧板",fileType:"文件类型",version:"版本",deviceType:"设备类型",fileSize:"文件大小",updateTime:"上传时间"},noticeTable:{startTime:"执行开始时间",fileName:"文件名称",version:"版本号",inOutCard:"内外侧板",fileType:"文件类型",executeMethod:"执行方式",executeStatus:"执行状态",executeProgress:"执行进度",noticeRange:"通知范围"},noticeTableRange:{deviceName:"设备名称",inOutCard:"内外侧板",ipAddress:"IP地址",noticeResult:"通知结果"},dialog:{pushUpdate:"推送升级",edit:"编辑"},uploadPush:{deviceNum:"设备数量",executeTime:"执行时间",startTime:"开始时间",executeMethod:"执行方式",executeFrequency:"执行频率",freeExecute:"空闲时执行"}}},i={type:{deviceTypeName:"设备二级分类",deviceClassName:"设备一级分类",type:"设备类型",placeholder:{queryInput:"请输入关键字搜索",deviceClass:"设备一级分类",deviceTypeName:"设备二级分类"},delete:{ininterror:"初始化数据不可以删除",management:"设备管理正在使用此数据，请停用后删除",custom:"设备属性正在使用此数据，请停用后删除"}}},s={management:{asset:"设备管理",baseInfo:"基本信息",moreInfo:"扩展信息",noneInfo:"暂无扩展属性",logInfo:"日志信息",monitorInfo:"监控信息",faultEvent:"故障事件",perfEvent:"性能事件",ipArrayInfo:"IP范围不能超过255",th:"自定义列",allCheck:"全选",assetCode:"设备编号",assetName:"设备名称",assetType:"设备类型",assetTypeName:"设备类型",responsiblePerson:"负责人",startTime:"起始时间",endTime:"终止时间",netWorkId:"网段",netWorkName:"网段",assetModel:"设备型号",manufactor:"制造商",osType:"操作系统",ipvAddress:"设备IP",startIP:"起始设备IP",endIP:"终止设备IP",memoryInfo:"内存容量",contactPhone:"联系电话",email:"邮件地址",makerContactPhone:"厂商电话",domaName:"隶属区域",securityComponent:"安全组件类型",riskRating:"风险等级",assetValue:"设备权值",assetDesc:"备注信息",exceed:"当前限制选择 1 个文件，请删除后再上传",successUpload:"操作完成，已成功导入{0}条数据!",rebase:"回归",log:{logSourceInfo:"日志源信息",logTotal:"日志接收总数",logDuration:"日志存储时长",logTrend:"日志采集趋势",originalLog:"原始日志",generalLog:"通用日志",logAccessTime:"日志接收时间",logLatestTime:"最新日志时间",type2Name:"原始日志名称",eventName:"事件类型",eventCategoryName:"事件类别",level:"事件等级",deviceCategoryName:"设备类别",deviceTypeName:"设备类型",time:"日志接收时间",logTime:"日志时间",code:"特征值",eventDesc:"事件描述",fromIp:"发生源IP"},columns:{assetName:"设备名称",assetType:"设备类型",importance:"重要程度",assetTypeName:"设备类型",netWorkId:"网段",netWorkName:"网段",assetModel:"设备型号",manufactor:"制造商",osType:"操作系统",memoryInfo:"内存容量",contactPhone:"联系电话",email:"邮件地址",makerContactPhone:"厂商电话",assetDesc:"备注信息",ipvAddress:"设备IP",in:"内侧板",out:"外侧板",inIpAddr:"内侧板IP",outIpAddr:"外侧板IP",macIn:"内侧板MAC",macOut:"外侧板MAC",accessTimeIn:"内侧板接入时间",accessTimeOut:"外侧板接入时间",authSerial:"序列号",sysVersion:"系统版本",authStateDesc:"授权状态",authState:"授权状态",inAppVersion:"内侧板系统版本",outAppVersion:"外侧板系统版本",domaName:"隶属区域",responsiblePerson:"负责人",assetValueDesc:"重要程度",assetCode:"设备编号",inCreateDate:"内侧板接入时间",outCreateDate:"外侧板接入时间",inNetworkName:"内侧网段",outNetworkName:"外侧网段",macAddr:"设备MAC",devId:"设备标识",appVersion:"应用版本",authCode:"授权码",serialNumber:"设备序列号",devTag:"内外板卡",devTagName:"内外板卡",securityComponent:"安全组件类型",riskRating:"风险等级",uid:"唯一标识",inout:"内/外侧板",baselineType:"基线类型",baselineType1:"安全策略",baselineType2:"系统配置",createDate:"建档时间",domainName:"来源平台"},placeholder:{info:"请补充扩展信息",selectPlace:"请选择",time:"选择日期时间",keyword:"请输入设备名称/设备IP",ipvAddress:"设备IP",manufactor:"制造商",responsiblePerson:"负责人",assetArr:"设备分类",assetName:"设备名称",netWorkId:"网段",devTag:"内外板卡",assetModel:"设备型号",osType:"操作系统",memoryInfo:"内存容量",contactPhone:"联系电话",email:"邮件地址",makerContactPhone:"厂商电话",assetCode:"设备编号",domaName:"隶属区域",domaId:"隶属区域",securityComponent:"安全组件类型",assetDesc:"备注信息",startIP:"起始设备IP",endIP:"终止设备IP",riskRating:"风险等级"},importRule:{new:"覆盖规则",old:"忽略规则"},downLoad:"下载模板",chooseFile:"请选择文件",uploadRemind:"当导入设备名称与系统设备名称相同时，以导入数据为准",uploadTalk:"当导入设备名称与系统设备名称相同时，以系统数据为准",originalLog:{title:"原始日志",eventName:"事件名称",level:"事件等级",sourceIp:"源IP",targetIp:"目的IP",time:"日志接收时间"}}},u={discover:{asset:"设备发现",findTask:"发现任务",taskLog:"任务日志",th:"自定义列",errorConnect:"连接中断,暂停获取进度",assetCode:"设备编号",domaName:"隶属区域",assetName:"设备名称",assetType:"设备类型",responsiblePerson:"负责人",netWorkId:"网段ID",netWorkName:"网段",assetModel:"设备型号",manufactor:"制造商",osType:"操作系统",ipvAddress:"设备IP",memoryInfo:"内存容量",contactPhone:"联系电话",email:"邮件地址",makerContactPhone:"厂商电话",assetDesc:"备注信息",securityComponent:"安全组件类型",columns:{assetName:"设备名称",assetType:"设备类型",netWorkId:"网段",assetModel:"设备型号",manufactor:"制造商",osType:"操作系统",memoryInfo:"内存容量",responsiblePerson:"负责人",contactPhone:"联系电话",email:"邮件地址",makerContactPhone:"厂商电话",assetCode:"设备编号",assetDesc:"备注信息",ipvAddress:"设备IP"},taskName:"任务名称",progressPercent:"状态",placeholder:{startIpv:"起始设备IP",endIpv:"终止设备IP",keyword:"请输入关键字搜索",taskName:"任务名称",netWorkId:"网段",assetName:"设备名称",assetType:"设备分类",assetModel:"设备型号",manufactor:"制造商",osType:"操作系统",ipvAddress:"设备IP",memoryInfo:"内存容量",responsiblePerson:"负责人",contactPhone:"联系电话",email:"邮件地址",makerContactPhone:"厂商电话",assetCode:"设备编号",domaName:"隶属区域",assetDesc:"备注信息"}}},l={custom:{validate:{yes:"是",no:"否"},noDictionary:"无字典",custom:"自定义设备属性",attributeId:"属性ID",attributeName:"属性名称",assetClass:"设备类型",newDic:"新增字典",assetClassName:"设备一级分类",assetTypeName:"设备二级分类",remark:"描述",controlType:"控件类型",dictionary:"设备字典",attributeLength:"文本长度",checkType:"是否多选",reqType:"是否必填",gridType:"列表展示",dicName:"字典名称",placeholder:{inputVal:"属性名称/描述",attributeName:"属性名称",assetClass:"设备类型",controlType:"控件类型",attributeLength:"文本长度",reqType:"是否必填",checkType:"是否多选",remark:"描述",dicName:"字典名称"},control:{text:"文本控件",select:"下拉控件",timer:"时间控件",textarea:"文本域控件",radio:"单选控件",checkBox:"多选控件"}}},m={dictionary:{id:"设备字典ID",dictionary:"设备字典",dicName:"设备字典名称",remark:"设备字典描述",placeholder:{queryInput:"请输入关键字搜索",attributeId:"自定义属性",dicName:"设备字典名称",remark:"设备字典描述"}}},d={area:{header:"区域管理",prop:{domaName:"区域名称",domaAbbr:"简称",officeTel:"办公电话",email:"电子邮箱",domaFunc:"职能",domaAddress:"地址",domaMemo:"备注",deviceNum:"设备数量"}}};t["default"]={asset:Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),i),s),u),l),m),d),o),c)}},a6c6:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={dashboard:{dropdown:{name:"仪表盘名称",refresh:"刷新",manual:"手动",minute:"分钟",operation:"操作",create:"新建仪表盘",edit:"编辑仪表盘"},dialog:{name:"仪表盘名称",layout:"布局",height:"高度",validate:{layout:"请至少选择一种布局"},comp:{label:"数据组件",type:"图表形态",axis:"轴向",formatType:"格式化类型",unit:"单位",fontSize:"字体大小",fontColor:"字体颜色",fontWeight:"字体粗细",align:"对齐方式",placeholder:"占位符",chartName:"图表名称",chartProperty:"图表属性",conditionConfig:"条件配置",compType:"组件类型"}},align:{center:"居中",left:"居左",right:"居右"},fontWeight:{thin:"细体",normal:"标准",bold:"粗体"},tip:{name:"仪表盘名称不能为空",layout:"请至少保留一种布局",empty:{data:"请添加数据组件",component:"请点击编辑仪表盘，添加数据组件",dashboard:"请新建仪表盘"},sort:"生成布局顺序与点击顺序相同",add:{layout:"添加布局",chart:"数据组件"}},placeholder:{startIP:"起始{0}",endIP:"终止{0}"}}},o={event:{audit:{label:{trend:"审计趋势",type:"审计类型",level:"审计等级",amount:"审计数量"}},security:{label:{trend:"安全事件趋势",type:"安全事件类别",level:"安全事件等级",name:"安全事件类型"}}}},c={management:{title:"大屏管理",table:{index:"索引",name:"大屏名称",url:"大屏路径",description:"大屏描述"},fuzzyQuery:"大屏名称/大屏路径"}},i={compliance:{title:{logSourceNumber:"日志源设备数",logTotalNumber:"日志接收总数",logStorageSpace:"日志存储空间",logStorageDuration:"日志存储时长",systemHealthy:"系统运行状况",logNumberTrend:"日志采集趋势",logSourceType:"日志源设备类型",devLogNumber:"日志源日志数量",devLogNumberTop:"日志源日志数量TOP10",devStorageDuration:"日志源存储时长",devStorageDurationTop:"日志源存储时长TOP10（单位：天）",logReceivingStatus:"日志源接收状态",auditAlarmTread:"审计告警数量趋势",securityEventCount:"安全事件类别数量",securityEventCountTop:"安全事件类别数量Top10"},label:{logSourceName:"日志源名称",count:"数量",storageDuration:"存储时长（天）",logSourceTotal:"日志源总数{0}",grantTotal:"占授权总数{0}",diskTotalSpace:"占磁盘总空间{0}"},empty:{component:"暂无数据"},receiving:{logSourceName:"日志源名称",status:"状态",durationHours:"24h",durationAll:"all"}}},s={actualTimeLog:{title:{attentionList:"关注清单",ignoreDesc:"实时日志监控列表忽略条件配置（多条件间关系为or）",attentionTrack:"关注跟踪",ignoreFilter:"忽略过滤",logTrend:"实时日志趋势（2min）"},label:{eventType:"事件类型",eventTypeName:"事件类型名称",logName:"日志名称",eventCategory:"事件类别",eventCategoryName:"事件类别名称",srcIp:"源IP",srcPort:"源端口",dstIp:"目的IP",dstPort:"目的端口",deviceType:"设备类型",deviceTypeName:"设备类型",fromIp:"日志源IP",level:"事件等级",levelName:"事件等级",timestamp:"时间戳",receiveTime:"接收时间",addIgnore:"新增忽略"},item:{logName:"日志名称",srcIp:"源IP",srcPort:"源端口",dstIp:"目的IP",dstPort:"目的端口",fromIp:"日志源IP"},empty:{component:"暂无数据"}}};t["default"]={visualization:Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c),i),s)}},a6d8:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{path:"/firewall/device/list",name:"FirewallDeviceList",component:function(){return n.e("chunk-78519dfb").then(n.bind(null,"6637"))},meta:{title:"设备列表",icon:"soc-icon-point"}},{path:"/firewall/device/groupmanagement",name:"FirewallGroupManage",component:function(){return n.e("chunk-b30294b4").then(n.bind(null,"bba6"))},meta:{title:"分组管理",icon:"soc-icon-point"}},{path:"/firewall/device/inspectionmanagement",name:"FirewallInspectionManage",component:function(){return n.e("chunk-5441f3e0").then(n.bind(null,"ddb2"))},meta:{title:"巡检管理",icon:"soc-icon-point"}},{path:"/firewall/auth/manage",name:"FirewallAuthManage",component:function(){return n.e("chunk-63d74eb1").then(n.bind(null,"ec3b"))},meta:{title:"授权管理",icon:"soc-icon-point"}},{path:"/firewall/backup/restore",name:"FirewallBackupRestore",component:function(){return Promise.all([n.e("chunk-9e9c9b90"),n.e("chunk-191ef07e")]).then(n.bind(null,"977b"))},meta:{title:"备份还原",icon:"soc-icon-point"}},{path:"/firewall/upgrade/manage",name:"FirewallUpgradeManage",component:function(){return n.e("chunk-623a88d6").then(n.bind(null,"4d3a"))},meta:{title:"升级管理",icon:"soc-icon-point"}},{path:"/firewall/protocol/set",name:"FirewallProtocolSet",component:function(){return n.e("chunk-77a2f85e").then(n.bind(null,"ff03"))},meta:{title:"工控协议集",icon:"soc-icon-point"}},{path:"/firewall/service/set",name:"FirewallServiceSet",component:function(){return n.e("chunk-3728b973").then(n.bind(null,"d54a"))},meta:{title:"服务集",icon:"soc-icon-point"}},{path:"/firewall/address/set",name:"FirewallAddressSet",component:function(){return n.e("chunk-2054971b").then(n.bind(null,"3b01"))},meta:{title:"地址集",icon:"soc-icon-point"}},{path:"/firewall/strategy/manage",name:"FirewallStrategyManage",component:function(){return n.e("chunk-21686e72").then(n.bind(null,"a83f"))},meta:{title:"策略管理",icon:"soc-icon-point"}},{path:"/firewall/strategy-record",name:"FirewallStrategyRecord",component:function(){return n.e("chunk-5d3d1775").then(n.bind(null,"9985"))},meta:{title:"策略记录",icon:"soc-icon-point"}}]},b0fd:function(e,t,n){var a={"./dark/element-ui/index.scss":"3196","./light/element-ui/index.scss":"a698"};function r(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=o,e.exports=r,r.id="b0fd"},c733:function(e,t,n){"use strict";n.r(t),t["default"]={expressionAutocomplete:{placeholder:"请输入表达式，如 t.ip1@!=:******** & (t.device@in:100,101 | t.raw@like:服务器)"}}},c9c2:function(e,t,n){var a={"./alarm.js":"cf49","./asset.js":"e777","./audit.js":"6cf0","./auditold.js":"81d2","./collector.js":"e8b1","./demo.js":"6b7d","./event.js":"db37","./firewall.js":"a6d8","./forecast.js":"9c7e","./guard.js":"808d","./hostguardian.js":"076b","./management.js":"fdc4","./monitor.js":"8225","./platform.js":"a56c","./report.js":"99a9","./repository.js":"d6f8","./visualization.js":"048b"};function r(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=o,e.exports=r,r.id="c9c2"},cd0e:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"n",(function(){return o})),n.d(t,"d",(function(){return c})),n.d(t,"i",(function(){return i})),n.d(t,"k",(function(){return s})),n.d(t,"l",(function(){return u})),n.d(t,"m",(function(){return l})),n.d(t,"a",(function(){return m})),n.d(t,"j",(function(){return d})),n.d(t,"h",(function(){return p})),n.d(t,"e",(function(){return h})),n.d(t,"b",(function(){return f})),n.d(t,"g",(function(){return b})),n.d(t,"f",(function(){return g}));var a=n("4020");function r(){return Object(a["a"])({url:"/authentication/logout",method:"get"})}function o(e){return Object(a["a"])({url:"/usermanagement/password",method:"put",data:e||{}})}function c(){return Object(a["a"])({url:"/menumanagement/menu/navigation",method:"get"})}function i(){return Object(a["a"])({url:"/actuator/home",method:"get"})}function s(){return Object(a["a"])({url:"/menumanagement/menu/tools",method:"get"})}function u(){return Object(a["a"])({url:"/usermanagement/user/extend",method:"get"})}function l(e){return Object(a["a"])({url:"/usermanagement/user/extend",method:"put",data:e||{}})}function m(){return Object(a["a"])({url:"/event/alarm/total",method:"get"})}function d(){return Object(a["a"])({url:"/systemmanagement/find-system-alarm-notice",method:"get"})}function p(){return Object(a["a"])({url:"/systemalarm/findNotIgnoreAlarm",method:"get"})}function h(){return Object(a["a"])({url:"/onsiteNotice/queryNotReadNotice",method:"get"})}function f(){return Object(a["a"])({url:"/systemmanagement/license/remain",method:"get"})}function b(){return Object(a["a"])({url:"/menumanagement/menu/search",method:"get"})}function g(){return Object(a["a"])({url:"/usermanagement/queryPermissions",method:"get"})}},cf49:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"AlarmTable",path:"/alarm/table",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-141018f2")]).then(n.bind(null,"47d5"))}},{name:"AlarmStrategy",path:"/alarm/strategy",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-18f93af8")]).then(n.bind(null,"c8f9"))}},{name:"SystemAlarm",path:"/alarm/system",component:function(){return n.e("chunk-15868997").then(n.bind(null,"5c59"))}},{name:"MonitorAlarm",path:"/alarm/monitor",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-3e856590"),n.e("chunk-786d67b6")]).then(n.bind(null,"ac7f"))}},{name:"OnSiteNotice",path:"/alarm/notice",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9065f8e0")]).then(n.bind(null,"7356"))}},{name:"AbnormalBehavior",path:"/alarm/abnormal-behavior",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-25b059ae")]).then(n.bind(null,"d023"))}}]},d021:function(e,t,n){"use strict";n.r(t);n("1c46");t["default"]={button:{build:"构建",on:"启用",off:"禁用",detail:"详情",confirm:"确认",determine:"确定",cancel:"取消",login:"登录",logout:"注销",add:"添加",change:"转化",update:"修改",edit:"编辑",delete:"删除",query:"查询",correct:"纠错",verify:"核查",operate:"操作",check:"手动检测",checkedAll:"全选",refresh:"刷新",save:"保存",submit:"提交",close:"关闭",lock:"锁定",unlock:"解锁",download:"下载",upload:"上传",packageUpload:"解析包上传",choseFile:"选择文件",import:"导入",sync:"同步",repeat:"撤销",config:"设置",recovery:"恢复",dataRecovery:"数据恢复",stop:"停止",ignore:"忽略",scan:"扫描",th:"自定义列",nowFind:"立即发现",find:"自动发现",workflow:"生成工单",show:"显示",hide:"隐藏",grant:"授权",clear:"清空",title:"标题",return:"返回",previous:"上一步",next:"下一步",newIp:"新建IP范围",threatenConfig:"威胁情报源配置",look:"查看",test:"测试",dictionary:"字典",log:"原文",restart:"重启",restore:"恢复出厂设置",shutdown:"关机",expression:"表达式",strategyConfig:"策略配置",handle:"处置",forward:"日志转发",copy:"复制",customQuery:"自定义查询",allConfirm:"全部确认",insert:"新增行",back:"返回",followOrNoFllow:"关注/取关",sort:"排序",contrast:"对比",pushUpdate:"推送升级",start:"启动",search:{high:"高级搜索",exact:"精准查询",advance:"高级查询"},reset:{default:"重置",password:"重置密码"},backups:{config:"备份设置"},move:{up:"上移",down:"下移"},batchText:"批量操作",batch:{add:"批量添加",update:"批量修改",delete:"批量删除",stop:"批量停用",grant:"批量授权",ignore:"批量忽略",change:"批量转化",run:"批量启用"},export:{default:"导出",excel:"导出EXCEL",pdf:"导出PDF",word:"导出WORD",xml:"导出XML",image:"导出图片",text:"导出文件",custom:"自定义导出"},toggle:{run:"运行",stop:"暂停",end:"终止"},model:{run:"模型已运行",stop:"模型已暂停",result:"模型结果"},view:"监控展示",logImport:"日志导入",nowExecute:"立即执行",detect:"一键检测",networkConfig:"配置",centerIp:"设置中心IP",generateParse:"生成解析",logView:"日志",emailDetermine:"发送验证邮件"}}},d385:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={instance:{name:"报表实例",table:{name:"实例名称",type:"实例类型",amount:"实例数量"},dialog:{title:"任务实例预览"}}},o={task:{name:"报表任务",label:{name:"任务名称",type:"任务类型",date:"更新时间",status:"使用状态",sendType:"发送类型",recipient:"收件人",description:"描述",instance:"实例选择"},tip:{delete:"删除之后对应的报表实例将不可找回，确定将选中的删除吗？"}}},c={show:{title:"报表管理",asset:{reportName:"设备信息报表",assetType:"设备类型",datetime:"查询时间",startTime:"开始时间",endTime:"结束时间"},comprehensive:{reportName:"综合报表",datetime:"查询时间",startTime:"开始时间",endTime:"结束时间"},dialog:{title:"报表预览"}}};t["default"]={report:Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c)}},d3dd:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={management:{header:"日志源管理",table:{collectorName:"采集器名称",ip:"采集地址",protName:"接入方式",agentIp:"代理服务器",agentStatus:"代理状态",runState:"使用状态",run:"启用停用",handle:"操作",typeName:"日志源设备",fuzzyField:"采集器名称/日志源设备"},dialog:{title:{add:"日志源管理添加",update:"日志源管理修改",logSourceAdd:"日志源类型添加"},columns:{collectorName:"采集器名称",innerType:"接入方式",agentIp:"代理服务器",logType:"日志类型",ip:"采集地址",kafkaAddress:"Kafka地址",propertyKind:"日志源设备",describe:"描述",dbType:"数据库类型",dbInst:"数据库实例",port:"端口",userName:"用户名",password:"密码",domain:"域名",strategy:"过滤策略",codeWay:"编码格式",logFile:"日志文件",importConfig:"导入配置",historyImport:"历史导入",ipAddress:"采集地址",fileName:"文件名称",createTime:"导入时间",directory:"目录",topic:"主题",isAsset:"生成设备"},exceed:"当前限制选择 1 个文件，请删除后再上传"},placeholder:{input:"采集器名称/采集地址/接入方式",innerType:"接入方式",agentIp:"代理服务器",logType:"日志类型",runState:"运行状态",run:"启用",propertyKind:"日志源设备",strategy:"过滤策略",dbType:"数据库类型",grabCycle:"抓取时间",codeWay:"编码格式",logFile:"日志文件"},label:{runSuccess:"请选择批量开启内容",runError:"请选择批量停止内容",runState:{on:"正在运行",off:"暂停"},agentStatus:{on:"在线",off:"下线"},useState:{on:"启用",off:"停用"}},error:{collectorName:"采集器名称不能为IP格式"}}},o={strategy:{header:"采集过滤策略",table:{policyName:"策略名称",level:"日志级别",eventTypeName:"事件类型",srcIpv:"源IP",dstIpv:"目的IP",describe:"描述",handle:"操作"},dialog:{title:{add:"采集过滤策略添加",update:"采集过滤策略修改"},columns:{policyName:"策略名称",level:"日志级别",eventType:"事件类型",srcIpv:"源IP",dstIpv:"目的IP",describe:"描述"}},placeholder:{input:"策略名称",policyName:"策略名称",level:"日志级别",eventType:"事件类型",srcStartIP:"源起始IP",srcEndIP:"源终止IP",dstStartIP:"目的起始IP",dstEndIP:"目的终止IP"},level:{none:"暂无风险等级"}}},c={logSource:{title:"日志源类型",label:{manufact:"厂商",categoryName:"日志源类别",typeName:"日志源类型",desc:"描述",isAsset:"生成设备"},placeholder:{fuzzyField:"日志源类型",manufact:"厂商",categoryId:"日志源类别",categoryName:"日志源类别",typeName:"日志源类型",desc:"描述"},tip:{assetExist:"采集器地址对应设备已存在，是否确定更新设备类型"}}};t["default"]={collector:Object(a["a"])(Object(a["a"])(Object(a["a"])({},r),o),c)}},d6f8:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"RepositoryCve",path:"/repository/cve",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-3a1a6337")]).then(n.bind(null,"a49b"))}},{name:"RepositoryCnvd",path:"/repository/cnvd",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-54f25af3")]).then(n.bind(null,"1f14"))}},{name:"RepositoryThreatLibrary",path:"/repository/threat",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-31962d14")]).then(n.bind(null,"1114"))}}]},db37:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"EventOriginal",path:"/event/original",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-0bd42ed0")]).then(n.bind(null,"dbaa"))}},{name:"EventPolymerizationStrategy",path:"/event/polymerization-strategy",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-b28bd428")]).then(n.bind(null,"24d4"))}},{name:"EventRelevanceStrategy",path:"/event/relevance-strategy",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-5e17f0e4")]).then(n.bind(null,"9380"))}},{name:"EventSecurity",path:"/event/security",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-c523d130")]).then(n.bind(null,"50b6"))}},{name:"EventOriginalLes",path:"/event/original-les",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-22e75f47")]).then(n.bind(null,"8a7b"))}},{name:"EventThreat",path:"/event/threat",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-1d869246")]).then(n.bind(null,"ed7e"))}},{name:"EventFault",path:"/event/fault",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-cd231c78")]).then(n.bind(null,"11b3"))}},{name:"EventPerf",path:"/event/performance",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-d2506b1a")]).then(n.bind(null,"f3f5"))}},{name:"GeneralLog",path:"/event/general-log",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-39cba3b1")]).then(n.bind(null,"0d2b"))}},{name:"CustomParse",path:"/event/custom-parse",component:function(){return n.e("chunk-5466b056").then(n.bind(null,"3a7d"))}},{name:"CustomCode",path:"/event/custom-code",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-4fe08515")]).then(n.bind(null,"169c"))}},{name:"intrattack",path:"/event/intrattack",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-07762d71")]).then(n.bind(null,"abe6"))}},{name:"flowvirus",path:"/event/flowvirus",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-2f5c6bb3")]).then(n.bind(null,"3381"))}},{name:"usbvirus",path:"/event/usbvirus",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-93907300")]).then(n.bind(null,"0b04"))}},{name:"outerlink",path:"/event/outerlink",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-55e73e0d")]).then(n.bind(null,"dce2"))}},{name:"opsalarm",path:"/event/opsalarm",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-5a1d16e4")]).then(n.bind(null,"4663"))}},{name:"serialport",path:"/event/serialport",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-213b0256")]).then(n.bind(null,"b767"))}},{name:"heightriskport",path:"/event/heightriskport",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-23be01c7")]).then(n.bind(null,"8ec4"))}},{name:"ipmac",path:"/event/ipmac",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-e5aa3c2c")]).then(n.bind(null,"18ac"))}},{name:"filecategory",path:"/event/filecategory",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-1732ce1e")]).then(n.bind(null,"c574"))}},{name:"whitelist",path:"/event/whitelist",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-commons"),n.e("chunk-71eb9e4f")]).then(n.bind(null,"c756"))}},{name:"EventRelevance",path:"/event/relevance",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-107ad0b4")]).then(n.bind(null,"081d"))}}]},db73:function(e,t,n){"use strict";n.r(t);var a=n("d0ff"),r={userData:{cur_user_role_type:0,token:"",user_name:"",permission:[]},runsDay:{author_status:"soon_overdue",module_infos:[{name:"入侵检测引擎",key_name:"ids_valid_period",version:"1.1",show_valid_time:"21天3小时11分"}],module_num:1,time_day_num:5},initial:"",routes:[]},o={SET_USER_DATA:function(e,t){e.userData=t},CLEAR_USER_DATA:function(e){e.userData={cur_user_role_type:0,token:"",user_name:"",permission:[]}},SET_RUNS_DAY:function(e,t){e.runsDay=t},SET_INITIAL:function(e,t){e.initial=t},SET_ROUTES:function(e,t){e.routes=Object(a["a"])(t)}},c={updateUserData:function(e,t){var n=e.commit;n("SET_USER_DATA",t)},clearUserData:function(e){var t=e.commit;t("CLEAR_USER_DATA")},updateRunsDay:function(e,t){var n=e.commit;n("SET_RUNS_DAY",t)},updateInitial:function(e,t){var n=e.commit;n("SET_INITIAL",t)},updateRoutes:function(e,t){var n=e.commit;n("SET_ROUTES",t)}};t["default"]={namespaced:!0,state:r,mutations:o,actions:c}},e777:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"AreaManagement",path:"/asset/area",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-164be1d1")]).then(n.bind(null,"6ff7"))}},{name:"AssetCustom",path:"/asset/custom",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-296f9e8e")]).then(n.bind(null,"c339"))}},{name:"AssetDiscover",path:"/asset/discover",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-0d12ef2c")]).then(n.bind(null,"edbf"))}},{name:"AssetManagement",path:"/asset/management",component:function(){return Promise.all([n.e("chunk-elementUI"),n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-cb20a79e")]).then(n.bind(null,"edde"))}},{name:"DeviceMonitor",path:"/asset/deviceMonitor",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-66df32b4")]).then(n.bind(null,"e289"))}},{name:"MonitorSetting",path:"/asset/monitorSetting",component:function(){return n.e("chunk-fb0ff992").then(n.bind(null,"1b3b"))}},{name:"DeviceUpdate",path:"/asset/deviceUpdate",component:function(){return Promise.all([n.e("chunk-echarts"),n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-67ddc70c")]).then(n.bind(null,"c2f0"))}},{name:"AssetNetworkManagement",path:"/asset/network-management",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-aa6dc7f8")]).then(n.bind(null,"830c"))}},{name:"AssetType",path:"/asset/type",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-44a54786")]).then(n.bind(null,"eeaf"))}},{name:"AssetBaselineTemplate",path:"/asset/baseline-temp",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-478787c2")]).then(n.bind(null,"73ce"))}},{path:"/asset/host-guardian-management",name:"HostGuardianManagement",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-2c200aa6")]).then(n.bind(null,"65b6"))},meta:{title:"主机卫士管理",requiresAuth:!0}},{path:"/asset/host-guardian-group",name:"HostGuardianGroup",component:function(){return Promise.all([n.e("chunk-b30294b4"),n.e("chunk-0aa72df1")]).then(n.bind(null,"8d15"))},meta:{title:"主机卫士分组管理",requiresAuth:!0}},{path:"/asset/senondary-auth",name:"SenondaryAuth",component:function(){return Promise.all([n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-20f1c03d"),n.e("chunk-58624a44")]).then(n.bind(null,"2911"))},meta:{title:"二次授权",requiresAuth:!0}}]},e7c0:function(e,t,n){"use strict";n.r(t),t["default"]={login_old:{systemName:"东软NetEye SOC网络安全管理系统V5.0",copyright:"版权所有 © 2001-2020东软集团股份有限公司",text:"登         录",systemModel:"产品型号：",account:{invalid:"用户名或密码错误",overdue:"账号不在有效期",illegalTime:"账号非法时间登录",illegalUser:"账号不存在",lock:"账号被锁定",toImproved:'账号首次登录,<b class="{0}" title="完善用户信息">完善</b>用户信息',unauth:"账号未授权任何菜单",empty:"用户名或密码不能为空"},password:{overdue:'账号<b class="{0}" title="修改密码">密码</b>过期',beReset:'密码被<b class="{0}" title="修改密码">重置</b>',invalid:"密码错误",forget:"忘记密码？"},captcha:{invalid:"验证码错误",empty:"验证码不能为空",switch:"看不清？点击切换验证码"},session:{invalid:"session已失效或者未登录"},upgrade:{doing:"系统升级中，请稍后再试！"},license:{sequence:"序列号",overdue:"License不合法，请联系管理员",admin:"License不合法，请重新{0}",copy:"复制"},success:{welcome:"欢迎 {0} 回来",morning:"上午好,今天也是充满希望的一天！",noon:"中午好，是时候展现真正的技术了！",afternoon:"下午好，祝你下午愉快、烦恼溜走！",evening:"晚上好，把每个平凡日常变成美好时光！",night:"夜间好，要注意休息喔！"},findPassword:{findPasswordByEmail:"通过邮箱找回密码",account:"账号",email:"注册邮箱",captcha:"验证码",newPassword:"新密码",confirmPassword:"确认密码"}},login:{systemName:"智能安全数据分析平台",copyright:"版权所有 © 2001-2020集团股份有限公司",text:"登         录",account:{error:"邮件地址重复",invalid:"用户名或密码错误",overdue:"账号不在有效期",illegalUser:"账号不存在",illegalTime:"账号非法时间登录",lock:"账号被锁定",toImproved:'账号首次登录,<b class="{0}" title="完善用户信息">完善</b>用户信息',unauth:"账号未授权任何菜单",empty:"用户名或密码不能为空"},password:{overdue:'账号<b class="{0}" title="修改密码">密码</b>过期',beReset:'密码被<b class="{0}" title="修改密码">重置</b>',invalid:"密码错误",forget:"忘记密码？"},captcha:{success:"验证通过，用时{0}s",fail:"验证失败",invalid:"验证码已失效"},session:{invalid:"session已失效或者未登录"},upgrade:{doing:"系统升级中，请稍后再试！"},license:{sequence:"序列号",illegal:"License不合法，请重新{0}",expire:"License已失效，请重新{0}",overdue:"License不合法，请联系管理员",admin:"License不合法，请重新{0}"},ticket:"票据异常",success:{welcome:"欢迎 {0} 回来",morning:"上午好,今天也是充满希望的一天！",noon:"中午好，是时候展现真正的技术了！",afternoon:"下午好，祝你下午愉快、烦恼溜走！",evening:"晚上好，把每个平凡日常变成美好时光！",night:"夜间好，要注意休息喔！"},mail:{getCaptcha:"获取验证码",invalid:"邮箱验证码错误",getAgainCaptcha:"{0}秒后再次获取验证码",noExist:"邮箱不存在"},type:{mail:"邮箱登录",account:"账户登录"},validate:{success:"验证码发送成功，请在邮箱查看",invalid:"无效的邮箱地址",failed:"验证码发送失败，请稍后重试",emailName:"邮件地址不能为空"},findPassword:{findPasswordByEmail:"通过邮箱找回密码",account:"账号",email:"注册邮箱",captcha:"验证码",newPassword:"新密码",confirmPassword:"确认密码"}}}},e8b1:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"CollectorManagement",path:"/collector/management",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-dff85b28")]).then(n.bind(null,"a056"))}},{name:"CollectorStrategy",path:"/collector/strategy",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-43a1053e")]).then(n.bind(null,"c1a2"))}},{name:"LogSource",path:"/collector/log-source",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-b103a8ba")]).then(n.bind(null,"a32a"))}}]},ee2a:function(e,t,n){"use strict";n.r(t),t["default"]={code:{level:{l0:"暂无等级信息",risk:{l1:"正常",l2:"低危",l3:"中危",l4:"高危",l5:"失陷"},event:{l1:"一般",l2:"低级",l3:"中级",l4:"高级",l5:"严重"},vul:{l1:"低级",l2:"中级",l3:"高级"},alarm:{l1:"很低",l2:"低",l3:"中",l4:"高",l5:"很高"},value:{l1:"低",l2:"一般",l3:"中",l4:"高",l5:"很高"},dga:{l0:"低级",l1:"高级"}},chart:{line:{line:"折线图",lineStack:"堆积线图",lineStep:"阶梯线图",lineStackStep:"堆叠阶梯线图"},pie:{pie:"饼状图",pieRose:"玫瑰图",pieHalf:"半圆图",pie3D:"3D圆图",ring:"环形图",ringRose:"玫瑰环图",ringHalf:"半环图",ring3D:"3D环图"},bar:{bar:"柱形图",barStack:"堆叠柱图",barPolar:"极地坐标柱图",barPolarStack:"极地坐标堆叠柱图",barRadial:"径向柱图",barRadialStack:"径向堆叠柱图"},axis:{x:"横向",y:"纵向"},formatType:{byte:"比特",number:"数量"}},status:{on:"在线",off:"离线"},executeStatus:{on:"启用",off:"停用"},state:{device:{offline:"离线状态",online:"在线状态",abnormal:"异常状态"},task:{enable:"启用",disable:"禁用"},scan:{fail:"提交失败",running:"正在运行",completed:"已完成",skipped:"已跳过",stopped:"已停止",wait:"等待",timeout:"超时"},vul:{resolved:"已解决",unsolved:"未解决"},asset:{notConfig:"未配置SNMP",noConfig:"未配置",offline:"离线",normal:"正常",abnormal:"异常"},connect:{success:"成功",failed:"失败"},current:{normal:"正常",abnormal:"异常"}},stage:{attack:{invasionAsset:"入侵设备",singleBlowup:"单点爆破",hInfiltrate:"横向渗透",stealData:"窃取数据"}},asset:{state:{all:"全部",register:"注册设备",noRegister:"非注册设备",important:"重点设备",noImportant:"非重点设备",focus:"关注设备",noFocus:"非关注设备",fixed:"固定设备",noFixed:"非固定设备"},audit:{manual:"手动评估",auto:"自动评估"},source:{manually:"手动录入",passiveFind:"被动发现",deviceSync:"设备同步"},change:{flowDiscern:"流量识别",scanFind:"扫描发现",deviceSync:"设备同步",bulkImport:"批量导入",manualEdit:"手动编辑"}},trend:{up:"上升",down:"下降",same:"不变"},cycle:{immediate:"立即执行",day:"每天一次",week:"每周一次",month:"每月一次"},alarm:{type:{cpu:"CPU超限",memory:"内存超限",disk:"磁盘超限"}},ip:{type:{all:"全部",outer:"外网",inner:"内网"}},area:{domestic:"国内",foreign:"国外"},place:{china:"中国",world:"世界"},flow:{type:{inner:"内网流量",outer:"外网流量"}},handleStatus:{unhandle:"未处理",ignore:"忽略"},anomalyType:{illegalAction:"异常行为",illegalIntruder:"非法入侵"},runStatus:{normal:"正常",abnormal:"异常"},forecastType:{total:"总体",eventType:"事件类型",srcIp:"源IP",dstIp:"目的IP",fromIp:"采集器IP"},resultStatus:{success:"成功",fail:"失败"},backupWay:{increment:"增量",all:"全量"},thresholdType:{fault:"故障",performance:"性能"},displayForm:{text:"文本",chart:"图表"}}}},f1c7:function(e,t,n){"use strict";n.r(t),t["default"]={layout:{search:{empty:"请输入内容",asset:"设备",event:"事件",audit:"审计",alarm:"告警"},setting:{alarm:"当前告警数量有:{0}条",systemAlarm:"当前系统告警数量有:{0}条",user:{label:"用户信息",username:"用户名",nickname:"昵称",email:"邮件地址",mobilephone:"移动电话",telephone:"固定电话",theme:"默认主题",menu:"默认菜单"},theme:{label:"切换主题",warning:"您切换的主题与当前主题相同哦!",techblue:"科技蓝",marrsgreen:"马尔斯绿",royalblue:"贵族蓝"},password:{label:"修改密码",new:"新密码",old:"旧密码",confirm:"确认密码",forget:"找回密码"},forgetPassword:{title:"只需两步就可重设密码",account:"账号",email:"注册邮箱"},logout:"注销登录"},license:"License还有{0}天就要过期了，请及时联系管理员上传",systemAlarm:"存在系统告警{0}条，点击进入系统告警页面查看详情。",onSiteNotice:"存在站内通知{0}条，点击进入监控告警页面查看详情。",systemInfo:{title:"系统信息详情",basic:{title:"基本信息"},cpu:{title:"CPU信息",cpuNum:"核心数",sys:"CPU系统使用率",used:"CPU用户使用率",wait:"CPU当前等待率",free:"CPU当前空闲率"},mem:{title:"内存信息",total:"内存总量（GB）",used:"已用内存",free:"剩余内存",usage:"使用率"},sys:{title:"系统信息",computerName:"服务器名称",osName:"操作系统",osArch:"系统架构"},sysFiles:{title:"系统文件信息",dirName:"盘符路径",sysTypeName:"盘符类型",typeName:"文件类型",total:"总大小",free:"剩余大小",used:"已经使用量",usage:"使用率"}}},logout:{message:"Session失效或系统停留时间过长，系统将返回登录页面!"}}},f6d0:function(e,t,n){"use strict";n.r(t);var a=n("f3f3"),r={page401:{department:"OOPS!",copyright:{label:"版权所有",name:"东软网络安全事业部SOC团队"},author:{label:"作者",name:""},instructions:"你没有权限去该页面......",check:"如有不满请联系管理员",home:"返回刚刚页面"}},o={page404:{department:"OOPS!",copyright:{label:"版权所有",name:"东软网络安全事业部SOC团队"},author:{label:"作者",name:""},instructions:"这个页面你不能进......",check:"请检查您输入的网址是否正确",home:"返回刚刚页面"}};t["default"]={exception:Object(a["a"])(Object(a["a"])({},r),o)}},f7b5:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));n("276c"),n("e954");var a=n("a47e"),r=n("5c96");function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n={i18nCode:"tip.confirm.tip",i18nParam:[],type:"info",center:!0,duration:1500,popup:!0,print:!1,error:""},o=Object.assign(n,e);if(o.popup&&0===document.getElementsByClassName("el-message").length){var c=o.i18nParam.length>0?a["a"].t(o.i18nCode,o.i18nParam):a["a"].t(o.i18nCode);Object(r["Message"])({message:c,type:o.type,center:o.center,duration:o.duration})}if(o.print)switch(o.type){case"info":console.info("%c".concat(a["a"].t(o.i18nCode)),"color: #909399");break;case"success":console.log("%c".concat(a["a"].t(o.i18nCode)),"color: #67c23a");break;case"warning":console.warn("%c".concat(a["a"].t(o.i18nCode)),"color: #e6a23c");break;case"error":console.error("%c".concat(a["a"].t(o.i18nCode)),"color: #f56c6c");break;default:break}""!==o.error&&console.error(o.error),t&&t()}},f907:function(e,t,n){"use strict";t["a"]={success:200,exception:{system:500,server:1002,session:2e3,access:2001,certification:2002,auth:2003,token:2004,param:2005,idempotency:1001,ip:2006,upload:2007},attack:{xss:1e3}}},f96b:function(e,t,n){var a={"./hostguardian.js":"3bd7","./hostguardianUser.js":"db73","./router.js":"38e2","./system.js":"3072","./user.js":"7fa2","./websocket.js":"58bd"};function r(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=o,e.exports=r,r.id="f96b"},fc0a:function(e,t,n){"use strict";n.r(t),t["default"]={tip:{sweet:"温馨提示",notice:"消息提醒",total:"总计",without:"无",add:{success:"添加成功",repeat:"添加重复",repeatName:"名称重复!",repeatNet:"与已有IP存在重合!",error:"添加失败",license:"资产已达最大值，无法继续添加",IpType:"起止IP类型不一致！",aroundError:"IP范围错误，终止IP应大于起始IP",innerType:"该接收方式、采集地址已配置采集器，无需重复配置。",maxCount:"超过最大采集器数量",innerTypeRepeat:"Kafka接收方式已存在，不能重复",ipAround:"IP区间不能超过120！",errorType:"IP范围不合法",repeatTask:"已有发现该网段的任务",ipExist:"IP已存在",licenseLimit:"超过license限制监控器数量",assetLimit:"资产信息达到阈值，无法同步添加资产。",logSourceExist:"所选日志源不存在，无法添加。",fail:"模板显示属性至少输入一条！"},define:{success:"确认成功",error:"确认失败"},delete:{success:"删除成功",error:"删除失败",prompt:"请选择删除内容",running:"正在使用中,请停用后再删除",use:"正在被使用中,请勿删除"},ignore:{success:"忽略成功",error:"忽略失败",prompt:"请选择忽略内容"},correct:{success:"加入白名单成功",error:"加入白名单失败"},update:{success:"修改成功",error:"修改失败",null:"不能修改为空!",repeat:"修改的内容已存在",prompt:"请选择修改内容",repeatGroup:"审计组名称重复",repeatPerson:"名称已存在",running:"使用中的数据不能修改!",ipAddress:"ip地址重复，请重新填写",ipRepeat:"ip地址范围存在重合，请重新填写",use:"正在被使用中,请勿修改",password:"密码修改成功，请重新登录！",innerType:"该接收方式已有对应ip",repeatDic:"字典名称重复",ipExist:"IP已存在",ipMonitored:"该资产已经被监控器使用,ip不能修改",repeatName:"名称已存在，请重新命名",reboot:"配置信息修改系统重启中，请稍后访问。",logSourceExist:"所选日志源不存在，无法修改。",emailTip:"邮箱已绑定。"},run:{prompt:"请选择运行内容"},stop:{prompt:"请选择停止内容",success:"停用成功",error:"停用失败",existStop:"选择中包含已经停用的记录，请勿重复停用。",selectRow:"请选择需要停用的记录"},start:{deletePrompt:"启用状态监控器不允许删除。",success:"启用成功",error:"启用失败",existStart:"选择中包含已经启用的记录，请勿重复启用。",selectRow:"请选择需要启用的记录"},query:{success:"查询成功",error:"查询失败"},build:{success:"构建成功",error:"构建失败"},config:{success:"配置成功",error:"配置失败"},sort:{success:"排序成功",error:"排序失败"},upload:{success:"上传成功",error:"上传失败",fileempty:"请选择上传文件",xml:"请选择xml格式文件",typeError:"上传文件格式错误",nameError:"上传文件名称错误",nameErr:"非法升级包名称",baseErr:"非法升级包版本",contentErr:"非法升级包内容",fail:"升级失败",format:"上传文件格式错误",back:{success:"版本回退成功",error:"版本回退失败,请稍后重试",none:"无法回退版本"},running:"系统已在升级，请勿操作",successUp:"升级成功！部分服务将重启!请三分钟后使用!"},export:{success:"导出成功",error:"导出失败",prompt:"请选择导出内容"},import:{success:"导入成功",error:"导入失败",license:"资产已达最大值，无法继续导入"},download:{success:"下载成功",error:"下载失败",prompt:"请选择下载内容"},clear:{success:"清空成功",error:"清空失败"},save:{success:"保存成功",error:"保存失败",onBackup:"数据正在备份中，请稍后。"},reset:{success:"重置成功",none:"无备份信息",error:"重置失败"},timing:{success:"校时成功",error:"校时失败"},restart:{success:"重启成功",error:"重启失败"},restore:{success:"恢复出厂设置成功",error:"恢复出厂设置失败"},shutdown:{success:"关机成功",error:"关机失败"},create:{success:"创建成功",repeat:"创建重复",error:"创建失败"},check:{success:"校验成功",error:"校验失败"},test:{success:"测试成功",error:"测试失败",tip:"网口正在测试中，请稍后。"},checkUrl:{success:"该域名不是恶意域名",error:"该域名是恶意域名"},operate:{success:"{0}成功",data:"有数据",error:"{0}失败",empty:"无数据",alert:"请选择要{0}的{1}",prompt:"请选择要{0}的内容",confirm:"确认{0}吗？"},component:{searchKeywords:"请输入查询关键字",choseDate:"选择日期",chose:"请选择"},confirm:{tip:"提示",submit:"确认提交吗？",delete:"确认删除吗？",reset:"确认重置吗？",save:"确认保存吗？",batchDelete:"确认将选中的删除吗？",content:"内容",error:"提交失败",cancel:"取消提交",correct:"确认加入白名单吗？",batchIgnore:"确认将选中的忽略吗？",grant:"确定要授权吗",clear:"确定要清空吗",restart:"确定要重启吗",shutdown:"确定要关机吗",restore:"确定要恢复出厂设置吗",existAccessmode:"该采集地址已经配置其他接入方式，确定提交吗？",existAsset:"该采集地址已配置资产，确定更新资产类型吗？",batchStop:"确认将选中的停用吗？",batchStart:"确认将选中的启用吗？",sslStart:"确定要启用SSH服务吗",sslStop:"确定要禁用SSH服务吗",allCenterIp:"确定要统一设置中心IP吗？",funcStart:"确定启用功能吗？",funcStop:"确定停用功能吗？",rebase:"确定回归这些基线模板吗？"},data:{empty:"暂无数据"},toggle:{success:"切换成功",error:"切换失败",run:"运行状态切换成功",stop:"运行状态切换失败"},change:{repeat:"资产名称重复",success:"转化成功",error:"转化失败",running:"发现任务正在运行",prompt:"请选择要转化的内容",number:"资产数量超过license规定数量",begin:"开始发现任务"},find:{start:"开始发现",repeat:"发现重复",success:"发现成功",error:"发现失败",prompt:"请选择要发现的内容"},enable:{success:"启用成功",error:"启用失败",prompt:"请选择要启用的内容"},disable:{success:"停用成功",error:"停用失败",prompt:"请选择要停用的内容"},status:{uninstall:{success:"卸载成功"},recover:{success:"恢复成功"},change:{error:"状态切换失败"},existMonitor:"该代理下已配置监控器信息，不允许卸载。",existCollector:"该代理下已配置采集器信息，不允许卸载。",existEquipment:"该代理下已配置监控器、采集器信息，不允许卸载。"},empty:"此项不能为空",ip:{error:"错误的ip格式"},port:{error:"错误的端口号格式"},select:{all:"全选",empty:"请至少选择一项",row:"请至少选择一行",one:"请选择一条记录"},placeholder:{query:"请输入{0}关键字",legalIp:"请输入合法IP"},execute:{success:"执行成功",error:"执行失败"},recovery:{success:"恢复成功",error:"恢复失败",process:"系统恢复过程中，请稍后使用"},copy:{success:"复制成功",error:"复制失败",repeat:"策略名称重复",builtIn:"内置策略，不允许复制。"},detect:{success:"检测成功",error:"检测失败"},agent:{info:"代理卸载尚未恢复，请稍后再试。"},send:{success:"发送成功",error:"发送失败",userNotExist:"用户不存在",userEmailNotMatch:"用户预留邮箱不匹配",serviceUnavailable:"邮件服务不可用",codeHasExpired:"验证码已失效"},expression:{repeat:"表达式重复"}}}},fc0e:function(e,t,n){var a={"./alarm/index.js":"8704","./asset/index.js":"a6a3","./audit/index.js":"9cf1","./collector/index.js":"d3dd","./common/index.js":"9d04","./component/index.js":"813d","./demo/index.js":"6894","./event/index.js":"86ba","./exception/index.js":"f6d0","./forecast/index.js":"7c6b","./layout/index.js":"f1c7","./login/index.js":"e7c0","./management/index.js":"7979","./monitor/index.js":"44be","./report/index.js":"d385","./repository/index.js":"35dc","./visualization/index.js":"a6c6"};function r(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=o,e.exports=r,r.id="fc0e"},fd82:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"f",(function(){return i})),n.d(t,"g",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"i",(function(){return l})),n.d(t,"e",(function(){return m})),n.d(t,"d",(function(){return d})),n.d(t,"j",(function(){return p}));n("99af");var a=n("4020");function r(){return Object(a["a"])({url:"/authentication/register",method:"get"})}function o(){return Object(a["a"])({url:"/authentication/captcha",method:"get"})}function c(e){return Object(a["a"])({url:"/authentication/login",method:"get",params:e||{}})}function i(){return Object(a["a"])({url:"/menumanagement/menu/navigation",method:"get"})}function s(e){return Object(a["a"])({url:"/usermanagement/password",method:"put",data:e||{}})}function u(e){return Object(a["a"])({url:"/usermanagement/user/extend/init",method:"put",data:e||{}})}function l(e){return Object(a["a"])({url:"/systemmanagement/license/upload",method:"post",data:e||{}},"upload")}function m(e){return Object(a["a"])({url:"/authentication/login-mail",method:"get",params:e||{}})}function d(e){return Object(a["a"])({url:"/authentication/captcha-mail/".concat(e),method:"get"})}function p(e){return Object(a["a"])({url:"/authentication/check/captcha/".concat(e),method:"get"})}},fdc4:function(e,t,n){"use strict";n.r(t);n("d3b7");t["default"]=[{name:"ManagementMenu",path:"/management/menu",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-a858ac6a")]).then(n.bind(null,"7680"))}},{name:"ManagementResource",path:"/management/resource",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-c6d52a16")]).then(n.bind(null,"65fc"))}},{name:"ManagementRole",path:"/management/role",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-54165ad6")]).then(n.bind(null,"06bb"))}},{name:"ManagementSystem",path:"/management/system",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-5f5965cb")]).then(n.bind(null,"8eae"))}},{name:"ManagementUser",path:"/management/user",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-b6d08ff2")]).then(n.bind(null,"8d79"))}},{name:"ManagementForwardServer",path:"/management/forward-server",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-5f47703f")]).then(n.bind(null,"0cfd"))}},{name:"ManagementLogAudit",path:"/management/log-audit",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-263080a8")]).then(n.bind(null,"45b6"))}},{name:"ManagementLogBackup",path:"/management/log-backup",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-263080a8"),n.e("chunk-51ebfc7c")]).then(n.bind(null,"0e43"))}},{name:"ManagementNetwork",path:"/management/network",component:function(){return n.e("chunk-049a61f6").then(n.bind(null,"3541"))}},{name:"ManagementSystemUpgrade",path:"/management/system-upgrade",component:function(){return n.e("chunk-03c483a9").then(n.bind(null,"1f56"))}},{name:"ManagementAccessControl",path:"/management/access-control",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-2c7558ce")]).then(n.bind(null,"dcfe"))}},{name:"ManagementProxyServer",path:"/management/proxy-server",component:function(){return Promise.all([n.e("chunk-commons"),n.e("chunk-63f54f46")]).then(n.bind(null,"8b9d"))}},{name:"LogPcap",path:"/management/log-pcap",component:function(){return n.e("chunk-1741ea3d").then(n.bind(null,"9e16"))}},{name:"Notice",path:"/management/notice",component:function(){return n.e("chunk-743990b6").then(n.bind(null,"1f81"))}},{name:"ThreatSetting",path:"/management/threat-setting",component:function(){return n.e("chunk-709aa764").then(n.bind(null,"3b0e"))}},{name:"TopoSetting",path:"/management/topo-setting",component:function(){return Promise.all([n.e("chunk-9e9c9b90"),n.e("chunk-2d0d6345"),n.e("chunk-20f1c03d"),n.e("chunk-b810ffa8"),n.e("chunk-65d21bde")]).then(n.bind(null,"17a3"))}}]}},[[0,"runtime","chunk-elementUI","chunk-libs"]]]);