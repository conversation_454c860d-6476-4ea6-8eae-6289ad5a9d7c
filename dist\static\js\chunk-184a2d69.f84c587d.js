(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-184a2d69"],{"00e5":function(e,t,a){},10:function(e,t){},"1dac":function(e,t,a){},"26c1":function(e,t,a){},"314c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("el-tabs",{on:{"tab-click":e.onChange,"tab-remove":e.onEdit},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[a("el-tab-pane",{attrs:{label:"设备列表",name:"0",closable:!1}},[a("header",{staticClass:"table-header"},[a("section",{staticClass:"table-header-main"},[a("section",{staticClass:"table-header-search"},[a("section",{directives:[{name:"show",rawName:"v-show",value:!e.isShow,expression:"!isShow"}],staticClass:"table-header-search-input"},[a("el-input",{attrs:{clearable:"",placeholder:"设备名称","prefix-icon":"soc-icon-search"},on:{change:e.handleAdvancedSearch},model:{value:e.searchForm.fireName,callback:function(t){e.$set(e.searchForm,"fireName",t)},expression:"searchForm.fireName"}})],1),a("section",{staticClass:"table-header-search-button"},[e.isShow?e._e():a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdvancedSearch}},[e._v("查询")]),a("el-button",{on:{click:e.toggleShow}},[e._v(" 高级搜索 "),a("i",{staticClass:"el-icon--right",class:e.isShow?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),a("section",{staticClass:"table-header-button"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAddClick}},[e._v("新建设备")]),a("el-button",{attrs:{type:"danger"},on:{click:e.handleDeleteClick}},[e._v("批量删除")])],1)]),a("section",{staticClass:"table-header-extend"},[a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}]},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"设备名称"},on:{change:e.handleAdvancedSearch},model:{value:e.searchForm.fireName,callback:function(t){e.$set(e.searchForm,"fireName",t)},expression:"searchForm.fireName"}})],1),a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"设备IP"},on:{change:e.handleAdvancedSearch},model:{value:e.searchForm.ip,callback:function(t){e.$set(e.searchForm,"ip",t)},expression:"searchForm.ip"}})],1),a("el-col",{attrs:{span:6}},[a("el-select",{attrs:{clearable:"",placeholder:"在线状态"},on:{change:e.handleAdvancedSearch},model:{value:e.searchForm.onlinStatus,callback:function(t){e.$set(e.searchForm,"onlinStatus",t)},expression:"searchForm.onlinStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"在线",value:"1"}}),a("el-option",{attrs:{label:"离线",value:"0"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,align:"right"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdvancedSearch}},[e._v("查询")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")]),a("el-button",{attrs:{icon:"soc-icon-scroller-top-all"},on:{click:e.toggleShow}})],1)],1)],1)])],1)]),a("main",{staticClass:"table-body"},[a("section",{staticClass:"table-body-header"},[a("h2",{staticClass:"table-body-title"},[e._v("审计设备管理")])]),a("section",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-body-main"},[a("el-table",{attrs:{data:e.auditData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"selection-change":e.onSelectChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s((e.currentPage-1)*e.currentPageSize+t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{prop:"notes",label:"设备名称","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handleLook(t.row)}}},[e._v(" "+e._s(t.row.notes)+" ")])]}}])}),a("el-table-column",{attrs:{prop:"category_text",label:"设备类型"}}),a("el-table-column",{attrs:{label:"在线状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:1===t.row.status?"status-online":"status-offline"},[a("i",{class:1===t.row.status?"el-icon-success":"el-icon-error"}),e._v(" "+e._s(1===t.row.status?"在线":"离线")+" ")])]}}])}),a("el-table-column",{attrs:{prop:"syl_cpu",label:"CPU率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.syl_cpu?a("el-progress",{attrs:{percentage:parseInt(t.row.syl_cpu),"stroke-width":8,color:"#52C41A"}}):e._e()]}}])}),a("el-table-column",{attrs:{prop:"syl_nc",label:"内存率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.syl_nc?a("el-progress",{attrs:{percentage:parseInt(t.row.syl_nc),"stroke-width":8,color:"#4C24ED"}}):e._e()]}}])}),a("el-table-column",{attrs:{prop:"syl_disk",label:"磁盘率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.syl_disk?a("el-progress",{attrs:{percentage:parseInt(t.row.syl_disk),"stroke-width":8,color:"#1373F1"}}):e._e()]}}])}),a("el-table-column",{attrs:{prop:"originPort",label:"实时流量",width:"200"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("echarts-test",{attrs:{record:e.row.device_flow_info}})]}}])}),a("el-table-column",{attrs:{label:"操作",width:"280",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"action-buttons"},[a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handleShowConfig(t.row)}}},[e._v("配置")]),a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handleLook(t.row)}}},[e._v("查看")]),a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handleDeleteClick(t.row)}}},[e._v("删除")]),a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handlePing(t.row)}}},[e._v("Ping")]),a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handleUser(t.row)}}},[e._v("用户管理")])],1)]}}])})],1)],1)]),a("footer",{staticClass:"table-footer"},[e.auditTotal>0?a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.currentPageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.auditTotal},on:{"size-change":e.onShowSizeChange,"current-change":e.handlePageChange}}):e._e()],1)]),e._l(e.panes,(function(e){return a("el-tab-pane",{key:e.key,attrs:{label:e.title,name:e.key,closable:!0}},[a("iframe",{attrs:{src:e.content,width:"100%",height:"600px",frameborder:"0"}})])}))],2),a("add-device-modal",{ref:"addModal",attrs:{"group-data":e.groupData},on:{save:e.handleAddDevice}}),a("edit-config-modal",{ref:"configModal",attrs:{"group-data":e.groupData,"current-config":e.currentConfig},on:{save:e.handleConifgSave}}),a("el-dialog",{attrs:{visible:e.userVisible,title:"已保存用户",width:"650px","close-on-click-modal":!1,center:""},on:{"update:visible":function(t){e.userVisible=t}}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-weight":"bold"}},[e._v("已保存用户")]),a("span",{staticStyle:{color:"#999","margin-left":"10px"}},[e._v("请选择登陆用户并保存")])]),a("el-table",{attrs:{data:e.curDevUserList,loading:e.loading,"row-key":"id"},on:{"selection-change":e.onUserSelectChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{label:"序号",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{prop:"account_name",label:"用户名"}}),a("el-table-column",{attrs:{prop:"account_role_text",label:"用户类型"}}),a("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{"border-radius":"2px",color:"#1890ff"},attrs:{type:"text"},on:{click:function(a){return e.handleDeleteUser(t.row)}}},[e._v(" 删除 ")])]}}])})],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{type:"flex",justify:"end"}},[a("el-pagination",{attrs:{"current-page":e.currentUserPage,"page-size":e.currentUserPageSize,total:e.userTotal,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.onUserShowSizeChange,"current-change":e.handleUserPageChange}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleCancelClick}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleOkUser}},[e._v("保存")])],1)],2)],1)},n=[],o=(a("4de4"),a("7db0"),a("c740"),a("4160"),a("d81d"),a("45fc"),a("b0c0"),a("a9e3"),a("d3b7"),a("ac1f"),a("25f0"),a("5319"),a("1276"),a("159b"),a("f3f3")),i=(a("96cf"),a("c964")),s=a("c9d9");function c(e){return l.apply(this,arguments)}function l(){return l=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/getData",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),l.apply(this,arguments)}function u(e){return d.apply(this,arguments)}function d(){return d=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/getUser",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),d.apply(this,arguments)}function p(e){return h.apply(this,arguments)}function h(){return h=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/addUser",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),h.apply(this,arguments)}function f(e){return g.apply(this,arguments)}function g(){return g=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/modifyUser",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),g.apply(this,arguments)}function m(e){return b.apply(this,arguments)}function b(){return b=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/deleteUser",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),b.apply(this,arguments)}function v(e){return w.apply(this,arguments)}function w(){return w=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/loginForLog",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),w.apply(this,arguments)}function y(e){return _.apply(this,arguments)}function _(){return _=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/devicePing",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),_.apply(this,arguments)}function k(e){return x.apply(this,arguments)}function x(){return x=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/DeleteData",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),x.apply(this,arguments)}function S(e){return A.apply(this,arguments)}function A(){return A=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/addEquipment",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),A.apply(this,arguments)}function C(e){return P.apply(this,arguments)}function P(){return P=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/editEquipment",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),P.apply(this,arguments)}function O(e){return D.apply(this,arguments)}function D(){return D=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/audit/getTopoData",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),D.apply(this,arguments)}function U(e){return j.apply(this,arguments)}function j(){return j=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/fireWall/setTopoData",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),j.apply(this,arguments)}function L(e){return R.apply(this,arguments)}function R(){return R=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(s["a"])({url:"/GroupManagement/searchGroup",method:"post",data:t}));case 1:case"end":return e.stop()}}),e)}))),R.apply(this,arguments)}var N=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,title:"添加设备",width:"600px","close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.onHide}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"设备名称",prop:"notes"}},[a("el-input",{attrs:{placeholder:"请输入设备名称"},model:{value:e.form.notes,callback:function(t){e.$set(e.form,"notes",t)},expression:"form.notes"}})],1),a("el-form-item",{attrs:{label:"设备IP",prop:"ip"}},[a("el-input",{attrs:{placeholder:"请输入设备IP"},model:{value:e.form.ip,callback:function(t){e.$set(e.form,"ip",t)},expression:"form.ip"}})],1),a("el-form-item",{attrs:{label:"重要性",prop:"importance"}},[a("el-select",{attrs:{placeholder:"请选择重要性"},model:{value:e.form.importance,callback:function(t){e.$set(e.form,"importance",t)},expression:"form.importance"}},[a("el-option",{attrs:{label:"高",value:1}}),a("el-option",{attrs:{label:"中",value:2}}),a("el-option",{attrs:{label:"低",value:3}})],1)],1),a("el-form-item",{attrs:{label:"设备分组",prop:"group_id"}},[a("el-select",{attrs:{placeholder:"请选择设备分组"},model:{value:e.form.group_id,callback:function(t){e.$set(e.form,"group_id",t)},expression:"form.group_id"}},e._l(e.groupData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.groupName,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"位置",prop:"position"}},[a("el-input",{attrs:{placeholder:"请输入位置"},model:{value:e.form.position,callback:function(t){e.$set(e.form,"position",t)},expression:"form.position"}})],1),a("el-form-item",{attrs:{label:"负责人",prop:"person_liable"}},[a("el-input",{attrs:{placeholder:"请输入负责人"},model:{value:e.form.person_liable,callback:function(t){e.$set(e.form,"person_liable",t)},expression:"form.person_liable"}})],1),a("el-form-item",{attrs:{label:"联系方式",prop:"contact"}},[a("el-input",{attrs:{placeholder:"请输入联系方式"},model:{value:e.form.contact,callback:function(t){e.$set(e.form,"contact",t)},expression:"form.contact"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.onHide}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSave}},[e._v("保存")])],1)],1)},I=[],E={name:"AddDeviceModal",props:{groupData:{type:Array,default:function(){return[]}}},data:function(){return{visible:!1,loading:!1,form:{notes:"",ip:"",importance:void 0,group_id:void 0,position:"",person_liable:"",contact:""},rules:{notes:[{required:!0,message:"请输入设备名称",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,message:"请输入汉字、字母、数字、短横线或下划线",trigger:"blur"}],ip:[{required:!0,message:"请输入设备IP",trigger:"blur"},{pattern:/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,message:"请输入正确的IP地址",trigger:"blur"}],importance:[{required:!0,message:"请选择重要性",trigger:"change"}]}}},methods:{onShow:function(){this.visible=!0,this.resetForm()},onHide:function(){this.visible=!1,this.loading=!1,this.resetForm()},resetForm:function(){this.form={notes:"",ip:"",importance:void 0,group_id:void 0,position:"",person_liable:"",contact:""},this.$refs.form&&this.$refs.form.clearValidate()},handleSave:function(){var e=this;this.$refs.form.validate((function(t){t&&(e.loading=!0,e.$emit("save",e.form),setTimeout((function(){e.loading=!1,e.onHide()}),1e3))}))}}},$=E,F=(a("a0ba"),a("2877")),M=Object(F["a"])($,N,I,!1,null,"58cb69b9",null),K=M.exports,B=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,title:"编辑配置",width:"600px","close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.onHide}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"重要性",prop:"importance"}},[a("el-select",{attrs:{placeholder:"请选择重要性"},model:{value:e.form.importance,callback:function(t){e.$set(e.form,"importance",t)},expression:"form.importance"}},[a("el-option",{attrs:{label:"高",value:1}}),a("el-option",{attrs:{label:"中",value:2}}),a("el-option",{attrs:{label:"低",value:3}})],1)],1),a("el-form-item",{attrs:{label:"设备分组",prop:"group_id"}},[a("el-select",{attrs:{placeholder:"请选择设备分组"},model:{value:e.form.group_id,callback:function(t){e.$set(e.form,"group_id",t)},expression:"form.group_id"}},e._l(e.groupData,(function(e){return a("el-option",{key:e.id,attrs:{label:e.groupName,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"位置",prop:"position"}},[a("el-input",{attrs:{placeholder:"请输入位置"},model:{value:e.form.position,callback:function(t){e.$set(e.form,"position",t)},expression:"form.position"}})],1),a("el-form-item",{attrs:{label:"负责人",prop:"person_liable"}},[a("el-input",{attrs:{placeholder:"请输入负责人"},model:{value:e.form.person_liable,callback:function(t){e.$set(e.form,"person_liable",t)},expression:"form.person_liable"}})],1),a("el-form-item",{attrs:{label:"联系方式",prop:"contact"}},[a("el-input",{attrs:{placeholder:"请输入联系方式"},model:{value:e.form.contact,callback:function(t){e.$set(e.form,"contact",t)},expression:"form.contact"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.onHide}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSave}},[e._v("保存")])],1)],1)},z=[],T=(a("b64b"),{name:"EditConfigModal",props:{groupData:{type:Array,default:function(){return[]}},currentConfig:{type:Object,default:function(){return{}}}},data:function(){return{visible:!1,loading:!1,form:{importance:void 0,group_id:void 0,position:"",person_liable:"",contact:""},rules:{importance:[{required:!0,message:"请选择重要性",trigger:"change"}]}}},watch:{currentConfig:{handler:function(e){e&&Object.keys(e).length>0&&(this.form={importance:e.importance,group_id:e.group_id,position:e.position||"",person_liable:e.person_liable||"",contact:e.contact||""})},deep:!0,immediate:!0}},methods:{onShow:function(){this.visible=!0,this.currentConfig&&Object.keys(this.currentConfig).length>0&&(this.form={importance:this.currentConfig.importance,group_id:this.currentConfig.group_id,position:this.currentConfig.position||"",person_liable:this.currentConfig.person_liable||"",contact:this.currentConfig.contact||""})},onHide:function(){this.visible=!1,this.loading=!1,this.$refs.form&&this.$refs.form.clearValidate()},handleSave:function(){var e=this;this.$refs.form.validate((function(t){t&&(e.loading=!0,e.$emit("save",e.form),setTimeout((function(){e.loading=!1,e.onHide()}),1e3))}))}}}),V=T,Y=(a("469c"),Object(F["a"])(V,B,z,!1,null,"7ed30d58",null)),J=Y.exports,G=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"echarts-container",staticStyle:{width:"100%",height:"60px"}},[a("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})])},Z=[],q=(a("a630"),a("3ca3"),a("313e")),H={name:"EchartsTest",props:{record:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){this.initChart()},beforeDestroy:function(){this.chart&&this.chart.dispose()},watch:{record:{handler:function(){this.updateChart()},deep:!0}},methods:{initChart:function(){this.chart=q["b"](this.$refs.chart),this.updateChart()},updateChart:function(){if(this.chart){var e={grid:{left:0,right:0,top:5,bottom:5},xAxis:{type:"category",show:!1,data:["1","2","3","4","5","6","7","8","9","10"]},yAxis:{type:"value",show:!1},series:[{data:this.generateData(),type:"line",smooth:!0,symbol:"none",lineStyle:{color:"#1890ff",width:2},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.3)"},{offset:1,color:"rgba(24, 144, 255, 0.1)"}]}}}]};this.chart.setOption(e)}},generateData:function(){return this.record&&this.record.data?this.record.data:Array.from({length:10},(function(){return Math.floor(100*Math.random())}))}}},W=H,Q=(a("324f"),Object(F["a"])(W,G,Z,!1,null,"5bcece60",null)),X=Q.exports,ee=a("f5af"),te=a.n(ee),ae=a("6003"),re=a.n(ae),ne=a("3452"),oe=a.n(ne),ie={manage:1,operator:2,audit:3},se={name:"DeviceList",components:{AddDeviceModal:K,EditConfigModal:J,EchartsTest:X},data:function(){return{activeKey:"0",isShow:!1,panes:[],timer:null,currentPage:1,currentPageSize:10,currentUserPage:1,currentUserPageSize:10,userTotal:0,selectedRowKeys:[],selectedDevList:[],userSelectedRowKeys:[],searchForm:{fireName:"",ip:"",onlinStatus:""},searchValue:{},visible:!1,remarkVisible:!1,userVisible:!1,currentData_id:null,currentDeviceId:0,auditPwdList:[],isManualErr:!1,isLocked:!1,iframeShow:!1,currentConfig:null,curDevUserList:[],topoData:{},groupData:[],auditData:[],auditTotal:0,loading:!1,onlineIcon:te.a,unonlineIcon:re.a}},mounted:function(){var e=this;this.getDeviceList(),this.getTopoDataFunc(),this.getGroupList(),window.addEventListener("message",this.receiveMessageFromIndex,!1),this.timer=setInterval((function(){return e.getDeviceList()}),3e4);var t=localStorage.getItem("auditpanes"),a=localStorage.getItem("auditUserPwdList");t&&(t=JSON.parse(t),this.panes=t),a&&(a=JSON.parse(a),this.auditPwdList=a)},beforeDestroy:function(){window.removeEventListener("message",this.receiveMessageFromIndex),this.timer&&clearInterval(this.timer)},methods:{toggleShow:function(){this.isShow=!this.isShow},getGroupList:function(e){var t=this;L().then((function(a){0===a.retcode?(t.groupData=a.data,e&&e()):t.$message.error(a.message)})).catch((function(e){console.error("获取分组列表失败:",e)}))},receiveMessageFromIndex:function(e){var t=this.auditPwdList,a=this.isLocked,r=this.curDevUserList;if(void 0!==e){var n=e.data.status,o={};if("success"!==n)if("loadSuccess"===e.data){var i=t.findIndex((function(t){return t.ip===e.origin.substring(8)}));-1===i||e.data["type"]||this.handleAutoLogin(e.origin.substring(8),t[i]["UserNamePwd"])}else if(e.data.type&&"fail"===e.data.state);else{var s=t.some((function(t){return t.ip===e.origin.substring(8)}));if(!a){if(s){var c=t.findIndex((function(t){return t.ip===e.origin.substring(8)}));t[c]["UserNamePwd"]=e.data}else o["ip"]=e.origin.substring(8),o["UserNamePwd"]=e.data,t.push(o);localStorage.setItem("auditUserPwdList",JSON.stringify(t)),this.auditPwdList=t}}else{var l=t.findIndex((function(t){return t.ip===e.origin.substring(8)})),u=t[l].accountId;t[l]&&(t[l]["roleName"]=e.data.roleName,this.auditPwdList=t,this.isManualErr=!1,this.isLocked=!1,this.getIframeData(e.data.roleName,e.origin.substring(8)));var d=r.find((function(t){return t.account_name===e.data.userName}));d&&(u=d.id),u&&this.loginSuccessSaveLog(u)}}},loginSuccessSaveLog:function(e){v({account_id:e}).then((function(e){console.log("登录日志保存成功")})).catch((function(e){console.error("登录日志保存失败:",e)}))},handleAutoLogin:function(e,t){var a=document.querySelectorAll("iframe");a.forEach((function(a,r){var n=a.getAttribute("src"),o=n.substring(8);if(e===o){var i=t.split(":");a.contentWindow.postMessage({username:i[0],pwd:i[1]},"https://".concat(e))}}))},getIframeData:function(e,t){var a=localStorage.getItem("auditUserPwdList");if(a){var r=JSON.parse(a).findIndex((function(e){return e.ip===t})),n=oe.a.enc.Base64.stringify(oe.a.enc.Utf8.parse(JSON.parse(a)[r]["UserNamePwd"])),o=this.currentDeviceId;0!==o&&p({auth:n,device_id:o,account_role:ie[e]}).then((function(e){0===e.code&&console.log("添加用户成功")})).catch((function(e){console.error("添加用户失败:",e)}))}},getDeviceList:function(){var e=this;this.loading=!0;var t=this.currentPageSize,a=this.currentPage,r=this.searchValue;c({_limit:t,_page:a,queryParams:r,type:2}).then((function(r){if(0===r.code){for(var n=r.data.items||[],i=0;i<n.length;i++)n[i]=Object(o["a"])({number:(a-1)*t+i+1},n[i]);e.auditData=n,e.auditTotal=r.data.total||0,e.selectedRowKeys=[],e.loading=!1}else e.$message.error(r.message),e.loading=!1})).catch((function(t){console.error("获取设备列表失败:",t),e.loading=!1}))},getUserListData:function(e){var t=this,a=this.currentUserPage,r=this.currentUserPageSize,n=this.currentDeviceId;u({device_id:n,page:a,per_page:r}).then((function(a){0===a.code&&a.data.total&&(t.userTotal=a.data.total,t.curDevUserList=a.data.items),e&&e(a)})).catch((function(e){console.error("获取用户列表失败:",e)}))},onSelectChange:function(e){this.selectedRowKeys=e.map((function(e){return e.id})),this.selectedDevList=e.map((function(e){return e.ip}))},onUserSelectChange:function(e){this.userSelectedRowKeys=e.map((function(e){return e.id}))},onShowSizeChange:function(e,t){this.currentPage=t,this.currentPageSize=e,this.getDeviceList()},onUserShowSizeChange:function(e,t){this.currentUserPage=t,this.currentUserPageSize=e,this.getUserListData()},handlePageChange:function(e){this.currentPage=e,this.getDeviceList()},handleUserPageChange:function(e){this.currentUserPage=e,this.getUserListData()},handleAdvancedSearch:function(){var e={};this.searchForm.fireName&&(e.fireName=this.searchForm.fireName),this.searchForm.ip&&(e.originIp=this.searchForm.ip),this.searchForm.onlinStatus&&(e.onlinStatus=this.searchForm.onlinStatus),this.searchValue=e,this.currentPage=1,this.getDeviceList()},handleReset:function(){this.searchForm={fireName:"",ip:"",onlinStatus:""},this.searchValue={},this.currentPage=1,this.getDeviceList()},handleAddClick:function(){this.$refs.addModal.onShow()},handleLook:function(e){1===e.status?window.open("https://".concat(e.ip,"/#/login?type=smp")):this.$message.error("设备不在线，无法查看!")},handlePing:function(e){var t=this;y({ip:e.ip}).then((function(e){0===e.code?0===e.data?t.$message.success("Ping成功"):t.$message.error("网络不通"):t.$message.error(e.message)})).catch((function(e){console.error("Ping失败:",e)}))},getUserList:function(e,t){var a=this,r=this.auditPwdList;u({device_id:e,page:1,per_page:10}).then((function(e){if(0===e.code){if(e.data.items&&e.data.items.length>0){var n=e.data.default_account_auth;n&&n.auth&&(n.auth=oe.a.enc.Base64.parse(n.auth).toString(oe.a.enc.Utf8));var o=r.findIndex((function(e){return e.ip===t}));if(-1===o){var i={};i["ip"]=t,i["UserNamePwd"]=n.auth,i["accountId"]=n.account_id,r.push(i)}else n&&(r[o]["UserNamePwd"]=n.auth,r[o]["accountId"]=n.account_id);a.auditPwdList=r}}else a.$message.error(e.message)})).catch((function(e){console.error("获取用户列表失败:",e)}))},handleCancelClick:function(){this.visible=!1,this.remarkVisible=!1,this.userVisible=!1},modifyDefLoginAccount:function(e){var t=this;f(e).then((function(e){0===e.code?t.$message.success("保存默认登录用户配置成功"):t.$message.error(e.message)})).catch((function(e){console.error("修改默认登录账号失败:",e)}))},handleOkUser:function(){var e=this.currentDeviceId,t=this.userSelectedRowKeys,a=t[0];a&&this.modifyDefLoginAccount({deviceId:e,accountId:a}),this.visible=!1,this.remarkVisible=!1,this.userVisible=!1},handleShowConfig:function(e){this.currentData_id=e.id,this.currentConfig=e,this.$refs.configModal.onShow()},handleUser:function(e){var t=this;this.userVisible=!0,this.currentDeviceId=e.id,this.getUserListData((function(e){if(e.data&&e.data.default_account_auth){var a=e.data.default_account_auth.account_id;t.userSelectedRowKeys=[a]}}))},handleDeleteUser:function(e){var t=this,a=this.auditPwdList,r=this.userSelectedRowKeys;m({account_id:Number(e.id)}).then((function(n){if(0===n.code){t.$message.success("删除成功"),a=a.filter((function(t){return t.accountId!==e.id})),localStorage.setItem("auditUserPwdList",JSON.stringify(a));var o=r?r[0]:null;r=o&&o===e.id?[]:r,t.auditPwdList=a,t.userSelectedRowKeys=r,t.getUserListData()}else t.$message.error(n.message)})).catch((function(e){console.error("删除用户失败:",e)}))},onChange:function(e){this.activeKey=e.name},onEdit:function(e,t){if("remove"===t){var a=this.panes,r=this.auditPwdList;a=a.filter((function(t){return t.key!==e})),r=r.filter((function(t){return t.ip!==e})),this.handleAutoLoginOut(e),localStorage.setItem("auditUserPwdList",JSON.stringify(r)),localStorage.setItem("auditpanes",JSON.stringify(a)),this.panes=a,this.auditPwdList=r,this.remove(e)}},handleAutoLoginOut:function(e){var t=document.querySelectorAll("iframe");t.forEach((function(t,a){var r=t.getAttribute("src"),n=r.substring(8);e===n&&t.contentWindow.postMessage("logout","*")}))},remove:function(e){var t,a=this.activeKey;this.panes.forEach((function(a,r){a.key===e&&(t=r-1)}));var r=this.panes.filter((function(t){return t.key!==e}));r.length&&a===e&&(a=t>=0?r[t].key:r[0].key),this.panes=r,this.activeKey=1===this.panes.length?"0":a},handleConifgSave:function(e){var t=this,a=this.currentData_id,r=this.currentConfig;if(r.importance!==e.importance||r.position!==e.position||r.person_liable!==e.person_liable||r.contact!==e.contact||r.group_id!==e.group_id){var n={device_id:a,importance:parseInt(e.importance),group_id:void 0===e.group_id?"":e.group_id,position:void 0===e.position?"":e.position,person_liable:void 0===e.person_liable?"":e.person_liable,contact:void 0===e.contact?"":e.contact};C(n).then((function(e){0===e.code?(t.$message.success("编辑成功"),t.remarkVisible=!1,t.getDeviceList()):t.$message.error(e.message),t.$refs.configModal.onHide()})).catch((function(e){console.error("编辑设备失败:",e)}))}else this.$message.error("配置未更改！")},handleDeleteClick:function(e){var t=this,a=[],r=[];e&&e.id?(a.push(e.id),r.push(e.ip)):(a=this.selectedRowKeys,r=this.selectedDevList),a.length?this.$confirm("确认删除这".concat(a.length,"条数据吗？"),"删除",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){k({device_ids:a}).then((function(e){0===e.code?(t.getDeviceList(),t.$message.success("删除成功")):t.$message.error(e.message)})).catch((function(e){console.error("删除设备失败:",e)}))})).catch((function(){console.log("取消删除")})):this.$message.error("至少选中一条数据")},handleAddDevice:function(e){var t=this,a=Object(o["a"])(Object(o["a"])({},e),{},{category:2});S(a).then((function(e){0===e.code?(t.$message.success("新增成功"),t.getDeviceList()):t.$message.error(e.message)})).catch((function(e){console.error("添加设备失败:",e)}))},getTopoDataFunc:function(e){var t=this;O().then((function(a){0===a.code?(t.topoData=a.data,e&&e()):t.$message.error(a.message)})).catch((function(e){console.error("获取拓扑数据失败:",e)}))},generateNode:function(e){return Object(o["a"])({id:this.guid(),type:"node",size:"50",shape:"koni-custom-node",color:"#69C0FF",labelOffsetY:38},e)},guid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,a="x"===e?t:3&t|8;return a.toString(16)}))},saveTopoData:function(e){var t=this;U({topology_text:e}).then((function(e){0===e.code?t.getDeviceList():t.$message.error(e.message)})).catch((function(e){console.error("保存拓扑数据失败:",e)}))}}},ce=se,le=(a("68462"),Object(F["a"])(ce,r,n,!1,null,"77282d7e",null));t["default"]=le.exports},"324f":function(e,t,a){"use strict";var r=a("26c1"),n=a.n(r);n.a},"32e7":function(e,t,a){},"469c":function(e,t,a){"use strict";var r=a("32e7"),n=a.n(r);n.a},6003:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAQ9JREFUKBWdkjEOgkAQRWcJNBALEqgptPUSxhrv4Bk4hmfwENbGS9jSUENCYaCBgP+vO4YYKyYZdvfvPGZnZ40srK7rDZbFPM85xp3bKo0xN8wvaZq+nCZGJ03THABc4ZlqyxFwBT8nSfKgbkEH3QGZYRik73sZx9Fyvu9LGIYSBIEAnOFHwsYd78lMBLquWyb6zqMosj8AWEHc+/iwpoyZCE3TJPiZtG1roTiOBbXZPWaFZ4ALD9CJEcxGI0TnUem65p7GgMk9rLcUtSbNRE1NNY2BviO4ygiWJHl7NNb0a6ppDPZLD4WyufbGOPIi6Ayi63oZQ2Z1O9Y/AB6BturJfVDbQ33k7K1tE8a/j/wNvRO1oIjXJdYAAAAASUVORK5CYII="},68462:function(e,t,a){"use strict";var r=a("1dac"),n=a.n(r);n.a},a0ba:function(e,t,a){"use strict";var r=a("00e5"),n=a.n(r);n.a},c9d9:function(e,t,a){"use strict";a("99af"),a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),a("5319"),a("2ca0");var r=a("bc3a"),n=a.n(r),o=a("4360"),i=a("a18c"),s=a("a47e"),c=a("f7b5"),l=a("f907"),u=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",r=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),u=r.NODE_ENV,d=r.VUE_APP_IS_MOCK,p=r.VUE_APP_BASE_API,h="true"===d?"":p;"production"===u&&(h="");var f={baseURL:h,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===u&&(f.timeout=a),t){case"upload":f.headers["Content-Type"]="multipart/form-data",f["processData"]=!1,f["contentType"]=!1;break;case"download":f["responseType"]="blob";break;case"eventSource":break;default:break}var g=n.a.create(f);return g.interceptors.request.use((function(e){var t=o["a"].getters.token;return""!==t&&(e.headers["access_token"]=t,e.url.startsWith("/api2/")&&(e.headers["Authorization"]="Basic YWRtaW5pc3RyYXRvcjpBZG1pbjEyMw==")),e}),(function(e){Object(c["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:e,print:!0}),Promise.reject("response-err:"+e)})),g.interceptors.response.use((function(e){var a=void 0===e.headers["code"]?200:Number(e.headers["code"]),r=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",r=arguments.length>2?arguments[2]:void 0,n="";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n="error"),e.data.code>=2e3&&e.data.code<3e3&&(n="warning"),Object(c["a"])({i18nCode:"ajax.".concat(a,".").concat(t),type:n}),Promise.reject("response-err-status:".concat(r||l["a"][a][t]," \nerr-question: ").concat(s["a"].t("ajax.".concat(a,".").concat(t))))};switch(e.data.code){case l["a"].exception.system:t("system");break;case l["a"].exception.server:t("server");break;case l["a"].exception.session:r();break;case l["a"].exception.access:r();break;case l["a"].exception.certification:t("certification");break;case l["a"].exception.auth:t("auth"),i["a"].replace({path:"/401"});break;case l["a"].exception.token:t("token");break;case l["a"].exception.param:t("param");break;case l["a"].exception.idempotency:t("idempotency");break;case l["a"].exception.ip:t("ip"),o["a"].dispatch("user/reset"),i["a"].replace({path:"/login"});break;case l["a"].exception.upload:t("upload");break;case l["a"].attack.xss:t("xss","attack");break;default:t("code","exception",-1);break}};switch(t){case"upload":if(0===a)return e.data.data;n();break;case"download":if(0===a)return{data:e.data,fileName:decodeURI(e.headers["file-name"])};n();break;default:if(0===e.data.code||0===e.data.retcode)return e.data;n();break}}),(function(e){var a=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))};return"upload"===t?(Object(c["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),403==e.response.status&&a(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(s["a"].t("ajax.service.upload")))):(Object(c["a"])({i18nCode:"ajax.service.timeout",type:"error"}),403==e.response.status&&a(),Promise.reject("response-err-status:".concat(e," \nerr-question: ").concat(s["a"].t("ajax.service.timeout"))))})),g(e)};t["a"]=u},f5af:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAThJREFUKBWdUjtOA0EMfd6lY5NJSQGsFCjS0IMEDUKigztwhhyDM3AIOiREAUjQ06SIkBZRUGbDUm7MPI+MopBqLc3P9hvbzxYsybNOeg10vIBcCHSfJoVMM+htAbk+ltG3u4tf7nRy2kJv4rt03cpZ5ZCrcxk9UG9AghbAvULlEzO84gNfaAy3hQKH2MU2BtFZNAPOCBamN4e+Ra/yBRUe8W6A5Y2/n2CIo5RM1YccbLAmghjpKYJiVNSzGs1PilhsFgiDYLYdBEYuicli8Zf8nelpPAmq5zXatrXFO3W00YdC8mLKuseH1+SRqHNxnfuQ8QjsJkx1SijZo7CmVXGd+1hv2Vw6knKyRyJCPyDPc1u8U0cbfSjE/GtHYtbsf9u6dlCHTgPg33YaOQf7kKfepjYZEWuG/BdX/57fCwmc7AAAAABJRU5ErkJggg=="}}]);