(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b6903b6"],{"078a":function(e,t,a){"use strict";var i=a("2b0e"),n=(a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319"),{bind:function(e,t,a){var i=[e.querySelector(".el-dialog__header"),e.querySelector(".el-dialog")],n=i[0],o=i[1];n.style.cssText+=";cursor:move;",o.style.cssText+=";top:0px;";var r=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();n.onmousedown=function(e){var t=[e.clientX-n.offsetLeft,e.clientY-n.offsetTop,o.offsetWidth,o.offsetHeight,document.body.clientWidth,document.body.clientHeight],i=t[0],l=t[1],s=t[2],c=t[3],u=t[4],d=t[5],p=[o.offsetLeft,u-o.offsetLeft-s,o.offsetTop,d-o.offsetTop-c],m=p[0],f=p[1],g=p[2],h=p[3],b=[r(o,"left"),r(o,"top")],v=b[0],S=b[1];v.includes("%")?(v=+document.body.clientWidth*(+v.replace(/%/g,"")/100),S=+document.body.clientHeight*(+S.replace(/%/g,"")/100)):(v=+v.replace(/px/g,""),S=+S.replace(/px/g,"")),document.onmousemove=function(e){var t=e.clientX-i,n=e.clientY-l;-t>m?t=-m:t>f&&(t=f),-n>g?n=-g:n>h&&(n=h),o.style.cssText+=";left:".concat(t+v,"px;top:").concat(n+S,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),o=function(e){e.directive("el-dialog-drag",n)};window.Vue&&(window["el-dialog-drag"]=n,i["default"].use(o)),n.elDialogDrag=o;t["a"]=n},2532:function(e,t,a){"use strict";var i=a("23e7"),n=a("5a34"),o=a("1d80"),r=a("ab13");i({target:"String",proto:!0,forced:!r("includes")},{includes:function(e){return!!~String(o(this)).indexOf(n(e),arguments.length>1?arguments[1]:void 0)}})},"5a34":function(e,t,a){var i=a("44e7");e.exports=function(e){if(i(e))throw TypeError("The method doesn't accept regular expressions");return e}},8523:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("header",{staticClass:"table-header"},[a("section",{staticClass:"table-header-main"},[a("section",{staticClass:"table-header-search"},[a("section",{staticClass:"table-header-search-input"},[a("el-input",{attrs:{clearable:"",placeholder:e.$t("tip.placeholder.query",[e.$t("visualization.management.fuzzyQuery")]),"prefix-icon":"soc-icon-search"},on:{change:e.clickQueryScreenTable},model:{value:e.data.fuzzyField,callback:function(t){e.$set(e.data,"fuzzyField",t)},expression:"data.fuzzyField"}})],1),a("section",{staticClass:"table-header-search-button"},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}]},[e._v(" "+e._s(e.$t("button.query"))+" ")])],1)]),a("section",{staticClass:"table-header-button"},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],on:{click:e.clickAddScreen}},[e._v(" "+e._s(e.$t("button.add"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],on:{click:e.clickBatchDeleteScreen}},[e._v(" "+e._s(e.$t("button.batch.delete"))+" ")])],1)])]),a("main",{staticClass:"table-body"},[a("header",{staticClass:"table-body-header"},[a("h2",{staticClass:"table-body-title"},[e._v(" "+e._s(e.$t("visualization.management.title"))+" ")])]),a("main",{staticClass:"table-body-main"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.data.loading,expression:"data.loading"}],ref:"screenTable",attrs:{data:e.data.table,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"current-change":e.screenTableRowChange,"selection-change":e.screenTableSelectsChange}},[a("el-table-column",{attrs:{type:"selection"}}),a("el-table-column",{attrs:{label:e.$t("visualization.management.table.name"),prop:"screenName","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:e.$t("visualization.management.table.url"),prop:"screenUrl","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:e.$t("visualization.management.table.description"),prop:"screenDescription","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{fixed:"right",width:"210"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"find",expression:"'find'"}],staticClass:"el-button--blue",on:{click:function(a){return e.clickLookScreen(t.row)}}},[e._v(" "+e._s(e.$t("button.look"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticClass:"el-button--blue",on:{click:function(a){return e.clickUpdateScreen(t.row)}}},[e._v(" "+e._s(e.$t("button.update"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticClass:"el-button--red",on:{click:function(a){return e.clickDeleteScreen(t.row)}}},[e._v(" "+e._s(e.$t("button.delete"))+" ")])]}}])})],1)],1)]),a("footer",{staticClass:"table-footer"},[e.pagination.visible?a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.pagination.pageNum,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.screenTableSizeChange,"current-change":e.screenTableCurrentChange}}):e._e()],1),a("au-dialog",{attrs:{visible:e.dialog.visible.add,title:e.dialog.title.add,"form-data":e.dialog.form.add},on:{"update:visible":function(t){return e.$set(e.dialog.visible,"add",t)},"on-submit":e.clickSubmitAddScreen}}),a("au-dialog",{attrs:{visible:e.dialog.visible.update,title:e.dialog.title.update,"form-data":e.dialog.form.update},on:{"update:visible":function(t){return e.$set(e.dialog.visible,"update",t)},"on-submit":e.clickSubmitUpdateScreen}})],1)},n=[],o=(a("d81d"),a("d3b7"),a("ac1f"),a("25f0"),a("1276"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("custom-dialog",{ref:"dialogTemp",attrs:{visible:e.visible,title:e.title,width:e.width},on:{"on-close":e.clickCancelDialog,"on-submit":e.clickSubmitForm}},[a("el-form",{ref:"formTemp",attrs:{model:e.formData,rules:e.rule,"label-width":"25%"}},[a("el-form-item",{attrs:{label:e.$t("visualization.management.table.name"),prop:"screenName"}},[a("el-input",{staticClass:"width-small",model:{value:e.formData.screenName,callback:function(t){e.$set(e.formData,"screenName",t)},expression:"formData.screenName"}})],1),a("el-form-item",{attrs:{label:e.$t("visualization.management.table.url"),prop:"screenUrl"}},[a("el-input",{staticClass:"width-small",model:{value:e.formData.screenUrl,callback:function(t){e.$set(e.formData,"screenUrl",t)},expression:"formData.screenUrl"}})],1),a("el-form-item",{attrs:{label:e.$t("visualization.management.table.description")}},[a("el-input",{staticClass:"width-small",attrs:{type:"textarea"},model:{value:e.formData.screenDescription,callback:function(t){e.$set(e.formData,"screenDescription",t)},expression:"formData.screenDescription"}})],1)],1)],1)}),r=[],l=a("d465"),s=a("f7b5"),c={components:{CustomDialog:l["a"]},props:{visible:{required:!0,type:Boolean},title:{type:String,default:""},width:{type:String,default:"35%"},formData:{type:Object,default:function(){return{}}}},data:function(){return{dialogVisible:this.visible,rule:{screenName:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],screenUrl:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}]}}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit("update:visible",e)}},methods:{clickCancelDialog:function(){this.$refs.dialogTemp.end(),this.dialogVisible=!1},clickSubmitForm:function(){var e=this;this.$refs.formTemp.validate((function(t){t?e.$confirm(e.$t("tip.confirm.submit"),e.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){e.$emit("on-submit",e.formData),e.clickCancelDialog()})):Object(s["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1}))})),this.$refs.dialogTemp.end()}}},u=c,d=a("2877"),p=Object(d["a"])(u,o,r,!1,null,null,null),m=p.exports,f=a("4020");function g(e){return Object(f["a"])({url:"/visualization/screenmanagement",method:"post",data:e||{}})}function h(e){return Object(f["a"])({url:"/visualization/screenmanagement/".concat(e),method:"delete"})}function b(e){return Object(f["a"])({url:"/visualization/screenmanagement",method:"put",data:e||{}})}function v(e){return Object(f["a"])({url:"/visualization/screenmanagement",method:"get",params:e||{}})}var S=a("a78e"),y=a.n(S),w={name:"VisualizationManagement",components:{AuDialog:m},data:function(){return{data:{loading:!1,table:[],selected:[],fuzzyField:""},pagination:{visible:!0,pageSize:this.$store.getters.pageSize,pageNum:1,total:0,currentRow:{}},dialog:{visible:{add:!1,update:!1},title:{add:this.$t("dialog.title.add",[this.$t("visualization.management.title")]),update:this.$t("dialog.title.update",[this.$t("visualization.management.title")])},form:{add:{screenName:"",screenUrl:"",screenDescription:""},update:{screenName:"",screenUrl:"",screenDescription:""}}}}},mounted:function(){this.getScreenTableData()},methods:{addScreen:function(e){var t=this;g(e).then((function(e){1===e?Object(s["a"])({i18nCode:"tip.add.success",type:"success"},(function(){t.getScreenTableData()})):2===e?Object(s["a"])({i18nCode:"tip.add.repeat",type:"error"}):Object(s["a"])({i18nCode:"tip.add.error",type:"error"})}))},deleteScreen:function(e){var t=this;this.$confirm(this.$t("tip.confirm.batchDelete"),this.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){h(e).then((function(a){a?Object(s["a"])({i18nCode:"tip.delete.success",type:"success"},(function(){var a=[t.pagination.pageNum,e.split(",")],i=a[0],n=a[1];n.length===t.data.table.length&&(t.pagination.pageNum=1===i?1:i-1),t.getScreenTableData()})):Object(s["a"])({i18nCode:"tip.delete.error",type:"error"})}))}))},updateScreen:function(e){var t=this;b(e).then((function(e){e?Object(s["a"])({i18nCode:"tip.update.success",type:"success"},(function(){t.getScreenTableData()})):Object(s["a"])({i18nCode:"tip.update.error",type:"error"})}))},getScreenTableData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.pagination.visible=!1,this.data.loading=!0,v(t).then((function(t){e.data.table=t.rows,e.pagination.total=t.total,e.pagination.visible=!0,e.data.loading=!1}))},clickAddScreen:function(){this.dialog.form.add={screenName:"",screenUrl:"",screenDescription:""},this.dialog.visible.add=!0},clickBatchDeleteScreen:function(){if(this.data.selected.length>0){var e=this.data.selected.map((function(e){return e.screenId})).toString();this.deleteScreen(e)}else Object(s["a"])({i18nCode:"tip.delete.prompt",type:"warning",print:!0})},clickQueryScreenTable:function(){this.pagination.pageNum=1,this.getScreenTableData({fuzzyField:this.data.fuzzyField,pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum})},clickLookScreen:function(e){this.openWindowDisplay(e.screenUrl)},clickUpdateScreen:function(e){this.dialog.form.update={screenId:e.screenId,screenName:e.screenName,screenUrl:e.screenUrl,screenDescription:e.screenDescription},this.dialog.visible.update=!0},clickDeleteScreen:function(e){this.deleteScreen(e.screenId)},clickSubmitAddScreen:function(e){this.addScreen(e)},clickSubmitUpdateScreen:function(e){this.updateScreen(e)},screenTableRowChange:function(e){this.pagination.currentRow=e},screenTableSelectsChange:function(e){this.data.selected=e},screenTableSizeChange:function(e){this.pagination.pageSize=e,this.getScreenTableData()},screenTableCurrentChange:function(e){this.pagination.pageNum=e,this.getScreenTableData()},openWindowDisplay:function(e){var t=this.$router.resolve({path:e});this.$store.dispatch("user/updatePath",e),y.a.set("store",JSON.stringify(this.$store.state)),window.open(t.href,"_blank")}}},C=w,$=Object(d["a"])(C,i,n,!1,null,null,null);t["default"]=$.exports},ab13:function(e,t,a){var i=a("b622"),n=i("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,"/./"[e](t)}catch(i){}}return!1}},caad:function(e,t,a){"use strict";var i=a("23e7"),n=a("4d64").includes,o=a("44d2"),r=a("ae40"),l=r("indexOf",{ACCESSORS:!0,1:0});i({target:"Array",proto:!0,forced:!l},{includes:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),o("includes")},d81d:function(e,t,a){"use strict";var i=a("23e7"),n=a("b727").map,o=a("1dde"),r=a("ae40"),l=o("map"),s=r("map");i({target:"Array",proto:!0,forced:!l||!s},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);