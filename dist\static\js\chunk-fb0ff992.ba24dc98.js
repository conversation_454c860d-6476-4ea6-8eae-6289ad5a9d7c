(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fb0ff992"],{"02c6":function(t,e,a){"use strict";a("99af"),a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),a("5319");var o=a("bc3a"),n=a.n(o),l=a("4360"),i=a("a18c"),s=a("a47e"),r=a("f7b5"),c=a("f907"),u=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",o=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),u=o.NODE_ENV,d=o.VUE_APP_IS_MOCK,m=o.VUE_APP_BASE_API,p="true"===d?"":m;"production"===u&&(p="");var v={baseURL:p,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===u&&(v.timeout=a),e){case"upload":v.headers["Content-Type"]="multipart/form-data",v["processData"]=!1,v["contentType"]=!1;break;case"download":v["responseType"]="blob";break;case"eventSource":break;default:break}var b=n.a.create(v);return b.interceptors.request.use((function(t){var e=l["a"].getters.token;return""!==e&&(t.headers["access_token"]=e),t}),(function(t){Object(r["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:t,print:!0}),Promise.reject("response-err:"+t)})),b.interceptors.response.use((function(t){var a=void 0===t.headers["code"]?200:Number(t.headers["code"]),o=function(){Object(r["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(l["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))},n=function(){var e=function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",o=arguments.length>2?arguments[2]:void 0,n="";return(500===t.data.code||t.data.code>=1e3&&t.data.code<2e3)&&(n="error"),t.data.code>=2e3&&t.data.code<3e3&&(n="warning"),Object(r["a"])({i18nCode:"ajax.".concat(a,".").concat(e),type:n}),Promise.reject("response-err-status:".concat(o||c["a"][a][e]," \nerr-question: ").concat(s["a"].t("ajax.".concat(a,".").concat(e))))};switch(t.data.code){case c["a"].exception.system:e("system");break;case c["a"].exception.server:e("server");break;case c["a"].exception.session:o();break;case c["a"].exception.access:o();break;case c["a"].exception.certification:e("certification");break;case c["a"].exception.auth:e("auth"),i["a"].replace({path:"/401"});break;case c["a"].exception.token:e("token");break;case c["a"].exception.param:e("param");break;case c["a"].exception.idempotency:e("idempotency");break;case c["a"].exception.ip:e("ip"),l["a"].dispatch("user/reset"),i["a"].replace({path:"/login"});break;case c["a"].exception.upload:e("upload");break;case c["a"].attack.xss:e("xss","attack");break;default:e("code","exception",-1);break}};switch(e){case"upload":if(a===c["a"].success)return t.data;n();break;case"download":if(a===c["a"].success)return{data:t.data,fileName:decodeURI(t.headers["file-name"])};n();break;default:if(t.data.code===c["a"].success||t.data.code===c["a"].exception.system)return t.data;n();break}}),(function(t){var a=function(){Object(r["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(l["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))};return"upload"===e?(Object(r["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),403==t.response.status&&a(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(s["a"].t("ajax.service.upload")))):(Object(r["a"])({i18nCode:"ajax.service.timeout",type:"error"}),403==t.response.status&&a(),Promise.reject("response-err-status:".concat(t," \nerr-question: ").concat(s["a"].t("ajax.service.timeout"))))})),b(t)};e["a"]=u},1894:function(t,e,a){},"1b3b":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.isShow?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"monitor"},[a("h2",{staticClass:"h2_title"},[t._v("监控设置")]),a("div",{staticClass:"table_container"},[t._m(0),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("在线状态")]),a("div",{staticClass:"content two"},[t._l(t.oneStateList,(function(e,o){return a("div",{key:o,staticClass:"msl-item"},[a("status-spot",{attrs:{bgColor:e.color}}),a("div",{staticClass:"label"},[t._v(t._s(e.label))])],1)})),a("el-select",{attrs:{placeholder:"请选择设备离线标准"},model:{value:t.monitorData.offlineJudge,callback:function(e){t.$set(t.monitorData,"offlineJudge",e)},expression:"monitorData.offlineJudge"}},t._l(t.deviceOfflineList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],2),a("div",{staticClass:"content three"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.alarmFlagOnline,callback:function(e){t.$set(t.monitorData,"alarmFlagOnline",e)},expression:"monitorData.alarmFlagOnline"}},[t._v("告警")]),a("el-select",{attrs:{placeholder:"请选择告警逻辑"},model:{value:t.monitorData.noticeDealOnline,callback:function(e){t.$set(t.monitorData,"noticeDealOnline",e)},expression:"monitorData.noticeDealOnline"}},[a("el-option",{attrs:{label:"在线<--\x3e离线",value:1}}),a("el-option",{attrs:{label:"在线--\x3e离线",value:2}})],1)],1),a("div",{staticClass:"content four"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagOnline,callback:function(e){t.$set(t.monitorData,"noticeFlagOnline",e)},expression:"monitorData.noticeFlagOnline"}},[t._v("通知")])],1)]),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("CPU使用率")]),a("div",{staticClass:"content two"},[t._v("沿用哨兵配置")]),a("div",{staticClass:"content three"},[t._v("沿用哨兵配置")]),a("div",{staticClass:"content four"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagCpu,callback:function(e){t.$set(t.monitorData,"noticeFlagCpu",e)},expression:"monitorData.noticeFlagCpu"}},[t._v("通知")])],1)]),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("内存使用率")]),a("div",{staticClass:"content two"},[t._v("沿用哨兵配置")]),a("div",{staticClass:"content three"},[t._v("沿用哨兵配置")]),a("div",{staticClass:"content four"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagMemory,callback:function(e){t.$set(t.monitorData,"noticeFlagMemory",e)},expression:"monitorData.noticeFlagMemory"}},[t._v("通知")])],1)]),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("硬盘使用率")]),a("div",{staticClass:"content two"},[t._v("沿用哨兵配置")]),a("div",{staticClass:"content three"},[t._v("沿用哨兵配置")]),a("div",{staticClass:"content four"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagDisk,callback:function(e){t.$set(t.monitorData,"noticeFlagDisk",e)},expression:"monitorData.noticeFlagDisk"}},[t._v("通知")])],1)]),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("系统配置")]),a("div",{staticClass:"content two"},[a("Progress",{attrs:{field:"warnThresholdSystem",monitorData:t.monitorData}})],1),a("div",{staticClass:"content three"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.alarmFlagSystem,callback:function(e){t.$set(t.monitorData,"alarmFlagSystem",e)},expression:"monitorData.alarmFlagSystem"}},[t._v("告警")]),a("el-select",{attrs:{placeholder:"请选择核查周期"},model:{value:t.monitorData.periodDealSystem,callback:function(e){t.$set(t.monitorData,"periodDealSystem",e)},expression:"monitorData.periodDealSystem"}},[a("el-option",{attrs:{label:"每天核查1次",value:1}}),a("el-option",{attrs:{label:"每周核查1次",value:2}}),a("el-option",{attrs:{label:"每月核查1次",value:3}}),a("el-option",{attrs:{label:"每季度核查1次",value:4}}),a("el-option",{attrs:{label:"每年核查1次",value:5}})],1),a("el-select",{attrs:{placeholder:"请选择核查时间"},model:{value:t.monitorData.timeDealSystem,callback:function(e){t.$set(t.monitorData,"timeDealSystem",e)},expression:"monitorData.timeDealSystem"}},[a("el-option",{attrs:{label:"0点",value:0}}),a("el-option",{attrs:{label:"2点",value:2}}),a("el-option",{attrs:{label:"4点",value:4}}),a("el-option",{attrs:{label:"6点",value:6}})],1)],1),a("div",{staticClass:"content four"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagSystem,callback:function(e){t.$set(t.monitorData,"noticeFlagSystem",e)},expression:"monitorData.noticeFlagSystem"}},[t._v("通知")])],1)]),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("策略配置")]),a("div",{staticClass:"content two"},[a("Progress",{attrs:{field:"warnThresholdSecurity",monitorData:t.monitorData}})],1),a("div",{staticClass:"content three"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.alarmFlagSecurity,callback:function(e){t.$set(t.monitorData,"alarmFlagSecurity",e)},expression:"monitorData.alarmFlagSecurity"}},[t._v("告警")]),a("el-select",{attrs:{placeholder:"请选择核查周期"},model:{value:t.monitorData.periodDealSecurity,callback:function(e){t.$set(t.monitorData,"periodDealSecurity",e)},expression:"monitorData.periodDealSecurity"}},[a("el-option",{attrs:{label:"每天核查1次",value:1}}),a("el-option",{attrs:{label:"每周核查1次",value:2}}),a("el-option",{attrs:{label:"每月核查1次",value:3}}),a("el-option",{attrs:{label:"每季度核查1次",value:4}}),a("el-option",{attrs:{label:"每年核查1次",value:5}})],1),a("el-select",{attrs:{placeholder:"请选择核查时间"},model:{value:t.monitorData.timeDealSecurity,callback:function(e){t.$set(t.monitorData,"timeDealSecurity",e)},expression:"monitorData.timeDealSecurity"}},[a("el-option",{attrs:{label:"0点",value:0}}),a("el-option",{attrs:{label:"2点",value:2}}),a("el-option",{attrs:{label:"4点",value:4}}),a("el-option",{attrs:{label:"6点",value:6}})],1)],1),a("div",{staticClass:"content four"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagSecurity,callback:function(e){t.$set(t.monitorData,"noticeFlagSecurity",e)},expression:"monitorData.noticeFlagSecurity"}},[t._v("通知")])],1)]),a("div",{staticClass:"content_container"},[a("div",{staticClass:"content one"},[t._v("授权")]),a("div",{staticClass:"content two auth"},[a("div",{staticClass:"one_row"},t._l(t.fourStateList,(function(e,o){return a("div",{key:o,staticClass:"msl-item"},[a("status-spot",{attrs:{bgColor:e.color}}),a("div",{staticClass:"label"},[t._v(t._s(e.label))])],1)})),0),a("div",{staticClass:"two_row"},[t._l([{label:"临期授权",color:"yellow"}],(function(e,o){return a("div",{key:o,staticClass:"msl-item"},[a("status-spot",{attrs:{bgColor:e.color}}),a("div",{staticClass:"label"},[t._v(t._s(e.label))])],1)})),t._v(" 授权到期前 "),a("el-select",{attrs:{placeholder:"请选择到期时间"},model:{value:t.monitorData.authNearlyJudge,callback:function(e){t.$set(t.monitorData,"authNearlyJudge",e)},expression:"monitorData.authNearlyJudge"}},[a("el-option",{attrs:{label:"7天",value:7}}),a("el-option",{attrs:{label:"15天",value:15}}),a("el-option",{attrs:{label:"30天",value:30}})],1),t._v(" 认定为临期 ")],2)]),a("div",{staticClass:"content three auth"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.alarmFlagAuth,callback:function(e){t.$set(t.monitorData,"alarmFlagAuth",e)},expression:"monitorData.alarmFlagAuth"}},[t._v("告警")]),a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.alarmFlagAuthNearly,callback:function(e){t.$set(t.monitorData,"alarmFlagAuthNearly",e)},expression:"monitorData.alarmFlagAuthNearly"}},[t._v("告警")])],1),a("div",{staticClass:"content four auth"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagAuth,callback:function(e){t.$set(t.monitorData,"noticeFlagAuth",e)},expression:"monitorData.noticeFlagAuth"}},[t._v("通知")]),a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.monitorData.noticeFlagAuthNearly,callback:function(e){t.$set(t.monitorData,"noticeFlagAuthNearly",e)},expression:"monitorData.noticeFlagAuthNearly"}},[t._v("通知")])],1)])]),a("div",{staticClass:"monitor-btn"},[a("el-button",{on:{click:t.handleReset}},[t._v("恢复出厂配置")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSave}},[t._v("保存配置")])],1)]):t._e()},n=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"title_container"},[a("div",{staticClass:"title one"},[t._v("哨兵配置项")]),a("div",{staticClass:"title two"},[t._v("异常设定")]),a("div",{staticClass:"title three"},[t._v("监测告警开关、告警逻辑、核查周期和聚合策略等设定")]),a("div",{staticClass:"title four"},[t._v("通知设定")])])}],l=(a("4160"),a("b64b"),a("159b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"progress"},[a("div",{staticClass:"progress-left"},[a("div",{ref:"progressBar",staticClass:"progress-left-bar",on:{mouseleave:t.mouseleave,mousemove:t.updateProgress}},[a("div",{ref:"plbInner",staticClass:"plb-inner",style:{width:t.plbInnerWidth,background:"#0ce30c"}}),a("div",{ref:"plbBtn",staticClass:"plb-btn",style:{left:t.plbBtnLeft},on:{mousedown:function(e){return t.startDrag(e,"plbBtn")},mouseup:t.endDrag}})]),a("div",{staticClass:"progress-left-barP"}),a("span",[t._v(t._s(t.endVal)+"%")])]),a("div",{staticClass:"progress-right"},[a("span",[t._v("偏离基线"+t._s(t.monitorData[t.field])+"%")])])])}),i=[],s={props:{field:{type:String,default:""},monitorData:{type:Object,default:{}}},data:function(){return{endVal:100,plbInnerWidth:0,plbBtnLeft:0,barFlag:"",isDragging:!1}},mounted:function(){this.init()},methods:{init:function(){this.plbInnerWidth="".concat(this.monitorData[this.field],"%"),this.plbBtnLeft="".concat(this.monitorData[this.field],"%")},startDrag:function(t,e){this.isDragging=!0,this.barFlag=e},endDrag:function(){this.isDragging=!1},mouseleave:function(){this.isDragging=!1},updateProgress:function(t){if(this.isDragging){var e=this.$refs.progressBar.getBoundingClientRect(),a=e.width,o=t.clientX-e.left;o<0?o=0:o>a&&(o=a);var n=o/a*100;this.plbInnerWidth="".concat(n,"%"),this.plbBtnLeft="".concat(n,"%"),this.monitorData[this.field]=Math.round(n)}}}},r=s,c=(a("96ba"),a("2877")),u=Object(c["a"])(r,l,i,!1,null,"52ef4b22",null),d=u.exports,m=a("4e40"),p=a("f7b5"),v=a("9b24"),b={components:{Progress:d,StatusSpot:m["a"]},data:function(){return{isShow:!1,loading:!1,id:"",monitorData:{id:"",offlineJudge:"",alarmFlagOnline:"",noticeDealOnline:"",noticeFlagOnline:"",noticeFlagCpu:"",noticeFlagMemory:"",noticeFlagDisk:"",alarmFlagSystem:"",periodDealSystem:"",timeDealSystem:"",noticeFlagSystem:"",alarmFlagSecurity:"",periodDealSecurity:"",timeDealSecurity:"",noticeFlagSecurity:"",authNearlyJudge:"",alarmFlagAuth:"",alarmFlagAuthNearly:"",noticeFlagAuth:"",noticeFlagAuthNearly:"",warnThresholdSecurity:0,warnThresholdSystem:0},deviceOfflineList:[{label:"无应答立即认定为离线",value:0},{label:"1分钟无应答认定为离线",value:1},{label:"5分钟无应答认定为离线",value:5},{label:"10分钟无应答认定为离线",value:10},{label:"1小时无应答认定为离线",value:60}],oneStateList:[{label:"在线",color:"green"},{label:"离线   ",color:"red"}],fourStateList:[{label:"已授权",color:"green"},{label:"未授权(包括从未授权或授权已过期)",color:"red"}]}},mounted:function(){this.getMonitors()},methods:{getMonitors:function(){var t=this;this.isShow=!1,Object(v["i"])().then((function(e){e&&e.length?(t.id=e[0].id,Object.keys(t.monitorData).forEach((function(a){t.monitorData[a]=e[0][a]})),t.isShow=!0):t.isShow=!1})).catch((function(e){t.isShow=!1}))},updateMonitors:function(){var t=this;this.loading=!0,Object(v["k"])(this.monitorData).then((function(e){200==e.code?Object(p["a"])({i18nCode:"asset.deviceMonitor.saveSuccess",type:"success"}):t.$message({message:e.message,type:"error"}),t.loading=!1})).catch((function(e){t.loading=!1}))},handleSave:function(){this.updateMonitors()},handleReset:function(){var t=this;this.isShow=!1,this.monitorData={id:this.id,offlineJudge:1,periodDealSystem:"",timeDealSystem:"",periodDealSecurity:"",timeDealSecurity:"",authNearlyJudge:15,alarmFlagOnline:0,alarmFlagSystem:0,alarmFlagSecurity:0,alarmFlagAuth:0,alarmFlagAuthNearly:0,noticeFlagAuth:0,noticeDealOnline:1,noticeFlagOnline:0,noticeFlagCpu:0,noticeFlagMemory:0,noticeFlagDisk:0,noticeFlagSystem:0,noticeFlagSecurity:0,noticeFlagAuthNearly:0,warnThresholdSecurity:0,warnThresholdSystem:0},setTimeout((function(){t.isShow=!0}),500)}}},f=b,h=(a("c7f9"),Object(c["a"])(f,o,n,!1,null,"49e58328",null));e["default"]=h.exports},2523:function(t,e,a){"use strict";var o=a("88b5"),n=a.n(o);n.a},"4e40":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"status-spot"},["noShadow"===t.typeStr?a("div",{class:["circle",t.bgColor]}):a("div",{staticClass:"shadow-circle"},[a("div",{class:["shadow-circle-inner",t.bgColor]})])])},n=[],l={name:"StatusSpot",props:{bgColor:{type:String,default:""},typeStr:{type:String,default:""}},data:function(){return{}},methods:{}},i=l,s=(a("2523"),a("2877")),r=Object(s["a"])(i,o,n,!1,null,"4c2db8ca",null);e["a"]=r.exports},"88b5":function(t,e,a){},"96ba":function(t,e,a){"use strict";var o=a("1894"),n=a.n(o);n.a},"9b24":function(t,e,a){"use strict";a.d(e,"f",(function(){return l})),a.d(e,"b",(function(){return i})),a.d(e,"j",(function(){return s})),a.d(e,"a",(function(){return r})),a.d(e,"g",(function(){return c})),a.d(e,"h",(function(){return u})),a.d(e,"e",(function(){return d})),a.d(e,"i",(function(){return m})),a.d(e,"k",(function(){return p})),a.d(e,"c",(function(){return v})),a.d(e,"d",(function(){return b}));var o=a("4020"),n=a("02c6");function l(t){return Object(o["a"])({url:"/assetmonitor/state",method:"get",params:t||{}})}function i(t){return Object(o["a"])({url:"/assetmanagement/assetcare",method:"put",data:t||{}})}function s(t){return Object(o["a"])({url:"/domainmanagement/sortdomains",method:"put",data:t||{}})}function r(t){return Object(o["a"])({url:"/assetmanagement/assetsort",method:"put",data:t||{}})}function c(t){return Object(o["a"])({url:"/assetmonitor/usage",method:"get",params:t||{}})}function u(t){return Object(o["a"])({url:"/assetmonitor/flow",method:"get",params:t||{}})}function d(t){return Object(o["a"])({url:"/domainmanagement/domains",method:"get",params:t||{}})}function m(){return Object(o["a"])({url:"/assetmonitor/monitors",method:"get"})}function p(t){return Object(n["a"])({url:"/assetmonitor/monitors",method:"put",data:t||{}})}function v(t){return Object(o["a"])({url:"/assetmanagement/getDevieContrast",method:"get",params:t||{}})}function b(t){return Object(o["a"])({url:"/assetmanagement/getNetPortState",method:"get",params:t||{}})}},c7f9:function(t,e,a){"use strict";var o=a("f7c0"),n=a.n(o);n.a},f7c0:function(t,e,a){}}]);