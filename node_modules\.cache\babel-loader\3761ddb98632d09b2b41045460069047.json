{"remainingRequest": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue", "mtime": 1750388182601}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}