(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e61720e2"],{"078a":function(e,t,n){"use strict";var r=n("2b0e"),a=(n("99af"),n("caad"),n("ac1f"),n("2532"),n("5319"),{bind:function(e,t,n){var r=[e.querySelector(".el-dialog__header"),e.querySelector(".el-dialog")],a=r[0],s=r[1];a.style.cssText+=";cursor:move;",s.style.cssText+=";top:0px;";var i=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();a.onmousedown=function(e){var t=[e.clientX-a.offsetLeft,e.clientY-a.offsetTop,s.offsetWidth,s.offsetHeight,document.body.clientWidth,document.body.clientHeight],r=t[0],o=t[1],u=t[2],c=t[3],l=t[4],d=t[5],m=[s.offsetLeft,l-s.offsetLeft-u,s.offsetTop,d-s.offsetTop-c],h=m[0],f=m[1],p=m[2],g=m[3],w=[i(s,"left"),i(s,"top")],v=w[0],b=w[1];v.includes("%")?(v=+document.body.clientWidth*(+v.replace(/%/g,"")/100),b=+document.body.clientHeight*(+b.replace(/%/g,"")/100)):(v=+v.replace(/px/g,""),b=+b.replace(/px/g,"")),document.onmousemove=function(e){var t=e.clientX-r,a=e.clientY-o;-t>h?t=-h:t>f&&(t=f),-a>p?a=-p:a>g&&(a=g),s.style.cssText+=";left:".concat(t+v,"px;top:").concat(a+b,"px;"),n.child.$emit("dragDialog")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),s=function(e){e.directive("el-dialog-drag",a)};window.Vue&&(window["el-dialog-drag"]=a,r["default"].use(s)),a.elDialogDrag=s;t["a"]=a},"11a6":function(e,t,n){},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"13cb":function(e,t,n){"use strict";var r=n("8a7f"),a=n.n(r);a.a},"15da":function(e,t,n){},"1f7e":function(e,t,n){"use strict";var r=n("11a6"),a=n.n(r);a.a},2532:function(e,t,n){"use strict";var r=n("23e7"),a=n("5a34"),s=n("1d80"),i=n("ab13");r({target:"String",proto:!0,forced:!i("includes")},{includes:function(e){return!!~String(s(this)).indexOf(a(e),arguments.length>1?arguments[1]:void 0)}})},"2af2":function(e,t,n){"use strict";var r=n("15da"),a=n.n(r);a.a},"45fa":function(e,t,n){e.exports=n.p+"static/media/red-warning.87b62ebd.mp3"},"48b5":function(e,t,n){},"5a34":function(e,t,n){var r=n("44e7");e.exports=function(e){if(r(e))throw TypeError("The method doesn't accept regular expressions");return e}},"841c":function(e,t,n){"use strict";var r=n("d784"),a=n("825a"),s=n("1d80"),i=n("129f"),o=n("14c3");r("search",1,(function(e,t,n){return[function(t){var n=s(this),r=void 0==t?void 0:t[e];return void 0!==r?r.call(t,n):new RegExp(t)[e](String(n))},function(e){var r=n(t,e,this);if(r.done)return r.value;var s=a(e),u=String(this),c=s.lastIndex;i(c,0)||(s.lastIndex=0);var l=o(s,u);return i(s.lastIndex,c)||(s.lastIndex=c),null===l?-1:l.index}]}))},"8a7f":function(e,t,n){},a434:function(e,t,n){"use strict";var r=n("23e7"),a=n("23cb"),s=n("a691"),i=n("50c4"),o=n("7b0b"),u=n("65f0"),c=n("8418"),l=n("1dde"),d=n("ae40"),m=l("splice"),h=d("splice",{ACCESSORS:!0,0:0,1:2}),f=Math.max,p=Math.min,g=9007199254740991,w="Maximum allowed length exceeded";r({target:"Array",proto:!0,forced:!m||!h},{splice:function(e,t){var n,r,l,d,m,h,v=o(this),b=i(v.length),y=a(e,b),$=arguments.length;if(0===$?n=r=0:1===$?(n=0,r=b-y):(n=$-2,r=p(f(s(t),0),b-y)),b+n-r>g)throw TypeError(w);for(l=u(v,r),d=0;d<r;d++)m=y+d,m in v&&c(l,d,v[m]);if(l.length=r,n<r){for(d=y;d<b-r;d++)m=d+r,h=d+n,m in v?v[h]=v[m]:delete v[h];for(d=b;d>b-r+n;d--)delete v[d-1]}else if(n>r)for(d=b-r;d>y;d--)m=d+r-1,h=d+n-1,m in v?v[h]=v[m]:delete v[h];for(d=0;d<n;d++)v[d+y]=arguments[d+2];return v.length=b-r+n,l}})},ab13:function(e,t,n){var r=n("b622"),a=r("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[a]=!1,"/./"[e](t)}catch(r){}}return!1}},c54a:function(e,t,n){"use strict";n.d(t,"l",(function(){return r})),n.d(t,"m",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"j",(function(){return u})),n.d(t,"q",(function(){return c})),n.d(t,"d",(function(){return l})),n.d(t,"f",(function(){return d})),n.d(t,"g",(function(){return m})),n.d(t,"e",(function(){return h})),n.d(t,"n",(function(){return f})),n.d(t,"k",(function(){return p})),n.d(t,"p",(function(){return g})),n.d(t,"h",(function(){return w})),n.d(t,"i",(function(){return v})),n.d(t,"o",(function(){return b}));n("ac1f"),n("466d"),n("1276");function r(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="";switch(t){case 0:n=/^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/;break;case 1:n=/^(?![_.\-])(?!.*?[_.\-]$)[a-zA-Z0-9_.\-\u4e00-\u9fa5]+$/;break;case 2:n=/^(?![_./\-])(?!.*?[_./\-]$)[a-zA-Z0-9_./\-\u4e00-\u9fa5]+$/;break;case 3:n=/^(?![_./\-\s])(?!.*?[_./\-\s]$)[a-zA-Z0-9_./\-\s\u4e00-\u9fa5]+$/;break;default:n=/^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/;break}return n.test(e)}function a(e){var t=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\[\].<>/?\-%]).{0,}$/;return t.test(e)}function s(e){var t=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return t.test(e)}function i(e){var t=/^([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;return t.test(e)}function o(e){var t=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return t.test(e)}function u(e){for(var t=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,n=e.split(","),r=0;r<n.length;r++)if(!t.test(n[r]))return!1;return!0}function c(e){var t=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return t.test(e)}function l(e){var t=/^(\d{2,5}-)?\d{6,9}(-\d{2,4})?$/;return t.test(e)}function d(e){var t=/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;return t.test(e)}function m(e){var t=/:/.test(e)&&e.match(/:/g).length<8&&/::/.test(e)?1===e.match(/::/g).length&&/^::$|^(::)?([\da-f]{1,4}(:|::))*[\da-f]{1,4}(:|::)?$/i.test(e):/^([\da-f]{1,4}:){7}[\da-f]{1,4}$/i.test(e);return t}function h(e){return d(e)||m(e)}function f(e){var t=/^([0-9]|[1-9][0-9]{0,4})$/;return t.test(e)}function p(e){for(var t=/^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,n=e.split(","),r=0;r<n.length;r++)if(!t.test(n[r]))return!1;return!0}function g(e){var t=/^[^ ]+$/;return t.test(e)}function w(e){var t=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\.[A-Fa-f0-9]{4}){2}$/;return t.test(e)}function v(e){var t=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return t.test(e)}function b(e){var t=/[^\u4E00-\u9FA5]/;return t.test(e)}},c5e7:function(e,t,n){e.exports=n.p+"static/img/simple.fcbdd64f.png"},caad:function(e,t,n){"use strict";var r=n("23e7"),a=n("4d64").includes,s=n("44d2"),i=n("ae40"),o=i("indexOf",{ACCESSORS:!0,1:0});r({target:"Array",proto:!0,forced:!o},{includes:function(e){return a(this,e,arguments.length>1?arguments[1]:void 0)}}),s("includes")},d415:function(e,t,n){"use strict";var r=n("48b5"),a=n.n(r);a.a},f826:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("layout-wrapper")},a=[],s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-wrapper"},[""!==e.data.username?r("top-nav",{attrs:{username:e.data.username,"system-name":e.data.systemName,"alarm-amount":e.data.alarms,"system-alarm-amount":e.data.systemAlarms,"on-site-notice-amount":e.data.onSiteNoticeAmount},on:{"on-search":e.asyncMenuPath,"shrink-top-nav":e.shrinkTopNavMenuHeight,"stretch-top-nav":e.stretchTopNavMenuHeight}}):e._e(),[r("audio",{ref:"sysAlarmRef",attrs:{muted:e.audioMuted,src:n("45fa")}})],e.data.menu.length>0?r("main",{staticClass:"main-container"},[r("side-menu",{staticClass:"menu-container",style:{width:e.width.menu},attrs:{height:e.height,"menu-data":e.data.menu,"default-menu":e.data.activeMenuID,width:e.width.menu},on:{"get-action":e.setRouteAuthAction,"scale-menu":e.menuWidthChangeRelayout}}),r("section",{ref:"renderRouter",staticClass:"router-container",style:{width:e.renderRouterWidth,"margin-left":e.width.menu+10+"px"}},[r("section",{staticClass:"router-wrapper"},[r("transition",{attrs:{name:"slide-fade",mode:"out-in",appear:""}},[e.isUpdateView?r("router-view",{staticClass:"app-main-body"}):e._e()],1)],1)])],1):e._e()],2)},i=[],o=(n("99af"),n("c975"),n("ac1f"),n("5319"),n("96cf"),n("c964")),u=n("a18c"),c=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("header",{ref:"header",staticClass:"header-nav"},[r("section",{staticClass:"header-title"},[r("img",{staticClass:"header-title-img",attrs:{src:n("c5e7")}}),r("span",[e._v(e._s(e.systemName||"工控哨兵管理平台"))])]),r("section",{staticClass:"header-shortcut"},[e.alarmAmount&&e.alarmAmount>0||e.systemAlarmAmount&&e.systemAlarmAmount>0||e.onSiteNoticeAmount&&e.onSiteNoticeAmount>0?r("section",{staticClass:"header-shortcut-container",on:{mouseenter:function(t){e.animate.alarm=!0},mouseleave:function(t){e.animate.alarm=!1}}},[r("i",{staticClass:"icon-font-size soc-icon-alarm"}),r("transition",{attrs:{name:"el-zoom-in-top",mode:"out-in"}},[r("ul",{directives:[{name:"show",rawName:"v-show",value:e.animate.alarm,expression:"animate.alarm"}],staticClass:"bubble"},[e.permission.includes("sysAlarm")?r("li",{on:{click:function(t){return e.clickJumpAlarmTab("sysAlarm")}}},[r("el-badge",{attrs:{value:e.systemAlarmAmount,hidden:e.systemAlarmAmount<=0}},[e._v("系统告警")])],1):e._e(),e.permission.includes("notice")?r("li",{on:{click:function(t){return e.clickJumpAlarmTab("onSiteNotice")}}},[r("el-badge",{attrs:{value:e.onSiteNoticeAmount,hidden:e.onSiteNoticeAmount<=0}},[e._v("站内通知")])],1):e._e()])])],1):e._e(),r("i",{staticClass:"icon-font-size soc-icon-username"}),r("section",{staticClass:"header-shortcut-container",on:{mouseenter:function(t){e.animate.setting=!0},mouseleave:function(t){e.animate.setting=!1}}},[r("a",{staticClass:"header-shortcut-username"},[e._v(e._s(e.username))]),r("transition",{attrs:{name:"el-zoom-in-top",mode:"out-in"}},[r("ul",{directives:[{name:"show",rawName:"v-show",value:e.animate.setting,expression:"animate.setting"}],staticClass:"bubble"},e._l(e.data.setting,(function(t,n){return r("li",{directives:[{name:"show",rawName:"v-show",value:"大屏展示"!==t.label,expression:"val.label !== '大屏展示'"}],key:n,on:{click:function(n){return e.clickSettingTab(t)}}},[r("i",{class:[t["icon"],{"header-config-important":t.important}]}),r("span",{class:{"header-config-important":t.important}},[e._v(e._s(t.label))])])})),0)])],1)]),r("section",{staticClass:"shortcut-component"},[r("modify-user",{attrs:{visible:e.visible.userInfo},on:{"on-display":e.displayUserInfoDialog}}),r("modify-password",{attrs:{visible:e.visible.password},on:{"on-display":e.displayPasswordDialog}})],1)])},l=[],d=(n("4160"),n("a630"),n("a434"),n("a9e3"),n("3ca3"),n("841c"),n("159b"),n("a78e")),m=n.n(d),h=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("custom-dialog",{ref:"userDialog",attrs:{visible:e.visible,title:e.$t("layout.setting.user.label"),width:"60%"},on:{"on-close":e.close,"on-submit":e.submit}},[n("el-form",{ref:"userForm",attrs:{model:e.userForm.model,rules:e.userForm.rules,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.user.username"),prop:"username"}},[n("el-input",{staticClass:"width-small",model:{value:e.userForm.model.username,callback:function(t){e.$set(e.userForm.model,"username",t)},expression:"userForm.model.username"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.user.nickname"),prop:"nickname"}},[n("el-input",{staticClass:"width-small",attrs:{maxlength:"64"},model:{value:e.userForm.model.nickname,callback:function(t){e.$set(e.userForm.model,"nickname",t)},expression:"userForm.model.nickname"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.user.email"),prop:"email"}},[n("el-input",{staticClass:"width-small",attrs:{maxlength:"32"},model:{value:e.userForm.model.email,callback:function(t){e.$set(e.userForm.model,"email",t)},expression:"userForm.model.email"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.user.mobilephone"),prop:"mobilephone"}},[n("el-input",{staticClass:"width-small",model:{value:e.userForm.model.mobilephone,callback:function(t){e.$set(e.userForm.model,"mobilephone",t)},expression:"userForm.model.mobilephone"}})],1)],1)],1),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.user.telephone"),prop:"telephone"}},[n("el-input",{staticClass:"width-small",model:{value:e.userForm.model.telephone,callback:function(t){e.$set(e.userForm.model,"telephone",t)},expression:"userForm.model.telephone"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.user.menu"),prop:"menu"}},[n("el-cascader",{staticClass:"width-small",attrs:{"expand-trigger":"hover",options:e.data.menu,props:e.data.menuRule,clearable:""},model:{value:e.userForm.model.menu,callback:function(t){e.$set(e.userForm.model,"menu",t)},expression:"userForm.model.menu"}})],1)],1)],1)],1)],1)},f=[],p=(n("498a"),n("d465")),g=n("f7b5"),w=n("c54a"),v=n("cd0e"),b={components:{CustomDialog:p["a"]},props:{visible:{type:Boolean,default:!1}},data:function(){var e=this,t=function(t,n,r){""===n.trim()||Object(w["c"])(n)?r():r(new Error(e.$t("validate.comm.email")))},n=function(t,n,r){""===n.trim()||Object(w["a"])(n)?r():r(new Error(e.$t("validate.comm.cellphone")))},r=function(t,n,r){""===n.trim()||Object(w["q"])(n)?r():r(new Error(e.$t("validate.comm.telephone")))};return{userForm:{model:{username:"",nickname:"",email:"",mobilephone:"",telephone:"",menu:[]},rules:{username:[{required:!0,message:this.$t("validate.empty"),trigger:"blur"}],email:[{validator:t,trigger:"blur"}],mobilephone:[{validator:n,trigger:"blur"}],telephone:[{validator:r,trigger:"blur"}]}},data:{menuRule:{value:"menuId",label:"menuName",children:"children"},menu:[]}}},mounted:function(){this.preloadData()},methods:{preloadData:function(){this.getMenu(),this.getUserInfo()},close:function(){this.$emit("on-display"),this.$refs["userForm"].resetFields()},submit:function(){var e=this;this.$refs["userForm"].validate((function(t){if(t){var n=e.userForm.model.menu;if("string"!==typeof n){var r=n.length;n=r>0?e.userForm.model.menu[r-1]:""}e.updateUserInfo({userFullName:e.userForm.model.username,userSortName:e.userForm.model.nickname,userMail:e.userForm.model.email,userPhone:e.userForm.model.telephone,userMobile:e.userForm.model.mobilephone,defaultMenu:n}),e.close()}else Object(g["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1}))})),this.$refs.userDialog.end()},getMenu:function(){var e=this;Object(v["d"])().then((function(t){e.data.menu=t}))},getUserInfo:function(){var e=this;Object(v["l"])().then((function(t){e.userForm.model.username=t.userFullName?t.userFullName:"",e.userForm.model.nickname=t.userSortName?t.userSortName:"",e.userForm.model.email=t.userMail?t.userMail:"",e.userForm.model.mobilephone=t.userMobile?t.userMobile:"",e.userForm.model.telephone=t.userPhone?t.userPhone:"",e.userForm.model.menu=t.defaultMenu?t.defaultMenu:""}))},updateUserInfo:function(e){var t=this;Object(v["m"])(e).then((function(e){e?1==e?Object(g["a"])({i18nCode:"tip.update.success",type:"success"},(function(){t.preloadData()})):6==e&&Object(g["a"])({i18nCode:"tip.update.emailTip",type:"error"},(function(){})):Object(g["a"])({i18nCode:"tip.update.error",type:"error"})}))}}},y=b,$=n("2877"),k=Object($["a"])(y,h,f,!1,null,null,null),A=k.exports,x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("custom-dialog",{ref:"passwordDialog",attrs:{visible:e.visible,title:e.$t("layout.setting.password.label"),width:"40%"},on:{"on-close":e.close,"on-submit":e.submit}},[n("el-form",{ref:"passwordForm",staticClass:"password-form",attrs:{model:e.password.form,rules:e.password.rules,"label-width":"25%"}},[n("el-form-item",{attrs:{label:e.$t("layout.setting.password.old"),prop:"old"}},[n("el-input",{staticClass:"width-mini",attrs:{"show-password":""},nativeOn:{"!paste":function(t){return t.preventDefault(),e.disablePaste()}},model:{value:e.password.form.old,callback:function(t){e.$set(e.password.form,"old",t)},expression:"password.form.old"}}),n("aside",{staticClass:"soc-form-error-text",attrs:{slot:"error"},slot:"error"},[e._v(" "+e._s(e.password.error.old)+" ")])],1),n("el-form-item",{attrs:{label:e.$t("layout.setting.password.new"),prop:"new"}},[n("el-input",{staticClass:"width-mini",attrs:{"show-password":""},nativeOn:{"!paste":function(t){return t.preventDefault(),e.disablePaste()}},model:{value:e.password.form.new,callback:function(t){e.$set(e.password.form,"new",t)},expression:"password.form.new"}}),n("aside",{staticClass:"soc-form-error-text",attrs:{slot:"error"},slot:"error"},[e._v(" "+e._s(e.password.error.new)+" ")])],1),n("el-form-item",{attrs:{label:e.$t("layout.setting.password.confirm"),prop:"confirm"}},[n("el-input",{staticClass:"width-mini",attrs:{"show-password":""},nativeOn:{"!paste":function(t){return t.preventDefault(),e.disablePaste()}},model:{value:e.password.form.confirm,callback:function(t){e.$set(e.password.form,"confirm",t)},expression:"password.form.confirm"}}),n("aside",{staticClass:"soc-form-error-text",attrs:{slot:"error"},slot:"error"},[e._v(" "+e._s(e.password.error.confirm)+" ")])],1)],1)],1)},S=[],_=n("720d"),M={components:{CustomDialog:p["a"]},props:{visible:{type:Boolean,default:!1}},data:function(){var e=this,t=function(t,n,r){""===n?r(e.password.error.old=e.$t("validate.password.old.empty")):r()},n=function(t,n,r){""===n?r(e.password.error.new=e.$t("validate.password.new.empty")):n===e.password.form.old?r(e.password.error.new=e.$t("validate.password.new.compare")):n.length<8||n.length>20?r(e.password.error.new=e.$t("validate.password.size")):Object(w["m"])(n)?r():r(e.password.error.new=e.$t("validate.password.rule"))},r=function(t,n,r){""===n?r(e.password.error.confirm=e.$t("validate.password.confirm.empty")):n!==e.password.form.new?r(e.password.error.confirm=e.$t("validate.password.confirm.compare")):r()};return{password:{form:{new:"",old:"",confirm:""},rules:{new:[{required:!0,validator:n,trigger:"blur"}],old:[{required:!0,validator:t,trigger:"blur"}],confirm:[{required:!0,validator:r,trigger:"blur"}]},error:{new:"",old:"",confirm:""}}}},methods:{close:function(){this.$refs.passwordForm.resetFields(),this.$emit("on-display")},submit:function(){var e=this;this.$refs.passwordForm.validate((function(t){if(t){var n=e.handlePasswordParam();e.updateUserPassword(n),e.close()}else Object(g["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return!1}))})),this.$refs.passwordDialog.end()},handlePasswordParam:function(){var e=new _["JSEncrypt"];return e.setPublicKey(this.$store.getters.publicKey),{userId:this.$store.getters.userID,newPassword:e.encrypt(this.password.form.new),oldPassword:e.encrypt(this.password.form.old),confirmPassword:e.encrypt(this.password.form.confirm)}},disablePaste:function(){return null},updateUserPassword:function(e){var t=this;Object(v["n"])(e).then((function(e){1===e?(Object(g["a"])({i18nCode:"tip.update.password",type:"success"}),t.$router.replace({path:"/login"})):Object(g["a"])({i18nCode:"tip.update.error",type:"error"})}))}}},C=M,N=Object($["a"])(C,x,S,!1,null,null,null),F=N.exports,O={components:{ModifyUser:A,ModifyPassword:F},props:{username:{type:String,default:"admin"},systemName:{type:String,default:""},alarmAmount:{type:[Number,Boolean],default:0},systemAlarmAmount:{type:[Number,Boolean],default:0},onSiteNoticeAmount:{type:[Number,Boolean],default:0}},data:function(){return{animate:{setting:!1,alarm:!1},visible:{userInfo:!1,password:!1,theme:!1},option:{type:[]},search:{type:"",fuzzyField:""},data:{setting:[],tools:[]},permission:[]}},mounted:function(){this.preloadData()},methods:{preloadData:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getSystemTools();case 2:e.getQuickSearch(),e.setSettingTools(),e.handleDisplayTools(),e.getNoticePermissions();case 6:case"end":return t.stop()}}),t)})))()},getNoticePermissions:function(){var e=this;Object(v["f"])().then((function(t){e.permission=t}))},setSettingTools:function(){this.data.setting=[{label:this.$t("layout.setting.user.label"),icon:"soc-icon-username",important:!1,fn:this.clickTabUserInfo},{label:this.$t("layout.setting.password.label"),icon:"soc-icon-password",important:!1,fn:this.clickTabUpdatePassword},{label:this.$t("layout.setting.logout"),icon:"soc-icon-submit",important:!0,fn:this.logout}]},handleDisplayTools:function(){var e=this,t=this;this.data.tools.forEach((function(n){var r=e.data.setting.length-1,a={label:n.menuName,icon:n.menuIcon,important:!1,fn:function(){t.openWindowDisplay(n.menuLocation)}};e.data.setting.splice(r,0,a)}))},clickFastSearch:function(){this.$router.push({path:this.search.type,query:{fuzzyField:this.search.fuzzyField}}),this.$emit("on-search",this.search.type)},clickJumpAlarmTab:function(e){"alarm"===e?this.$router.push({path:"/alarm/table"}):"sysAlarm"===e?this.$router.push({path:"/alarm/system"}):"onSiteNotice"===e&&this.$router.push({path:"/alarm/notice"})},clickSettingTab:function(e){this.animate.setting=!1,e.fn&&e.fn()},displayUserInfoDialog:function(e){this.visible.userInfo=e},displayPasswordDialog:function(e){this.visible.password=e},clickShrinkHeaderNav:function(){this.$refs.header.css({visibility:"hidden"}).animate({height:"0"},400),this.$refs.shrinkButton.hide(),this.$refs.stretchButton.css({visibility:"initial"}).show(),this.$emit("shrink-top-nav")},clickStretchHeaderNav:function(){this.$refs.header.css({visibility:""}).animate({height:"50"},400),this.$refs.shrinkButton.show(),this.$refs.stretchButton.hide(),this.$emit("stretch-top-nav")},clickTabUserInfo:function(){this.visible.userInfo=!0},clickTabUpdatePassword:function(){this.visible.password=!0},clickTabSwitchTheme:function(){var e="light"===this.$store.getters.theme?"dark":"light";this.$store.dispatch("system/switchTheme",e)},logout:function(){var e=function(e){Array.from(document.querySelectorAll(e)).forEach((function(e){e.remove()}))};this.$store.dispatch("user/logout").then((function(){e(".el-message"),e(".el-notification")}))},openWindowDisplay:function(e){var t=this.$router.resolve({path:e});this.$store.dispatch("user/updatePath",e),m.a.set("store",JSON.stringify(this.$store.state)),window.open(t.href,"_blank")},getSystemTools:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(v["k"])().then((function(t){e.data.tools=t}));case 2:case"end":return t.stop()}}),t)})))()},getQuickSearch:function(){var e=this;Object(v["g"])().then((function(t){e.option.type=t,e.search.type=t[0]&&t[0]["url"]?t[0]["url"]:""}))}}},D=O,j=(n("2af2"),Object($["a"])(D,c,l,!1,null,"f02f7a5c",null)),I=j.exports,T=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("menu",{ref:"menuContainer",style:{width:e.width+"px"}},[n("el-menu",{attrs:{collapse:e.isCollapse,"unique-opened":!1,"default-active":e.defaultMenu,"collapse-transition":!1}},[n("sub-menu",{attrs:{"menu-data":e.menuData},on:{"on-menu":e.routerLink}})],1),n("footer",{ref:"menuFooter"},[n("i",{ref:"shrinkButton",staticClass:"iconfont icon-shousuo",on:{click:function(t){return e.clickShrinkMenu(t)}}}),n("i",{ref:"stretchButton",staticClass:"iconfont icon-zhankai",on:{click:function(t){return e.clickStretchMenu(t)}}})])],1)},P=[],R=function(){var e=this,t=e.$createElement,n=e._self._c||t;return Object.keys(e.menuData).length>0?n("fragment",[e._l(e.menuData,(function(t){return[null===t.children||0===t.children.length?n("el-menu-item",{key:t.menuId,attrs:{index:t.menuId},on:{click:function(n){return e.clickMenu(t)}}},[t.menuIcon?n("i",{class:t.menuIcon}):e._e(),n("span",{attrs:{slot:"title"},slot:"title"},[e._v(e._s(t.menuName))])]):n("el-submenu",{key:t.menuId,attrs:{index:t.menuId}},[n("template",{slot:"title"},[t.menuIcon?n("i",{class:t.menuIcon}):e._e(),n("span",{attrs:{slot:"title"},slot:"title"},[e._v(e._s(t.menuName))])]),n("sub-menu",{attrs:{"menu-data":t.children},on:{"on-menu":e.clickMenu}})],2)]}))],2):e._e()},z=[],L={name:"SubMenu",props:{menuData:{required:!0,type:Array}},methods:{clickMenu:function(e){this.$emit("on-menu",e)}}},W=L,q=(n("1f7e"),Object($["a"])(W,R,z,!1,null,"8b5d3c88",null)),E=q.exports,U={components:{SubMenu:E},props:{menuData:{required:!0,type:Array,default:function(){return[]}},defaultMenu:{required:!1,type:String||Number,default:""},width:{required:!1,type:Number,default:240},height:{required:!1,type:String||Number,default:"calc(100% - 70px)"}},data:function(){return{isCollapse:!1,location:"",shrinkWidth:74,stretchWidth:180,menuWidth:0}},watch:{menuWidth:function(e){this.$refs.menuContainer.css({width:"".concat(e,"px"),transition:"0.4s all"})},height:function(){this.$refs.menuContainer.css({height:this.height})}},mounted:function(){this.setStyle()},methods:{setStyle:function(){this.$refs.menuContainer.css({height:this.height})},routerLink:function(e){if("/visualization/situation"===e.menuLocation)return window.open(e.menuLocation),void this.$emit("get-action",e);this.$router.push({path:e.menuLocation}),this.$emit("get-action",e)},clickShrinkMenu:function(e){this.isCollapse=!0,e.target.hide(),this.$refs.stretchButton.show(),this.menuWidth=this.shrinkWidth,this.$emit("scale-menu",this.menuWidth)},clickStretchMenu:function(e){this.isCollapse=!1,e.target.hide(),this.$refs.shrinkButton.show(),this.menuWidth=this.stretchWidth,this.$emit("scale-menu",this.menuWidth)}}},B=U,Z=(n("13cb"),Object($["a"])(B,T,P,!1,null,"2a26d99a",null)),H=Z.exports,V=n("921c"),J={name:"LayoutWrapper",components:{TopNav:I,SideMenu:H},provide:function(){return{updateView:this.updateView,alarm:this.getAlarmAmount,sysAlarm:this.getSystemAlarmAmount,onSiteAlarm:this.getNotReadNoticeAmount}},data:function(){return{height:"",width:{window:window.innerWidth,menu:180},data:{license:-1,alarms:0,systemAlarms:0,onSiteNoticeAmount:0,isSound:0,menu:[],defaultMenuID:"",activeMenuID:"",defaultMenuPath:"",username:"",systemName:this.$store.getters.systemName,routeQuery:{}},audioMuted:!1,isUpdateView:!0}},computed:{renderRouterWidth:function(){var e=this.width.window-this.width.menu-20;return"".concat(e,"px")}},watch:{$route:{handler:function(e){var t=Object(V["c"])(e.path,"menuLocation",this.data.menu,!0,"menuId");this.loadBreadcrumb(e.path),t&&(this.data.activeMenuID=t[t.length-1])},deep:!0,immediate:!0},"data.license":function(e){-1e4!==e&&e<=7&&this.$notify({title:this.$t("tip.sweet"),type:"warning",duration:0,message:this.$t("layout.license",[e]),position:"bottom-right"})},"data.systemAlarms":function(e){var t=this;if(e>0&&"1"===this.data.isSound){var n=this.$notify({title:this.$t("tip.notice"),duration:1e4,dangerouslyUseHTMLString:!0,message:"<span style='cursor: pointer'>"+this.$t("layout.systemAlarm",[e])+"</span>",position:"bottom-right"});n.$el.querySelector("span").onclick=function(){t.$router.push({path:"/alarm/system"})}}},"data.onSiteNoticeAmount":function(e){var t=this;if(e>0&&"1"===this.data.isSound){var n=this.$notify({title:this.$t("tip.notice"),duration:1e4,dangerouslyUseHTMLString:!0,message:"<span style='cursor: pointer'>"+this.$t("layout.onSiteNotice",[e])+"</span>",position:"bottom-right"});n.$el.querySelector("span").onclick=function(){t.$router.push({path:"/alarm/notice"})}}}},created:function(){this.initWebsocket()},mounted:function(){document.querySelector("title").innerText="工控哨兵管理平台",this.preloadData(),this.resize()},beforeDestroy:function(){this.$store.dispatch("websocket/close")},methods:{preloadData:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getSystem();case 2:return t.next=4,e.getMenu();case 4:return t.next=6,e.getAlarmAmount();case 6:return t.next=8,e.getSystemNoticeConfig();case 8:return t.next=10,e.getSystemAlarmAmount();case 10:return t.next=12,e.getNotReadNoticeAmount();case 12:e.getLicenseRemainDay();case 13:case"end":return t.stop()}}),t)})))()},asyncMenuPath:function(e){var t=Object(V["c"])(e,"menuLocation",this.data.menu,!0,"menuId");this.data.activeMenuID=t[t.length-1],this.loadBreadcrumb(e)},shrinkTopNavMenuHeight:function(){this.height="100%"},stretchTopNavMenuHeight:function(){this.height="calc(100% - 70px)"},menuWidthChangeRelayout:function(e){this.width.menu=e},loadBreadcrumb:function(e){var t=Object(V["c"])(e,"menuLocation",this.data.menu,!0,"actions");t&&this.$store.dispatch("user/authAction",t[t.length-1])},setMenuDefaultValue:function(e){var t=this,n=function(){var n=Object(V["a"])(e),r=n[n.length-1];t.data.defaultMenuID=r["menuId"],t.data.defaultMenuPath=r["menuLocation"],t.$store.dispatch("user/authAction",r["actions"])};if(""===this.data.defaultMenuID||null===this.data.defaultMenuID)n();else{var r=Object(V["c"])(this.data.defaultMenuID,"menuId",e);r?(this.data.defaultMenuPath=r[r.length-1]["menuLocation"],this.$store.dispatch("user/authAction",r[r.length-1]["actions"])):n()}},setStoreHomePath:function(e){if(this.data.routeQuery=this.$route.query,"online"===this.$store.getters.mode){var t=Object(V["c"])(this.$route.path,"menuLocation",e);""!==this.$route.path&&t?(this.loadBreadcrumb(this.$route.path),this.data.activeMenuID=t[t.length-1]["menuId"],this.$router.replace({path:t[t.length-1]["menuLocation"],query:this.data.routeQuery})):(this.$router.replace({path:this.data.defaultMenuPath}),this.loadBreadcrumb(this.data.defaultMenuPath),this.data.activeMenuID=this.data.defaultMenuID)}},setRouteAuthAction:function(e){JSON.stringify(u["c"]).indexOf(e.menuLocation)>-1&&(this.$store.dispatch("user/updatePath",e.menuLocation),this.$store.dispatch("user/authAction",e.actions))},updateView:function(){var e=this;this.isUpdateView=!1,this.$nextTick((function(){e.isUpdateView=!0}))},resize:function(){var e=this;window.addEventListener("resize",(function(){e.width.window=window.innerWidth}))},initWebsocket:function(){var e="",t="";if(""===e){var n="http"===document.location.protocol?"ws":"wss";t="".concat(n,"://").concat(document.location.host,"/websocket")}else t=e.replace("https","wss").replace("http","ws")+"/websocket";this.$store.dispatch("websocket/init",{url:t,token:this.$store.getters.token})},getMenu:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(v["d"])().then((function(t){e.data.menu=t,e.setMenuDefaultValue(t),e.setStoreHomePath(t)}));case 2:case"end":return t.stop()}}),t)})))()},getSystem:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(v["i"])().then((function(t){e.data.username=t.account,e.data.defaultMenuID=t.defaultMenu,e.data.systemName=t.systemName,document.querySelector("title").innerText=t.systemName,e.$store.dispatch("user/saveUserID",t.userId)}));case 2:case"end":return t.stop()}}),t)})))()},getLicenseRemainDay:function(){var e=this;Object(v["b"])().then((function(t){e.data.license=t}))},getAlarmAmount:function(){var e=this;Object(v["a"])().then((function(t){e.data.alarms=t}))},getSystemNoticeConfig:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(v["j"])().then((function(t){e.data.isSound=t.isSound}));case 2:case"end":return t.stop()}}),t)})))()},getSystemAlarmAmount:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("1"!==e.data.isSound){t.next=3;break}return t.next=3,Object(v["h"])().then((function(t){e.data.systemAlarms=t,e.data.systemAlarms>0&&e.playaudio()}));case 3:case"end":return t.stop()}}),t)})))()},getNotReadNoticeAmount:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("1"!==e.data.isSound){t.next=3;break}return t.next=3,Object(v["e"])().then((function(t){e.data.onSiteNoticeAmount=t,e.data.onSiteNoticeAmount>0&&e.playaudio()}));case 3:case"end":return t.stop()}}),t)})))()},playaudio:function(){this.audioMuted=!0,this.$refs.sysAlarmRef.play()}}},Q=J,K=(n("d415"),Object($["a"])(Q,s,i,!1,null,"4c91b1e5",null)),X=K.exports,Y={name:"Layout",components:{LayoutWrapper:X}},G=Y,ee=Object($["a"])(G,r,a,!1,null,null,null);t["default"]=ee.exports}}]);