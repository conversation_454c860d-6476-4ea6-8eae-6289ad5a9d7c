import { getAlarmEventListData, deleteSecurityPolicyListData } from '@/services/Log/log';
import {deleteAlarmLogByNotSelected,getSystemDate} from "../../../services/Log/alarmEvent";

export default {
	namespace: 'alarmEvent',

	state: {
		alarmEventData: {},
		deviceTimeLogData: [],
	},

	effects: {
		*getData({ payload, callback }, { call, put }) {
			try {
				const response = yield call(getAlarmEventListData, payload);
				if (!payload.export){
					yield put({
						type: 'saveData',
						alarmEventData: response.data,
					})
				}
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*getSystemDate({payload,callback},{call,put}){
		    try {
				const response = yield call(getSystemDate);
				if (callback) callback(response);
			}catch (err) {
				console.log(err)
			}
		},
		*getDataForCount({payload,callback},{call,put}){
		   try {
			   const response = yield call(getAlarmEventListData,payload);
               if (callback) callback(response);
		   }catch (err) {
			   console.log(err);
		   }
		},
		*getAllData({payload,callback},{call,put}){
			try{
				const response = yield call(getAlarmEventListData , payload);
				yield put({
					type:'saveData',
					alarmEventAllDataKeys:response.data.items.map((item)=>item.id)
				});
				if(callback) callback(response);
			}catch(err){
				console.log(err);
			}
		},
		*getChoiceTimeData({ payload, callback }, { call, put }) {
			try {
				const response = yield call(getAlarmEventListData, payload);
				yield put({
					type: 'saveData',
					deviceTimeLogData: response.data,
					totoList: response.data.total
				})
				if (callback) callback(response);
			} catch (err) {
				console.log(err)
			}
		},
		*DeleteData({ payload, callback }, { call, put }) {
			try {
				const response = yield call(deleteSecurityPolicyListData, payload);
				const jsonData = JSON.parse(response);
				if (callback) callback(jsonData);
			} catch (err) {
				console.log(err);
			}
		},
		*DeleteDataByNotSelected({payload , callback},{call , put}){
		    try {
				const response = yield call(deleteAlarmLogByNotSelected , payload);
				const jsonData = JSON.parse(response);
				if (callback) callback(response);
			}catch (err) {
				console.log(err);
			}
		},
		*clearData({ payload, callback }, { call, put }) {
			yield put({
				type: 'saveData',
				alarmEventData: {},
				deviceTimeLogData: [],
				totoList: ''
			})
		},
	},

	reducers: {
		saveData(state, action) {
			return {
				...state, ...action
			}
		}
	}
}