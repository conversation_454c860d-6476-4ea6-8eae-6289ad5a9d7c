(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ff4da0ac"],{"0aa8":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"soc-http401"},[a("el-row",{staticClass:"soc-http401-container"},[a("el-col",{staticClass:"soc-http401-img",attrs:{span:12}},[a("img",{attrs:{src:s("5d51"),width:"313",height:"428"}})]),a("el-col",{staticClass:"soc-http401-context",attrs:{span:12}},[t.department?a("section",{staticClass:"soc-http401-context-department"},[t._v(" "+t._s(t.$t("exception.page401.department"))+" ")]):t._e(),t.info?a("section",{staticClass:"soc-http401-context-info"},[a("p",[a("span",[t._v(t._s(t.$t("exception.page401.copyright.label")))]),t._v(" : "),a("span",[t._v(t._s(t.$t("exception.page401.copyright.name")))])]),a("p",[a("span",[t._v(t._s(t.$t("exception.page401.author.label")))]),t._v(" : "),a("a",{staticClass:"link-type"},[t._v(t._s(t.$t("exception.page401.author.name")))])])]):t._e(),a("section",{staticClass:"soc-http401-context-headline"},[t._v(" "+t._s(t.$t("exception.page401.instructions"))+" ")]),a("section",{staticClass:"soc-http401-context-info"},[t._v(" "+t._s(t.$t("exception.page401.check"))+" ")]),t.goRecently?a("el-button",{staticClass:"soc-http401-context-backhome",attrs:{plain:""},on:{click:function(e){return t.goRecently()}}},[t._v(" "+t._s(t.$t("exception.page401.home"))+" ")]):t._e()],1)],1)],1)},c=[],n=(s("ac1f"),s("5319"),{name:"Page401",data:function(){return{department:!1,info:!1,backhome:!0}},methods:{goRecently:function(){this.$router.replace({path:this.$store.getters.homePath||""!==this.$store.getters.homePath?this.$store.getters.homePath:"/layout"})}}}),o=n,i=(s("6b11"),s("2877")),p=Object(i["a"])(o,a,c,!1,null,"3c8a70f5",null);e["default"]=p.exports},"321e":function(t,e,s){},"5d51":function(t,e,s){t.exports=s.p+"static/img/401.089007e7.gif"},"6b11":function(t,e,s){"use strict";var a=s("321e"),c=s.n(a);c.a}}]);