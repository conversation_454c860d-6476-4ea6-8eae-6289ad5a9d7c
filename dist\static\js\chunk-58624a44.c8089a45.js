(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58624a44"],{1690:function(e,t,a){"use strict";var n=a("981a"),i=a.n(n);i.a},2911:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.clickTabSwitch},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},[a("el-tab-pane",{attrs:{label:"待办列表",name:"1"}},[1==e.tabName?a("Todo"):e._e()],1),a("el-tab-pane",{attrs:{label:"已办列表",name:"2"}},[2==e.tabName?a("Done"):e._e()],1)],1)],1)},i=[],o=(a("b0c0"),a("a394")),r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("table-header",{attrs:{condition:e.query},on:{"update:condition":function(t){e.query=t},"on-change":e.changeQueryTable}}),a("table-body",{attrs:{"title-name":e.title,"table-loading":e.table.loading,"table-data":e.table.data}}),a("table-footer",{attrs:{pagination:e.pagination},on:{"update:pagination":function(t){e.pagination=t},"size-change":e.tableSizeChange,"page-change":e.tablePageChange}})],1)},l=[],c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("header",{staticClass:"table-header"},[a("div",{staticClass:"table-header-query"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"设备名称"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.devName,callback:function(t){e.$set(e.filterCondition.form,"devName",t)},expression:"filterCondition.form.devName"}})],1),a("el-col",{attrs:{span:6}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"授权类型"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.authType,callback:function(t){e.$set(e.filterCondition.form,"authType",t)},expression:"filterCondition.form.authType"}},e._l(e.authTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"运维内容"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.msg,callback:function(t){e.$set(e.filterCondition.form,"msg",t)},expression:"filterCondition.form.msg"}})],1),a("el-col",{attrs:{span:6,align:"right"}},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.changeQueryCondition}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")])],1)],1)],1)])},u=[],s=a("13c3"),p=a("7018"),h={props:{condition:{required:!0,type:Object}},data:function(){return{filterCondition:this.condition,debounce:null,authTypeOptions:[]}},watch:{condition:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:condition",e)}},mounted:function(){var e=this;this.initDebounceQuery(),Object(p["b"])().then((function(t){e.authTypeOptions=t}))},methods:{initDebounceQuery:function(){var e=this;this.debounce=Object(s["a"])((function(){e.$emit("on-change")}),400)},changeQueryCondition:function(){this.debounce()},resetQuery:function(){this.filterCondition.form={devName:"",authType:"",msg:""},this.changeQueryCondition()}}},d=h,g=(a("9103"),a("2877")),b=Object(g["a"])(d,c,u,!1,null,"7e5dda88",null),f=b.exports,m=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("main",{staticClass:"table-body"},[a("main",{staticClass:"table-body-main"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"calc(100% + 50px)"}},[a("el-table-column",{attrs:{width:"80",type:"index",label:"序号",align:"center"}}),a("el-table-column",{attrs:{prop:"applyTime",label:"申请时间","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"devName",label:"设备名称","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"devTagShow",label:"内外侧板","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"authTypeDesc",label:"授权类型","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"authStateStr",label:"授权状态","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"workTicket",label:"工作票号","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"optUser",label:"运维人员","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"authUser",label:"授权人","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"msg",label:"授权内容","show-overflow-tooltip":""}})],1)],1)])},v=[],y={props:{titleName:{required:!0,type:String},tableLoading:{required:!0,type:Boolean},tableData:{required:!0,type:Array}},methods:{}},w=y,C=Object(g["a"])(w,m,v,!1,null,null,null),T=C.exports,k=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",{staticClass:"table-footer"},[e.filterCondition.visible?a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.filterCondition.pageNum,"page-sizes":[10,20,50,100],"page-size":e.filterCondition.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filterCondition.total},on:{"size-change":e.clickSize,"current-change":e.clickPage}}):e._e()],1)},O=[],S={props:{pagination:{required:!0,type:Object}},data:function(){return{filterCondition:this.pagination}},watch:{pagination:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:pagination",e)}},methods:{clickSize:function(e){this.$emit("size-change",e)},clickPage:function(e){this.$emit("page-change",e)}}},j=S,N=Object(g["a"])(j,k,O,!1,null,null,null),x=N.exports,$={components:{TableHeader:f,TableBody:T,TableFooter:x},data:function(){return{title:"",query:{form:{devName:"",authType:"",msg:""}},table:{loading:!1,data:[]},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0}}},mounted:function(){this.queryTableData()},methods:{changeQueryTable:function(e){"turn-page"!==e&&(this.pagination.pageNum=1);var t=this.handleQueryParams();this.queryTableData(t)},handleQueryParams:function(){return this.query.form},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.changeQueryTable()},tablePageChange:function(e){this.pagination.pageNum=e,this.changeQueryTable("turn-page")},queryTableData:function(e){var t=this;e=Object.assign({},e,{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum,type:2}),this.table.loading=!0,this.pagination.visible=!1,Object(p["c"])(e).then((function(e){e&&(t.table.data=e.rows,t.pagination.total=e.total,t.pagination.pageNum=e.pageNum,t.pagination.pageSize=e.pageSize),t.table.loading=!1,t.pagination.visible=!0}))}}},_=$,z=Object(g["a"])(_,r,l,!1,null,null,null),q=z.exports,Q={components:{Todo:o["a"],Done:q},data:function(){return{tabName:"1"}},methods:{clickTabSwitch:function(e){this.tabName=e.name}}},D=Q,B=(a("1690"),Object(g["a"])(D,n,i,!1,null,"3bcbec6e",null));t["default"]=B.exports},7018:function(e,t,a){"use strict";a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"a",(function(){return r}));var n=a("4020");function i(e){return Object(n["a"])({url:"/api/secondAuth/all",method:"get",params:e||{}})}function o(e){return Object(n["a"])({url:"/api/secondAuth/combo/authType",method:"get"})}function r(e){return Object(n["a"])({url:"/api/secondAuth/auth2",method:"get",params:e||{}})}},"7efe":function(e,t,a){"use strict";a.d(t,"d",(function(){return i})),a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"f",(function(){return u}));a("99af"),a("a623"),a("4de4"),a("4160"),a("c975"),a("d81d"),a("13d5"),a("ace4"),a("b6802"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("466d"),a("5319"),a("1276"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("159b"),a("ddb0"),a("2b3d");var n=a("0122");a("720d"),a("4360");function i(e,t){if(0===arguments.length)return null;var a,i=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(n["a"])(e)?a=e:(10===(""+e).length&&(e=1e3*parseInt(e)),a=new Date(e));var o={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()};return i.replace(/{(y|m|d|h|i|s|a)+}/g,(function(e,t){var a=o[t];return"a"===t?["日","一","二","三","四","五","六"][a]:(e.length>0&&a<10&&(a="0"+a),a||0)}))}function o(e){if(e||"object"===Object(n["a"])(e)){var t=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(a){t[a]=e[a]&&"object"===Object(n["a"])(e[a])?o(e[a]):t[a]=e[a]})),t}console.error("argument type error")}function r(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),n=1;n<t;n++)a[n-1]=arguments[n];return a.reduce((function(e,t){return Object.keys(t).reduce((function(e,a){var n=t[a];return n.constructor===Object?e[a]=r(e[a]?e[a]:{},n):n.constructor===Array?e[a]=n.map((function(t,n){if(t.constructor===Object){var i=e[a]?e[a]:[];return r(i[n]?i[n]:{},t)}return t})):e[a]=n,e}),e)}),e)}function l(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"children",n=[],i=[];return e.forEach((function(e){e[t]&&-1===n.indexOf(e[t])&&n.push(e[t])})),n.forEach((function(n){var o={};o[t]=n,o[a]=e.filter((function(e){return n===e[t]})),i.push(o)})),i}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,a=1024,n=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],i=Math.floor(Math.log(e)/Math.log(a));return i>=0?"".concat(parseFloat((e/Math.pow(a,i)).toFixed(t))).concat(n[i]):"".concat(parseFloat(e.toFixed(t))).concat(n[0])}function u(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,a=1e4,n=["","万","亿","兆","万兆","亿兆"],i=Math.floor(Math.log(e)/Math.log(a));return i>=0?"".concat(parseFloat((e/Math.pow(a,i)).toFixed(t))).concat(n[i]):"".concat(parseFloat(e.toFixed(t))).concat(n[0])}},8446:function(e,t,a){"use strict";var n=a("8e2b"),i=a.n(n);i.a},"8e2b":function(e,t,a){},9092:function(e,t,a){},9103:function(e,t,a){"use strict";var n=a("9092"),i=a.n(n);i.a},"981a":function(e,t,a){},a394:function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("table-header",{attrs:{condition:e.query},on:{"update:condition":function(t){e.query=t},"on-change":e.changeQueryTable}}),a("table-body",{attrs:{"title-name":e.title,"table-loading":e.table.loading,"table-data":e.table.data},on:{refresh:e.queryTableData}}),a("table-footer",{attrs:{pagination:e.pagination},on:{"update:pagination":function(t){e.pagination=t},"size-change":e.tableSizeChange,"page-change":e.tablePageChange}})],1)},i=[],o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("header",{staticClass:"table-header"},[a("div",{staticClass:"table-header-query"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"设备名称"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.devName,callback:function(t){e.$set(e.filterCondition.form,"devName",t)},expression:"filterCondition.form.devName"}})],1),a("el-col",{attrs:{span:6}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"授权类型"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.authType,callback:function(t){e.$set(e.filterCondition.form,"authType",t)},expression:"filterCondition.form.authType"}},e._l(e.authTypeOptions,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"运维内容"},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.msg,callback:function(t){e.$set(e.filterCondition.form,"msg",t)},expression:"filterCondition.form.msg"}})],1),a("el-col",{attrs:{span:6,align:"right"}},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.changeQueryCondition}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")])],1)],1)],1)])},r=[],l=a("13c3"),c=a("7018"),u={props:{condition:{required:!0,type:Object}},data:function(){return{filterCondition:this.condition,debounce:null,authTypeOptions:[]}},watch:{condition:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:condition",e)}},mounted:function(){var e=this;this.initDebounceQuery(),Object(c["b"])().then((function(t){e.authTypeOptions=t}))},methods:{initDebounceQuery:function(){var e=this;this.debounce=Object(l["a"])((function(){e.$emit("on-change")}),400)},changeQueryCondition:function(){this.debounce()},resetQuery:function(){this.filterCondition.form={devName:"",authType:"",msg:""},this.changeQueryCondition()}}},s=u,p=(a("8446"),a("2877")),h=Object(p["a"])(s,o,r,!1,null,"482b1578",null),d=h.exports,g=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("main",{staticClass:"table-body"},[a("main",{staticClass:"table-body-main"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"calc(100% + 50px)"}},[a("el-table-column",{attrs:{width:"80",type:"index",label:"序号",align:"center"}}),a("el-table-column",{attrs:{prop:"applyTime",label:"申请时间","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"devName",label:"设备名称","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"devTagShow",label:"内外侧板","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"authTypeDesc",label:"授权类型","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"authStateStr",label:"授权状态","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"workTicket",label:"工作票号","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"optUser",label:"运维人员","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"msg",label:"授权内容","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{fixed:"right",width:"280",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[3!==t.row.authType?a("el-button",{staticClass:"el-button--blue",on:{click:function(a){return e.grant(t.row)}}},[e._v("授权")]):e._e(),3!==t.row.authType?a("el-button",{staticClass:"el-button--red",on:{click:function(a){return e.refuse(t.row)}}},[e._v("拒绝")]):e._e(),3===t.row.authType?a("el-button",{staticClass:"el-button--blue",on:{click:function(a){return e.cancelBlock(t.row)}}},[e._v("取消阻断")]):e._e()]}}])})],1)],1)])},b=[],f=(a("96cf"),a("c964")),m=a("7efe"),v={props:{titleName:{required:!0,type:String},tableLoading:{required:!0,type:Boolean},tableData:{required:!0,type:Array}},methods:{grant:function(e){var t=this;return Object(f["a"])(regeneratorRuntime.mark((function a(){var n,i,o,r,l,u;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,n=e.devSn,i=e.devTag,o=e.authType,e.authUser,r=e.authId,l=e.domainToken,a.next=4,Object(c["a"])({devSn:n,devTag:i,authType:o,authUser:"test",authId:r,domainToken:l,authDate:Object(m["d"])(new Date),authState:1});case 4:u=a.sent,u?(t.$message.success("授权成功"),t.$emit("refresh")):t.$message.error(u.message||"授权失败"),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),t.$message.error("授权请求失败");case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()},refuse:function(e){var t=this;return Object(f["a"])(regeneratorRuntime.mark((function a(){var n,i,o,r,l,u;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,n=e.devSn,i=e.devTag,o=e.authType,e.authUser,r=e.authId,l=e.domainToken,a.next=4,Object(c["a"])({devSn:n,devTag:i,authType:o,authUser:"test",authId:r,domainToken:l,authDate:Object(m["d"])(new Date),authState:2});case 4:u=a.sent,u.data?(t.$message.success("拒绝成功"),t.$emit("refresh")):t.$message.error(u.message||"拒绝失败"),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),t.$message.error("拒绝请求失败");case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()},cancelBlock:function(e){var t=this;return Object(f["a"])(regeneratorRuntime.mark((function a(){var n,i,o,r,l,u;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,n=e.devSn,i=e.devTag,o=e.authType,e.authUser,r=e.authId,l=e.domainToken,a.next=4,Object(c["a"])({devSn:n,devTag:i,authType:o,authUser:"test",authId:r,domainToken:l,authDate:Object(m["d"])(new Date),authState:1});case 4:u=a.sent,u?(t.$message.success("取消阻断成功"),t.$emit("refresh")):t.$message.error(u.message||"取消阻断失败"),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),t.$message.error("取消阻断请求失败");case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()}}},y=v,w=Object(p["a"])(y,g,b,!1,null,null,null),C=w.exports,T=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",{staticClass:"table-footer"},[e.filterCondition.visible?a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.filterCondition.pageNum,"page-sizes":[10,20,50,100],"page-size":e.filterCondition.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filterCondition.total},on:{"size-change":e.clickSize,"current-change":e.clickPage}}):e._e()],1)},k=[],O={props:{pagination:{required:!0,type:Object}},data:function(){return{filterCondition:this.pagination}},watch:{pagination:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:pagination",e)}},methods:{clickSize:function(e){this.$emit("size-change",e)},clickPage:function(e){this.$emit("page-change",e)}}},S=O,j=Object(p["a"])(S,T,k,!1,null,null,null),N=j.exports,x={components:{TableHeader:d,TableBody:C,TableFooter:N},data:function(){return{title:"",query:{form:{devName:"",authType:"",msg:""}},table:{loading:!1,data:[]},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0}}},mounted:function(){this.queryTableData()},methods:{changeQueryTable:function(e){"turn-page"!==e&&(this.pagination.pageNum=1);var t=this.handleQueryParams();this.queryTableData(t)},handleQueryParams:function(){return this.query.form},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.changeQueryTable()},tablePageChange:function(e){this.pagination.pageNum=e,this.changeQueryTable("turn-page")},queryTableData:function(e){var t=this;e=Object.assign({},e,{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum,type:1}),this.table.loading=!0,this.pagination.visible=!1,Object(c["c"])(e).then((function(e){e&&(t.table.data=e.rows,t.pagination.total=e.total,t.pagination.pageNum=e.pageNum,t.pagination.pageSize=e.pageSize),t.table.loading=!1,t.pagination.visible=!0}))}}},$=x,_=Object(p["a"])($,n,i,!1,null,null,null);t["a"]=_.exports}}]);