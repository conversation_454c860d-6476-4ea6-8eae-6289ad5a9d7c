(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9da964da"],{"16f0":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"basic-strategy"},[r("terminal-groups",{on:{terminalGroupId:e.getTerminalGroupId}},[r("div",{staticClass:"strategy-container"},[r("customStrategy",{ref:"customStrategy"})],1)])],1)},o=[],n=(r("96cf"),r("c964")),s=r("a4cc"),i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"custom-strategy"},[r("div",[r("p",{staticClass:"default-custom-strategy-title"},[r("span",[e._v("默认策略")]),r("i",{staticClass:"toggle-icon",class:e.defalutExpand?"el-icon-arrow-up":"el-icon-arrow-down",on:{click:function(t){e.defalutExpand=!e.defalutExpand}}})]),e.defalutExpand?e._e():r("el-form",{ref:"form",staticClass:"custom-strategy-form",attrs:{model:e.form,rules:e.rules,"label-width":"160px"}},[r("el-form-item",{attrs:{label:"实时告警",prop:"is_alarm"}},[r("el-radio-group",{model:{value:e.form.is_alarm,callback:function(t){e.$set(e.form,"is_alarm",t)},expression:"form.is_alarm"}},[r("el-radio",{attrs:{label:1}},[e._v("开启")]),r("el-radio",{attrs:{label:0}},[e._v("关闭")])],1)],1),r("el-form-item",{attrs:{label:"自保护",prop:"is_self_safe"}},[r("el-radio-group",{model:{value:e.form.is_self_safe,callback:function(t){e.$set(e.form,"is_self_safe",t)},expression:"form.is_self_safe"}},[r("el-radio",{attrs:{label:1}},[e._v("开启")]),r("el-radio",{attrs:{label:0},on:{change:e.closeSafeMode}},[e._v("关闭")])],1),0===e.form.is_self_safe?r("span",{staticClass:"tip-text"},[e._v(" 关闭自保护将降低主机安全，建议开启 ")]):e._e()],1),r("el-form-item",{attrs:{label:"终端卸载",prop:"uninstall_pwd"}},[r("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"输入卸载密码","show-password":""},model:{value:e.form.uninstall_pwd,callback:function(t){e.$set(e.form,"uninstall_pwd",t)},expression:"form.uninstall_pwd"}})],1),r("el-form-item",{attrs:{label:"终端退出",prop:"quit_pwd"}},[r("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"输入退出密码","show-password":""},model:{value:e.form.quit_pwd,callback:function(t){e.$set(e.form,"quit_pwd",t)},expression:"form.quit_pwd"}})],1),r("el-form-item",{attrs:{label:"登录超时",prop:"login_timeout_grain"}},[r("el-select",{staticStyle:{width:"140px"},model:{value:e.form.login_timeout_grain,callback:function(t){e.$set(e.form,"login_timeout_grain",t)},expression:"form.login_timeout_grain"}},[r("el-option",{attrs:{value:1,label:"永不"}}),r("el-option",{attrs:{value:2,label:"3分钟"}}),r("el-option",{attrs:{value:3,label:"10分钟"}}),r("el-option",{attrs:{value:4,label:"30分钟"}}),r("el-option",{attrs:{value:5,label:"1小时"}}),r("el-option",{attrs:{value:6,label:"2小时"}})],1),r("span",{staticClass:"tip-text"},[e._v(" 无操作状态下，安全卫士将在规定时间后自动退出登录 ")])],1),e._e(),r("el-form-item",[r("el-button",{on:{click:e.defaultPolicyCancel}},[e._v("取消")]),r("el-button",{attrs:{type:"primary"},on:{click:e.defaultPolicySave}},[e._v("应用")])],1)],1)],1),-1!==e.customForm.group_id?r("div",{staticClass:"custom-strategy-section"},[r("p",{staticClass:"custom-strategy-title"},[r("span",[e._v("自定义策略")]),r("i",{staticClass:"toggle-icon",class:e.customExpand?"el-icon-arrow-up":"el-icon-arrow-down",on:{click:function(t){e.customExpand=!e.customExpand}}})]),e.customExpand?e._e():r("el-form",{ref:"customForm",staticClass:"custom-strategy-form",attrs:{model:e.customForm,rules:e.rules,"label-width":"160px"}},[r("el-form-item",{attrs:{label:"策略对象",prop:"group_id_value"}},[r("div",[r("span",{staticClass:"disabled-input"},[e._v(" "+e._s(e.customForm.group_id_value)+" ")])])]),r("el-form-item",{attrs:{label:"实时告警",prop:"is_alarm"}},[r("el-radio-group",{model:{value:e.customForm.is_alarm,callback:function(t){e.$set(e.customForm,"is_alarm",t)},expression:"customForm.is_alarm"}},[r("el-radio",{attrs:{label:1}},[e._v("开启")]),r("el-radio",{attrs:{label:0}},[e._v("关闭")])],1)],1),r("el-form-item",{attrs:{label:"自保护",prop:"is_self_safe"}},[r("el-radio-group",{model:{value:e.customForm.is_self_safe,callback:function(t){e.$set(e.customForm,"is_self_safe",t)},expression:"customForm.is_self_safe"}},[r("el-radio",{attrs:{label:1}},[e._v("开启")]),r("el-radio",{attrs:{label:0},on:{change:e.closeCustomSafeMode}},[e._v("关闭")])],1),0===e.customForm.is_self_safe?r("span",{staticClass:"tip-text"},[e._v(" 关闭自保护将降低主机安全，建议开启 ")]):e._e()],1),r("el-form-item",{attrs:{label:"终端卸载",prop:"uninstall_pwd"}},[r("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"输入卸载密码","show-password":""},model:{value:e.customForm.uninstall_pwd,callback:function(t){e.$set(e.customForm,"uninstall_pwd","string"===typeof t?t.trim():t)},expression:"customForm.uninstall_pwd"}})],1),r("el-form-item",{attrs:{label:"终端退出",prop:"quit_pwd"}},[r("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"输入退出密码","show-password":""},model:{value:e.customForm.quit_pwd,callback:function(t){e.$set(e.customForm,"quit_pwd","string"===typeof t?t.trim():t)},expression:"customForm.quit_pwd"}})],1),r("el-form-item",{attrs:{label:"登录超时",prop:"login_timeout_grain"}},[r("el-select",{staticStyle:{width:"140px"},model:{value:e.customForm.login_timeout_grain,callback:function(t){e.$set(e.customForm,"login_timeout_grain",t)},expression:"customForm.login_timeout_grain"}},[r("el-option",{attrs:{value:1,label:"永不"}}),r("el-option",{attrs:{value:2,label:"3分钟"}}),r("el-option",{attrs:{value:3,label:"10分钟"}}),r("el-option",{attrs:{value:4,label:"30分钟"}}),r("el-option",{attrs:{value:5,label:"1小时"}}),r("el-option",{attrs:{value:6,label:"2小时"}})],1),r("span",{staticClass:"tip-text"},[e._v(" 无操作状态下，安全卫士将在规定时间后自动退出登录 ")])],1),e._e(),r("el-form-item",[r("el-button",{on:{click:e.restoreDefault}},[e._v("恢复默认")]),r("el-button",{on:{click:e.customPolicyCancel}},[e._v("取消")]),r("el-button",{attrs:{type:"primary"},on:{click:e.customPolicySave}},[e._v("应用")])],1)],1)],1):e._e()])},c=[],l=(r("b0c0"),r("f3f3")),u=r("ee97");function m(e){return Object(u["a"])({url:"/api/ieg/v1/policy/base/info",method:"get",params:e||{}})}function p(e){return Object(u["a"])({url:"/api/ieg/v1/policy/base/apply",method:"post",data:e||{}})}function f(e){return Object(u["a"])({url:"/api/ieg/v1/policy/base/reset",method:"post",data:e||{}})}var d={control_mode:"",group_id:0,is_alarm:"",is_self_safe:"",login_timeout_grain:"",quit_pwd:"",uninstall_pwd:""},g={control_mode:"",group_id:-1,is_alarm:"",is_self_safe:"",login_timeout_grain:"",quit_pwd:"",uninstall_pwd:"",group_id_value:"全部终端"},_={name:"CustomStrategy",data:function(){return{form:Object(l["a"])({},d),customForm:Object(l["a"])({},g),rules:{is_alarm:[{required:!0,trigger:"change"}],is_self_safe:[{required:!0,trigger:"change"}],uninstall_pwd:[{required:!0,message:"输入卸载密码",trigger:"blur"}],quit_pwd:[{required:!0,message:"输入退出密码",trigger:"blur"}],login_timeout_grain:[{required:!0,trigger:"change"}],control_mode:[{required:!0,trigger:"change"}]},defalutExpand:!1,customExpand:!1}},mounted:function(){this.changeTerminalGroupId(this.$store.state.hostguardian.terminalGroupsData.id,this.$store.state.hostguardian.terminalGroupsData.name)},methods:{defaultPolicyCancel:function(){var e=this;this.$confirm("确定要取消修改吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.getInfo(!0),e.$message.success("取消策略成功")})).catch((function(){}))},customPolicyCancel:function(){var e=this;this.$confirm("确定要取消修改吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.getInfo(!1),e.$message.success("取消策略成功")})).catch((function(){}))},restoreDefault:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$confirm("确定要恢复默认策略吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(n["a"])(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,f({group_id:e.customForm.group_id});case 3:r=t.sent,r&&0===r.code?(e.$message.success(r.msg||"恢复默认策略成功"),e.getInfo(!1)):e.$message.error(r&&r.msg||"恢复默认策略失败"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("恢复默认策略失败:",t.t0),e.$message.error("恢复默认策略失败");case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))).catch((function(){}));case 1:case"end":return t.stop()}}),t)})))()},defaultPolicySave:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$confirm("确定要应用默认策略吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(n["a"])(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,p(Object(l["a"])(Object(l["a"])({},e.form),{},{group_id:0}));case 3:r=t.sent,r&&0===r.code?(e.$message.success(r.msg||"默认策略应用成功"),e.getInfo(!0)):e.$message.error(r&&r.msg||"默认策略应用失败"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("默认策略应用失败:",t.t0),e.$message.error("默认策略应用失败");case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))).catch((function(){}));case 1:case"end":return t.stop()}}),t)})))()},customPolicySave:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$confirm("确定要应用自定义策略吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(n["a"])(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,p(Object(l["a"])(Object(l["a"])({},e.customForm),{},{group_id:-1===e.customForm.group_id?0:e.customForm.group_id}));case 3:r=t.sent,r&&0===r.code?(e.$message.success(r.msg||"自定义策略应用成功"),e.getInfo(!1)):e.$message.error(r&&r.msg||"自定义策略应用失败"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("自定义策略应用失败:",t.t0),e.$message.error("自定义策略应用失败");case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))).catch((function(){}));case 1:case"end":return t.stop()}}),t)})))()},closeSafeMode:function(){var e=this;this.$confirm("关闭自保护将降低主机安全，请确认？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.form.is_self_safe=0})).catch((function(){e.form.is_self_safe=1}))},closeCustomSafeMode:function(){var e=this;this.$confirm("关闭自保护将降低主机安全，请确认？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.customForm.is_self_safe=0})).catch((function(){e.customForm.is_self_safe=1}))},changeTerminalGroupId:function(e,t){var r=this;return Object(n["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:e>0?(r.defalutExpand=!0,r.customExpand=!1):(r.defalutExpand=!1,r.customExpand=!1),r.getInfo(!0),r.customForm.group_id=0===e?-1:e,-1!==e&&0!==e&&(r.customForm.group_id=e,r.customForm.group_id_value=t,r.getInfo(!1));case 4:case"end":return a.stop()}}),a)})))()},getInfo:function(e){var t=this;return Object(n["a"])(regeneratorRuntime.mark((function r(){var a,o;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(r.prev=0,!e){r.next=8;break}return r.next=4,m({group_id:0});case 4:a=r.sent,a&&0===a.code?t.form=a.data:t.$message.error(a&&a.msg||"获取默认策略失败"),r.next=12;break;case 8:return r.next=10,m({group_id:-1===t.customForm.group_id?0:t.customForm.group_id});case 10:o=r.sent,o&&0===o.code?0===o.data.group_id?t.customForm=Object(l["a"])(Object(l["a"])(Object(l["a"])({},t.customForm),o.data),{},{group_id:-1}):t.customForm=Object(l["a"])(Object(l["a"])({},t.customForm),o.data):t.$message.error(o&&o.msg||"获取自定义策略失败");case 12:r.next=18;break;case 14:r.prev=14,r.t0=r["catch"](0),console.error("获取策略信息失败:",r.t0),t.$message.error("获取策略信息失败");case 18:case"end":return r.stop()}}),r,null,[[0,14]])})))()}}},h=_,v=(r("399a"),r("2877")),b=Object(v["a"])(h,i,c,!1,null,"12111948",null),w=b.exports,x=r("d2c9"),y={name:"BasicStrategy",components:{TerminalGroups:s["a"],customStrategy:w},data:function(){return{group_id:0,group_name:""}},mounted:function(){this.loginForHg()},methods:{loginForHg:function(){var e=this;return Object(n["a"])(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(x["a"])();case 3:return r=t.sent,r||e.$message.error("登录失败，无法获取数据"),t.abrupt("return",r);case 8:return t.prev=8,t.t0=t["catch"](0),console.error("登录失败:",t.t0),e.$message.error("登录失败，无法获取数据"),t.abrupt("return","");case 13:case"end":return t.stop()}}),t,null,[[0,8]])})))()},getTerminalGroupId:function(e,t){this.$store.commit("hostguardian/setTerminalGroupsData",{id:e,name:t}),this.group_id=e,this.group_name=t,this.$refs.customStrategy.changeTerminalGroupId(e,t)},changeTab:function(){this.$refs.customStrategy.changeTerminalGroupId(this.group_id,this.group_name)}}},$=y,F=(r("5cfa"),r("bd70")),k=r.n(F),j=Object(v["a"])($,a,o,!1,null,"6423ac3c",null);"function"===typeof k.a&&k()(j);t["default"]=j.exports},"399a":function(e,t,r){"use strict";var a=r("ad97"),o=r.n(a);o.a},"5cfa":function(e,t,r){"use strict";var a=r("8215"),o=r.n(a);o.a},8215:function(e,t,r){},ad97:function(e,t,r){},bd70:function(e,t){}}]);