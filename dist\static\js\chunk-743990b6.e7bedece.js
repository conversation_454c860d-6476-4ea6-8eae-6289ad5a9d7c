(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-743990b6"],{"1f81":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"context-wrapper"},[n("h2",[e._v("通知配置")]),n("div",{staticClass:"table-wrapper"},[n("el-row",{staticClass:"right-wrap"},[n("el-col",{staticClass:"head",attrs:{span:4}},[e._v("通知内容")]),n("el-col",{staticClass:"head",attrs:{span:4}},[e._v("是否通知")]),n("el-col",{staticClass:"head",attrs:{span:8}},[e._v("通知范围")]),n("el-col",{staticClass:"head",attrs:{span:8}},[e._v("通知策略")])],1),e._l(e.data,(function(t,a){return n("el-row",{key:a,staticClass:"right-wrap"},[n("el-col",{attrs:{span:4}},[e._v(e._s(t.notificationContent))]),n("el-col",{staticClass:"head",attrs:{span:4}},[n("el-checkbox",{model:{value:t.isNotify,callback:function(n){e.$set(t,"isNotify",n)},expression:"o.isNotify"}})],1),0===a?n("el-col",{staticClass:"head",attrs:{span:8}},[n("el-checkbox-group",{model:{value:t.securityEventLevel,callback:function(n){e.$set(t,"securityEventLevel",n)},expression:"o.securityEventLevel"}},[n("el-checkbox",{attrs:{label:"3"}},[e._v("高级事件")]),n("el-checkbox",{attrs:{label:"2"}},[e._v("中级事件")]),n("el-checkbox",{attrs:{label:"1"}},[e._v("低级事件")])],1)],1):e._e(),1===a?n("el-col",{staticClass:"head",attrs:{span:8}},[n("el-checkbox-group",{model:{value:t.actionDenyAccessLog,callback:function(n){e.$set(t,"actionDenyAccessLog",n)},expression:"o.actionDenyAccessLog"}},[n("el-checkbox",{attrs:{label:"refuse"}},[e._v("动作为拒绝的访问控制日志")])],1)],1):e._e(),n("el-col",{staticClass:"head",attrs:{span:8}},[n("el-radio-group",{model:{value:t.securityEventStrategy,callback:function(n){e.$set(t,"securityEventStrategy",n)},expression:"o.securityEventStrategy"}},[n("el-radio",{attrs:{label:"2"}},[e._v(" 实时通知 ")]),n("el-radio",{attrs:{label:"1"}},[e._v(" 每天通知一次 ")])],1)],1)],1)}))],2),n("section",{staticClass:"footer-button"},[n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],attrs:{type:"primary"},on:{click:e.clickSaveSystemConfig}},[e._v(" "+e._s(e.$t("button.save"))+" ")])],1)])},c=[],s=(n("caad"),n("d81d"),n("2532"),n("f3f3")),i=n("f7b5"),o=n("4020");function l(e){return Object(o["a"])({url:"/notificationPolicy/query",method:"get",params:e||{}})}function r(e){return Object(o["a"])({url:"/notificationPolicy/update",method:"post",data:e||{}})}var u={data:function(){return{data:[]}},mounted:function(){this.init()},methods:{init:function(){this.getSystemConfig()},getSystemConfig:function(){var e=this;l().then((function(t){e.data=t.map((function(e){var t=e.notifyScopeAdvancedEvent,n=e.notifyScopeMediumEvent,a=e.notifyScopeLevelEvent,c=e.notifyScopeRealtime,i=e.notifyScopeDaily,o=[];t&&o.push("3"),n&&o.push("2"),a&&o.push("1");var l="";return c&&(l="2"),i&&(l="1"),Object(s["a"])(Object(s["a"])({},e),{},{isNotifyRaw:e.isNotify,isNotify:null===e.isNotify?null:!!e.isNotify,actionDenyAccessLogRaw:e.actionDenyAccessLog,actionDenyAccessLog:null===e.actionDenyAccessLog?null:!!e.actionDenyAccessLog,securityEventLevel:o,securityEventStrategy:l})}))}))},clickSaveSystemConfig:function(){var e=this.data.map((function(e){var t=Object(s["a"])(Object(s["a"])({},e),{},{isNotify:null===e.isNotifyRaw?null:e.isNotify?1:0,actionDenyAccessLog:null===e.actionDenyAccessLogRaw?null:e.actionDenyAccessLog?1:0,notifyScopeAdvancedEvent:null===e.notifyScopeAdvancedEvent?null:e.securityEventLevel.includes("3")?1:0,notifyScopeMediumEvent:null===e.notifyScopeMediumEvent?null:e.securityEventLevel.includes("2")?1:0,notifyScopeLevelEvent:null===e.notifyScopeLevelEvent?null:e.securityEventLevel.includes("1")?1:0,notifyScopeRealtime:null===e.notifyScopeRealtime?null:"2"===e.securityEventStrategy?1:0,notifyScopeDaily:null===e.notifyScopeDaily?null:"1"===e.securityEventStrategy?1:0});return delete t.isNotifyRaw,delete t.actionDenyAccessLogRaw,delete t.securityEventLevel,delete t.securityEventStrategy,t}));this.saveSystemConfig(e)},saveSystemConfig:function(e){r(e).then((function(e){e?Object(i["a"])({i18nCode:"tip.save.success",type:"success"}):Object(i["a"])({i18nCode:"tip.save.error",type:"error"})}))}}},v=u,y=(n("ed6d"),n("2877")),f=Object(y["a"])(v,a,c,!1,null,"5b6787fe",null);t["default"]=f.exports},2532:function(e,t,n){"use strict";var a=n("23e7"),c=n("5a34"),s=n("1d80"),i=n("ab13");a({target:"String",proto:!0,forced:!i("includes")},{includes:function(e){return!!~String(s(this)).indexOf(c(e),arguments.length>1?arguments[1]:void 0)}})},"52be":function(e,t,n){},"5a34":function(e,t,n){var a=n("44e7");e.exports=function(e){if(a(e))throw TypeError("The method doesn't accept regular expressions");return e}},ab13:function(e,t,n){var a=n("b622"),c=a("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[c]=!1,"/./"[e](t)}catch(a){}}return!1}},caad:function(e,t,n){"use strict";var a=n("23e7"),c=n("4d64").includes,s=n("44d2"),i=n("ae40"),o=i("indexOf",{ACCESSORS:!0,1:0});a({target:"Array",proto:!0,forced:!o},{includes:function(e){return c(this,e,arguments.length>1?arguments[1]:void 0)}}),s("includes")},d81d:function(e,t,n){"use strict";var a=n("23e7"),c=n("b727").map,s=n("1dde"),i=n("ae40"),o=s("map"),l=i("map");a({target:"Array",proto:!0,forced:!o||!l},{map:function(e){return c(this,e,arguments.length>1?arguments[1]:void 0)}})},ed6d:function(e,t,n){"use strict";var a=n("52be"),c=n.n(a);c.a}}]);