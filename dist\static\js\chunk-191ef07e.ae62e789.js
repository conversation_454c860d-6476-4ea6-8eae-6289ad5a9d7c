(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-191ef07e"],{"1bd4":function(e,t,a){"use strict";var r=a("6173"),n=a.n(r);n.a},"2ca0":function(e,t,a){"use strict";var r=a("23e7"),n=a("06cf").f,s=a("50c4"),i=a("5a34"),o=a("1d80"),c=a("ab13"),u=a("c430"),l="".startsWith,d=Math.min,p=c("startsWith"),h=!u&&!p&&!!function(){var e=n(String.prototype,"startsWith");return e&&!e.writable}();r({target:"String",proto:!0,forced:!h&&!p},{startsWith:function(e){var t=String(o(this));i(e);var a=s(d(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return l?l.call(t,r,a):t.slice(a,a+r.length)===r}})},"5a0c":function(e,t,a){!function(t,a){e.exports=a()}(0,(function(){"use strict";var e=1e3,t=6e4,a=36e5,r="millisecond",n="second",s="minute",i="hour",o="day",c="week",u="month",l="quarter",d="year",p="date",h="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,b={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],a=e%100;return"["+e+(t[(a-20)%10]||t[a]||t[0])+"]"}},g=function(e,t,a){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(a)+e},v={s:g,z:function(e){var t=-e.utcOffset(),a=Math.abs(t),r=Math.floor(a/60),n=a%60;return(t<=0?"+":"-")+g(r,2,"0")+":"+g(n,2,"0")},m:function e(t,a){if(t.date()<a.date())return-e(a,t);var r=12*(a.year()-t.year())+(a.month()-t.month()),n=t.clone().add(r,u),s=a-n<0,i=t.clone().add(r+(s?-1:1),u);return+(-(r+(a-n)/(s?n-i:i-n))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:u,y:d,w:c,d:o,D:p,h:i,m:s,s:n,ms:r,Q:l}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},k="en",y={};y[k]=b;var w="$isDayjsObject",$=function(e){return e instanceof T||!(!e||!e[w])},x=function e(t,a,r){var n;if(!t)return k;if("string"==typeof t){var s=t.toLowerCase();y[s]&&(n=s),a&&(y[s]=a,n=s);var i=t.split("-");if(!n&&i.length>1)return e(i[0])}else{var o=t.name;y[o]=t,n=o}return!r&&n&&(k=n),n||!r&&k},D=function(e,t){if($(e))return e.clone();var a="object"==typeof t?t:{};return a.date=e,a.args=arguments,new T(a)},_=v;_.l=x,_.i=$,_.w=function(e,t){return D(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var T=function(){function b(e){this.$L=x(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[w]=!0}var g=b.prototype;return g.parse=function(e){this.$d=function(e){var t=e.date,a=e.utc;if(null===t)return new Date(NaN);if(_.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(f);if(r){var n=r[2]-1||0,s=(r[7]||"0").substring(0,3);return a?new Date(Date.UTC(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],n,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(t)}(e),this.init()},g.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},g.$utils=function(){return _},g.isValid=function(){return!(this.$d.toString()===h)},g.isSame=function(e,t){var a=D(e);return this.startOf(t)<=a&&a<=this.endOf(t)},g.isAfter=function(e,t){return D(e)<this.startOf(t)},g.isBefore=function(e,t){return this.endOf(t)<D(e)},g.$g=function(e,t,a){return _.u(e)?this[t]:this.set(a,e)},g.unix=function(){return Math.floor(this.valueOf()/1e3)},g.valueOf=function(){return this.$d.getTime()},g.startOf=function(e,t){var a=this,r=!!_.u(t)||t,l=_.p(e),h=function(e,t){var n=_.w(a.$u?Date.UTC(a.$y,t,e):new Date(a.$y,t,e),a);return r?n:n.endOf(o)},f=function(e,t){return _.w(a.toDate()[e].apply(a.toDate("s"),(r?[0,0,0,0]:[23,59,59,999]).slice(t)),a)},m=this.$W,b=this.$M,g=this.$D,v="set"+(this.$u?"UTC":"");switch(l){case d:return r?h(1,0):h(31,11);case u:return r?h(1,b):h(0,b+1);case c:var k=this.$locale().weekStart||0,y=(m<k?m+7:m)-k;return h(r?g-y:g+(6-y),b);case o:case p:return f(v+"Hours",0);case i:return f(v+"Minutes",1);case s:return f(v+"Seconds",2);case n:return f(v+"Milliseconds",3);default:return this.clone()}},g.endOf=function(e){return this.startOf(e,!1)},g.$set=function(e,t){var a,c=_.p(e),l="set"+(this.$u?"UTC":""),h=(a={},a[o]=l+"Date",a[p]=l+"Date",a[u]=l+"Month",a[d]=l+"FullYear",a[i]=l+"Hours",a[s]=l+"Minutes",a[n]=l+"Seconds",a[r]=l+"Milliseconds",a)[c],f=c===o?this.$D+(t-this.$W):t;if(c===u||c===d){var m=this.clone().set(p,1);m.$d[h](f),m.init(),this.$d=m.set(p,Math.min(this.$D,m.daysInMonth())).$d}else h&&this.$d[h](f);return this.init(),this},g.set=function(e,t){return this.clone().$set(e,t)},g.get=function(e){return this[_.p(e)]()},g.add=function(r,l){var p,h=this;r=Number(r);var f=_.p(l),m=function(e){var t=D(h);return _.w(t.date(t.date()+Math.round(e*r)),h)};if(f===u)return this.set(u,this.$M+r);if(f===d)return this.set(d,this.$y+r);if(f===o)return m(1);if(f===c)return m(7);var b=(p={},p[s]=t,p[i]=a,p[n]=e,p)[f]||1,g=this.$d.getTime()+r*b;return _.w(g,this)},g.subtract=function(e,t){return this.add(-1*e,t)},g.format=function(e){var t=this,a=this.$locale();if(!this.isValid())return a.invalidDate||h;var r=e||"YYYY-MM-DDTHH:mm:ssZ",n=_.z(this),s=this.$H,i=this.$m,o=this.$M,c=a.weekdays,u=a.months,l=a.meridiem,d=function(e,a,n,s){return e&&(e[a]||e(t,r))||n[a].slice(0,s)},p=function(e){return _.s(s%12||12,e,"0")},f=l||function(e,t,a){var r=e<12?"AM":"PM";return a?r.toLowerCase():r};return r.replace(m,(function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return _.s(t.$y,4,"0");case"M":return o+1;case"MM":return _.s(o+1,2,"0");case"MMM":return d(a.monthsShort,o,u,3);case"MMMM":return d(u,o);case"D":return t.$D;case"DD":return _.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return d(a.weekdaysMin,t.$W,c,2);case"ddd":return d(a.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(s);case"HH":return _.s(s,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return f(s,i,!0);case"A":return f(s,i,!1);case"m":return String(i);case"mm":return _.s(i,2,"0");case"s":return String(t.$s);case"ss":return _.s(t.$s,2,"0");case"SSS":return _.s(t.$ms,3,"0");case"Z":return n}return null}(e)||n.replace(":","")}))},g.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},g.diff=function(r,p,h){var f,m=this,b=_.p(p),g=D(r),v=(g.utcOffset()-this.utcOffset())*t,k=this-g,y=function(){return _.m(m,g)};switch(b){case d:f=y()/12;break;case u:f=y();break;case l:f=y()/3;break;case c:f=(k-v)/6048e5;break;case o:f=(k-v)/864e5;break;case i:f=k/a;break;case s:f=k/t;break;case n:f=k/e;break;default:f=k}return h?f:_.a(f)},g.daysInMonth=function(){return this.endOf(u).$D},g.$locale=function(){return y[this.$L]},g.locale=function(e,t){if(!e)return this.$L;var a=this.clone(),r=x(e,t,!0);return r&&(a.$L=r),a},g.clone=function(){return _.w(this.$d,this)},g.toDate=function(){return new Date(this.valueOf())},g.toJSON=function(){return this.isValid()?this.toISOString():null},g.toISOString=function(){return this.$d.toISOString()},g.toString=function(){return this.$d.toUTCString()},b}(),S=T.prototype;return D.prototype=S,[["$ms",r],["$s",n],["$m",s],["$H",i],["$W",o],["$M",u],["$y",d],["$D",p]].forEach((function(e){S[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),D.extend=function(e,t){return e.$i||(e(t,T,D),e.$i=!0),D},D.locale=x,D.isDayjs=$,D.unix=function(e){return D(1e3*e)},D.en=y[k],D.Ls=y,D.p={},D}))},"5a34":function(e,t,a){var r=a("44e7");e.exports=function(e){if(r(e))throw TypeError("The method doesn't accept regular expressions");return e}},6173:function(e,t,a){},"888e":function(e,t,a){},"977b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("header",{staticClass:"table-header"},[a("section",{staticClass:"table-header-main"},[a("section",{staticClass:"table-header-search"},[a("section",{directives:[{name:"show",rawName:"v-show",value:!e.isShow,expression:"!isShow"}],staticClass:"table-header-search-input"},[a("el-input",{attrs:{clearable:"",placeholder:"备份名称","prefix-icon":"soc-icon-search"},on:{change:e.handleQuery},model:{value:e.queryInput.backupName,callback:function(t){e.$set(e.queryInput,"backupName",t)},expression:"queryInput.backupName"}})],1),a("section",{staticClass:"table-header-search-button"},[e.isShow?e._e():a("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),a("el-button",{on:{click:e.toggleShow}},[e._v(" 高级搜索 "),a("i",{staticClass:"el-icon--right",class:e.isShow?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),a("section",{staticClass:"table-header-button"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleCreateBackup}},[e._v("创建备份")]),a("el-button",{attrs:{type:"danger"},on:{click:e.handleBatchDelete}},[e._v("批量删除")])],1)]),a("section",{staticClass:"table-header-extend"},[a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}]},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-input",{attrs:{clearable:"",placeholder:"备份名称"},on:{change:e.handleQuery},model:{value:e.queryInput.backupName,callback:function(t){e.$set(e.queryInput,"backupName",t)},expression:"queryInput.backupName"}})],1),a("el-col",{attrs:{span:6}},[a("el-select",{attrs:{clearable:"",placeholder:"备份类型"},on:{change:e.handleQuery},model:{value:e.queryInput.backupType,callback:function(t){e.$set(e.queryInput,"backupType",t)},expression:"queryInput.backupType"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"完整备份",value:"full"}}),a("el-option",{attrs:{label:"配置备份",value:"config"}}),a("el-option",{attrs:{label:"策略备份",value:"policy"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},on:{change:e.handleQuery},model:{value:e.queryInput.createTime,callback:function(t){e.$set(e.queryInput,"createTime",t)},expression:"queryInput.createTime"}})],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24,align:"right"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")]),a("el-button",{attrs:{icon:"soc-icon-scroller-top-all"},on:{click:e.toggleShow}})],1)],1)],1)])],1)]),a("main",{staticClass:"table-body"},[e._m(0),a("section",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-body-main"},[a("el-table",{attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"序号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s((e.pagination.currentPage-1)*e.pagination.pageSize+t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{prop:"backupName",label:"备份名称","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"backupType",label:"备份类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getBackupTypeText(t.row.backupType))+" ")]}}])}),a("el-table-column",{attrs:{prop:"fileSize",label:"文件大小"}}),a("el-table-column",{attrs:{prop:"deviceCount",label:"设备数量"}}),a("el-table-column",{attrs:{prop:"status",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e.getStatusClass(t.row.status)},[e._v(" "+e._s(e.getStatusText(t.row.status))+" ")])]}}])}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.createTime))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"action-buttons"},[a("el-button",{staticClass:"el-button--blue",attrs:{type:"text",disabled:"success"!==t.row.status},on:{click:function(a){return e.handleRestore(t.row)}}},[e._v("还原")]),a("el-button",{staticClass:"el-button--blue",attrs:{type:"text",disabled:"success"!==t.row.status},on:{click:function(a){return e.handleDownload(t.row)}}},[e._v("下载")]),a("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])],1)]}}])})],1)],1)]),a("footer",{staticClass:"table-footer"},[e.pagination.visible?a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.pagination.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handlePageChange}}):e._e()],1),a("backup-modal",{attrs:{visible:e.backupModalVisible},on:{"update:visible":function(t){e.backupModalVisible=t},"on-submit":e.handleBackupSubmit}}),a("restore-modal",{attrs:{visible:e.restoreModalVisible,"current-backup":e.currentBackup},on:{"update:visible":function(t){e.restoreModalVisible=t},"on-submit":e.handleRestoreSubmit}})],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",{staticClass:"table-body-header"},[a("h2",{staticClass:"table-body-title"},[e._v("备份管理")])])}],s=(a("a15b"),a("d81d"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("f3f3")),i=(a("96cf"),a("c964")),o=a("c9d9");function c(e){return Object(o["a"])({url:"/dev/backup/pages",method:"post",data:e||{}})}function u(e){return Object(o["a"])({url:"/dev/backup/create",method:"post",data:e||{}})}function l(e){return Object(o["a"])({url:"/dev/backup/restore",method:"post",data:e||{}})}function d(e){return Object(o["a"])({url:"/dev/backup/delete",method:"post",data:e||{}})}function p(e){return Object(o["a"])({url:"/dev/backup/download",method:"post",data:e||{},responseType:"blob"})}function h(e){return Object(o["a"])({url:"/dev/device/all",method:"post",data:e||{}})}var f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"创建备份",visible:e.dialogVisible,width:"600px","before-close":e.handleClose,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"form",attrs:{model:e.formData,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"备份名称",prop:"backupName"}},[a("el-input",{attrs:{placeholder:"请输入备份名称"},model:{value:e.formData.backupName,callback:function(t){e.$set(e.formData,"backupName",t)},expression:"formData.backupName"}})],1),a("el-form-item",{attrs:{label:"选择设备",prop:"deviceIds"}},[a("el-tree",{ref:"deviceTree",attrs:{data:e.deviceData,"show-checkbox":"","node-key":"srcId",props:e.treeProps,"check-strictly":!1,"default-checked-keys":e.selectedDeviceIds},on:{check:e.handleTreeCheck}})],1),a("el-form-item",{attrs:{label:"备份类型",prop:"backupType"}},[a("el-radio-group",{model:{value:e.formData.backupType,callback:function(t){e.$set(e.formData,"backupType",t)},expression:"formData.backupType"}},[a("el-radio",{attrs:{value:"full"}},[e._v("完整备份")]),a("el-radio",{attrs:{value:"config"}},[e._v("配置备份")]),a("el-radio",{attrs:{value:"policy"}},[e._v("策略备份")])],1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",rows:3,maxlength:"100","show-word-limit":""},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v("创建备份")])],1)],1)},m=[],b=(a("4160"),a("159b"),{name:"BackupModal",props:{visible:{type:Boolean,default:!1}},data:function(){return{loading:!1,submitLoading:!1,formData:{backupName:"",deviceIds:[],backupType:"full",remark:""},rules:{backupName:[{required:!0,message:"请输入备份名称",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5\w]{1,30}$/,message:"字符串长度范围: 1 - 30",trigger:"blur"}],deviceIds:[{required:!0,message:"请选择设备",trigger:"change"}],backupType:[{required:!0,message:"请选择备份类型",trigger:"change"}]},deviceData:[],selectedDeviceIds:[],treeProps:{children:"childList",label:"name",disabled:function(e){return"0"===e.type}}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{visible:function(e){e&&(this.initForm(),this.loadDeviceData())}},methods:{initForm:function(){var e=this;this.formData={backupName:"",deviceIds:[],backupType:"full",remark:""},this.selectedDeviceIds=[],this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},loadDeviceData:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,h({});case 4:a=t.sent,0===a.retcode?e.deviceData=e.transformTreeData(a.data||[]):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](1),e.$message.error("获取设备列表失败");case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,null,[[1,8,11,14]])})))()},transformTreeData:function(e){var t=this;return e.map((function(e){var a=Object(s["a"])(Object(s["a"])({},e),{},{disabled:"0"===e.type});return e.childList&&e.childList.length>0&&(a.childList=t.transformTreeData(e.childList)),a}))},handleTreeCheck:function(e,t){var a=[],r=t.checkedNodes||[];r.forEach((function(e){"1"===e.type&&a.push(e.srcId)})),this.formData.deviceIds=a},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a&&e.$confirm("确认创建备份吗？备份过程可能需要一些时间。","确认备份",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.submitLoading=!0,t.prev=1,a={backupName:e.formData.backupName,deviceIds:e.formData.deviceIds.join(","),backupType:e.formData.backupType,remark:e.formData.remark},t.next=5,u(a);case 5:r=t.sent,0===r.retcode?(e.$message.success("备份任务已创建，请稍后查看备份结果"),e.$emit("on-submit"),e.handleClose()):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),e.$message.error("创建备份失败");case 12:return t.prev=12,e.submitLoading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[1,9,12,15]])})))).catch((function(){}));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleClose:function(){this.dialogVisible=!1}}}),g=b,v=(a("e286"),a("2877")),k=Object(v["a"])(g,f,m,!1,null,"e647d766",null),y=k.exports,w=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"还原备份",visible:e.dialogVisible,width:"600px","before-close":e.handleClose,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"form",attrs:{model:e.formData,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"备份信息"}},[a("div",{staticClass:"backup-info"},[a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[e._v("备份名称：")]),a("span",{staticClass:"value"},[e._v(e._s(e.currentBackup.backupName||"-"))])]),a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[e._v("备份类型：")]),a("span",{staticClass:"value"},[e._v(e._s(e.getBackupTypeText(e.currentBackup.backupType)))])]),a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[e._v("创建时间：")]),a("span",{staticClass:"value"},[e._v(e._s(e.formatTime(e.currentBackup.createTime)))])]),a("div",{staticClass:"info-row"},[a("span",{staticClass:"label"},[e._v("备份大小：")]),a("span",{staticClass:"value"},[e._v(e._s(e.currentBackup.fileSize||"-"))])])])]),a("el-form-item",{attrs:{label:"选择设备",prop:"deviceIds"}},[a("el-tree",{ref:"deviceTree",attrs:{data:e.deviceData,"show-checkbox":"","node-key":"srcId",props:e.treeProps,"check-strictly":!1,"default-checked-keys":e.selectedDeviceIds},on:{check:e.handleTreeCheck}})],1),a("el-form-item",{attrs:{label:"还原选项",prop:"restoreOptions"}},[a("el-checkbox-group",{model:{value:e.formData.restoreOptions,callback:function(t){e.$set(e.formData,"restoreOptions",t)},expression:"formData.restoreOptions"}},[a("el-checkbox",{attrs:{value:"config"}},[e._v("配置文件")]),a("el-checkbox",{attrs:{value:"policy"}},[e._v("策略规则")]),a("el-checkbox",{attrs:{value:"user"}},[e._v("用户信息")]),a("el-checkbox",{attrs:{value:"log"}},[e._v("日志设置")])],1)],1),a("el-alert",{attrs:{title:"警告",type:"warning",description:"还原操作将覆盖目标设备的现有配置，请确认后再执行。建议在还原前先创建当前配置的备份。","show-icon":"",closable:!1}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleClose}},[e._v("取消")]),a("el-button",{attrs:{type:"danger",loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v("确认还原")])],1)],1)},$=[],x=a("5a0c"),D=a.n(x),_={name:"RestoreModal",props:{visible:{type:Boolean,default:!1},currentBackup:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,submitLoading:!1,formData:{deviceIds:[],restoreOptions:["config","policy"]},rules:{deviceIds:[{required:!0,message:"请选择设备",trigger:"change"}],restoreOptions:[{required:!0,message:"请选择还原选项",trigger:"change"}]},deviceData:[],selectedDeviceIds:[],treeProps:{children:"childList",label:"name",disabled:function(e){return"0"===e.type}}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{visible:function(e){e&&(this.initForm(),this.loadDeviceData())}},methods:{initForm:function(){var e=this;this.formData={deviceIds:[],restoreOptions:["config","policy"]},this.selectedDeviceIds=[],this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},loadDeviceData:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,h({});case 4:a=t.sent,0===a.retcode?e.deviceData=e.transformTreeData(a.data||[]):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](1),e.$message.error("获取设备列表失败");case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,null,[[1,8,11,14]])})))()},transformTreeData:function(e){var t=this;return e.map((function(e){var a=Object(s["a"])(Object(s["a"])({},e),{},{disabled:"0"===e.type});return e.childList&&e.childList.length>0&&(a.childList=t.transformTreeData(e.childList)),a}))},handleTreeCheck:function(e,t){var a=[],r=t.checkedNodes||[];r.forEach((function(e){"1"===e.type&&a.push(e.srcId)})),this.formData.deviceIds=a},getBackupTypeText:function(e){var t={full:"完整备份",config:"配置备份",policy:"策略备份"};return t[e]||"-"},formatTime:function(e){return e?D()(e).format("YYYY-MM-DD HH:mm:ss"):"-"},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a&&e.$confirm("确认还原备份吗？此操作将覆盖目标设备的现有配置，且不可撤销！","危险操作确认",{confirmButtonText:"确认还原",cancelButtonText:"取消",type:"error",dangerouslyUseHTMLString:!0,message:"<p>还原操作风险提示：</p><ul><li>将覆盖目标设备的现有配置</li><li>操作不可撤销</li><li>建议先备份当前配置</li></ul>"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.submitLoading=!0,t.prev=1,a={backupId:e.currentBackup.id,deviceIds:e.formData.deviceIds.join(","),restoreOptions:e.formData.restoreOptions.join(",")},t.next=5,l(a);case 5:r=t.sent,0===r.retcode?(e.$message.success("还原任务已创建，请稍后查看还原结果"),e.$emit("on-submit"),e.handleClose()):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),e.$message.error("还原失败");case 12:return t.prev=12,e.submitLoading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[1,9,12,15]])})))).catch((function(){}));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleClose:function(){this.dialogVisible=!1}}},T=_,S=(a("b188"),Object(v["a"])(T,w,$,!1,null,"c2385eb6",null)),C=S.exports,O={name:"BackupRestore",components:{BackupModal:y,RestoreModal:C},data:function(){return{isShow:!1,loading:!1,queryInput:{backupName:"",backupType:"",createTime:null},tableData:[],selectedRows:[],pagination:{total:0,pageSize:10,currentPage:1,visible:!0},backupModalVisible:!1,restoreModalVisible:!1,currentBackup:{}}},mounted:function(){this.getBackupList()},methods:{toggleShow:function(){this.isShow=!this.isShow},getBackupList:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,a=Object(s["a"])({pageIndex:e.pagination.currentPage,pageSize:e.pagination.pageSize},e.buildQueryParams()),t.prev=2,t.next=5,c(a);case 5:r=t.sent,0===r.retcode?(e.tableData=r.data.rows||[],e.pagination.total=r.data.total||0,e.selectedRows=[]):e.$message.error(r.msg),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),e.$message.error("获取备份列表失败");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[2,9,12,15]])})))()},buildQueryParams:function(){var e={};return this.queryInput.backupName&&(e.backupName=this.queryInput.backupName),this.queryInput.backupType&&(e.backupType=this.queryInput.backupType),this.queryInput.createTime&&this.queryInput.createTime.length>0&&(e.startDate=this.queryInput.createTime[0],e.endDate=this.queryInput.createTime[1]),e},handleQuery:function(){this.pagination.currentPage=1,this.getBackupList()},handleReset:function(){this.queryInput={backupName:"",backupType:"",createTime:null},this.handleQuery()},handleCreateBackup:function(){this.backupModalVisible=!0},handleRestore:function(e){this.currentBackup=e,this.restoreModalVisible=!0},handleDownload:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function a(){var r,n,s,i;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,p({id:e.id});case 3:r=a.sent,n=new Blob([r]),s=window.URL.createObjectURL(n),i=document.createElement("a"),i.href=s,i.download=e.backupName+".backup",i.click(),window.URL.revokeObjectURL(s),a.next=16;break;case 13:a.prev=13,a.t0=a["catch"](0),t.$message.error("下载失败");case 16:case"end":return a.stop()}}),a,null,[[0,13]])})))()},handleDelete:function(e){var t=this;this.$confirm("确定要删除该备份吗?删除后不可恢复","删除",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,d({ids:e.id});case 3:r=a.sent,0===r.retcode?(t.$message.success("删除成功"),t.getBackupList()):t.$message.error(r.msg),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),t.$message.error("删除失败");case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))).catch((function(){}))},handleBatchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm("确定要删除选中备份吗?删除后不可恢复","删除",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a=e.selectedRows.map((function(e){return e.id})).join(","),t.next=4,d({ids:a});case 4:r=t.sent,0===r.retcode?(e.$message.success("删除成功"),e.getBackupList()):e.$message.error(r.msg),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.$message.error("删除失败");case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))).catch((function(){})):this.$message.error("至少选中一条数据")},handleBackupSubmit:function(){this.backupModalVisible=!1,this.getBackupList()},handleRestoreSubmit:function(){this.restoreModalVisible=!1,this.getBackupList()},handleSelectionChange:function(e){this.selectedRows=e},handleSizeChange:function(e){this.pagination.pageSize=e,this.getBackupList()},handlePageChange:function(e){this.pagination.currentPage=e,this.getBackupList()},getBackupTypeText:function(e){var t={full:"完整备份",config:"配置备份",policy:"策略备份"};return t[e]||"-"},getStatusText:function(e){var t={pending:"备份中",success:"成功",failed:"失败"};return t[e]||"-"},getStatusClass:function(e){var t={pending:"status-warning",success:"status-success",failed:"status-failed"};return t[e]||""},formatTime:function(e){return e&&"-"!==e?D()(e).format("YYYY-MM-DD HH:mm:ss"):"-"}}},M=O,I=(a("1bd4"),Object(v["a"])(M,r,n,!1,null,"12d983ba",null));t["default"]=I.exports},ab13:function(e,t,a){var r=a("b622"),n=r("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,"/./"[e](t)}catch(r){}}return!1}},b188:function(e,t,a){"use strict";var r=a("888e"),n=a.n(r);n.a},c9d9:function(e,t,a){"use strict";a("99af"),a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),a("5319"),a("2ca0");var r=a("bc3a"),n=a.n(r),s=a("4360"),i=a("a18c"),o=a("a47e"),c=a("f7b5"),u=a("f907"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",r=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),l=r.NODE_ENV,d=r.VUE_APP_IS_MOCK,p=r.VUE_APP_BASE_API,h="true"===d?"":p;"production"===l&&(h="");var f={baseURL:h,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===l&&(f.timeout=a),t){case"upload":f.headers["Content-Type"]="multipart/form-data",f["processData"]=!1,f["contentType"]=!1;break;case"download":f["responseType"]="blob";break;case"eventSource":break;default:break}var m=n.a.create(f);return m.interceptors.request.use((function(e){var t=s["a"].getters.token;return""!==t&&(e.headers["access_token"]=t,e.url.startsWith("/api2/")&&(e.headers["Authorization"]="Basic YWRtaW5pc3RyYXRvcjpBZG1pbjEyMw==")),e}),(function(e){Object(c["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:e,print:!0}),Promise.reject("response-err:"+e)})),m.interceptors.response.use((function(e){var a=void 0===e.headers["code"]?200:Number(e.headers["code"]),r=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(s["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",r=arguments.length>2?arguments[2]:void 0,n="";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n="error"),e.data.code>=2e3&&e.data.code<3e3&&(n="warning"),Object(c["a"])({i18nCode:"ajax.".concat(a,".").concat(t),type:n}),Promise.reject("response-err-status:".concat(r||u["a"][a][t]," \nerr-question: ").concat(o["a"].t("ajax.".concat(a,".").concat(t))))};switch(e.data.code){case u["a"].exception.system:t("system");break;case u["a"].exception.server:t("server");break;case u["a"].exception.session:r();break;case u["a"].exception.access:r();break;case u["a"].exception.certification:t("certification");break;case u["a"].exception.auth:t("auth"),i["a"].replace({path:"/401"});break;case u["a"].exception.token:t("token");break;case u["a"].exception.param:t("param");break;case u["a"].exception.idempotency:t("idempotency");break;case u["a"].exception.ip:t("ip"),s["a"].dispatch("user/reset"),i["a"].replace({path:"/login"});break;case u["a"].exception.upload:t("upload");break;case u["a"].attack.xss:t("xss","attack");break;default:t("code","exception",-1);break}};switch(t){case"upload":if(0===a)return e.data.data;n();break;case"download":if(0===a)return{data:e.data,fileName:decodeURI(e.headers["file-name"])};n();break;default:if(0===e.data.code||0===e.data.retcode)return e.data;n();break}}),(function(e){var a=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(s["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))};return"upload"===t?(Object(c["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),403==e.response.status&&a(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(o["a"].t("ajax.service.upload")))):(Object(c["a"])({i18nCode:"ajax.service.timeout",type:"error"}),403==e.response.status&&a(),Promise.reject("response-err-status:".concat(e," \nerr-question: ").concat(o["a"].t("ajax.service.timeout"))))})),m(e)};t["a"]=l},d81d:function(e,t,a){"use strict";var r=a("23e7"),n=a("b727").map,s=a("1dde"),i=a("ae40"),o=s("map"),c=i("map");r({target:"Array",proto:!0,forced:!o||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},dc36:function(e,t,a){},e286:function(e,t,a){"use strict";var r=a("dc36"),n=a.n(r);n.a}}]);