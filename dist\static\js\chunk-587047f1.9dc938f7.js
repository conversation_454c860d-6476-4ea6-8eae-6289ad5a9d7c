(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-587047f1"],{"078a":function(e,t,a){"use strict";var i=a("2b0e"),n=(a("99af"),a("caad"),a("ac1f"),a("2532"),a("5319"),{bind:function(e,t,a){var i=[e.querySelector(".el-dialog__header"),e.querySelector(".el-dialog")],n=i[0],r=i[1];n.style.cssText+=";cursor:move;",r.style.cssText+=";top:0px;";var o=function(){return window.document.currentStyle?function(e,t){return e.currentStyle[t]}:function(e,t){return getComputedStyle(e,!1)[t]}}();n.onmousedown=function(e){var t=[e.clientX-n.offsetLeft,e.clientY-n.offsetTop,r.offsetWidth,r.offsetHeight,document.body.clientWidth,document.body.clientHeight],i=t[0],s=t[1],c=t[2],l=t[3],u=t[4],d=t[5],f=[r.offsetLeft,u-r.offsetLeft-c,r.offsetTop,d-r.offsetTop-l],p=f[0],m=f[1],h=f[2],v=f[3],g=[o(r,"left"),o(r,"top")],y=g[0],b=g[1];y.includes("%")?(y=+document.body.clientWidth*(+y.replace(/%/g,"")/100),b=+document.body.clientHeight*(+b.replace(/%/g,"")/100)):(y=+y.replace(/px/g,""),b=+b.replace(/px/g,"")),document.onmousemove=function(e){var t=e.clientX-i,n=e.clientY-s;-t>p?t=-p:t>m&&(t=m),-n>h?n=-h:n>v&&(n=v),r.style.cssText+=";left:".concat(t+y,"px;top:").concat(n+b,"px;"),a.child.$emit("dragDialog")},document.onmouseup=function(){document.onmousemove=document.onmouseup=null}}}}),r=function(e){e.directive("el-dialog-drag",n)};window.Vue&&(window["el-dialog-drag"]=n,i["default"].use(r)),n.elDialogDrag=r;t["a"]=n},"212a":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-container",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"iframe-wrapper"},[a("iframe",{ref:"frameDOM",style:{width:e.width,height:e.height},attrs:{src:e.frameUrl,frameborder:"0"}})])},n=[],r={props:{width:{type:String,default:"100%"},height:{type:String,default:"100%"},frameUrl:{required:!0,type:String}},data:function(){return{loading:!0}},mounted:function(){this.onload()},methods:{onload:function(){var e=this,t=this.$refs.frameDOM;t.attachEvent?t.attachEvent("onload",(function(){e.loading=!1})):t.onload=function(){e.loading=!1}},reload:function(){this.$refs.frameDOM.contentWindow.location.reload(!0)}}},o=r,s=(a("fde8"),a("2877")),c=Object(s["a"])(o,i,n,!1,null,"6d9f4d83",null),l=c.exports;t["a"]=l},2532:function(e,t,a){"use strict";var i=a("23e7"),n=a("5a34"),r=a("1d80"),o=a("ab13");i({target:"String",proto:!0,forced:!o("includes")},{includes:function(e){return!!~String(r(this)).indexOf(n(e),arguments.length>1?arguments[1]:void 0)}})},"5a34":function(e,t,a){var i=a("44e7");e.exports=function(e){if(i(e))throw TypeError("The method doesn't accept regular expressions");return e}},"64cf":function(e,t,a){},6583:function(e,t,a){"use strict";var i=a("64cf"),n=a.n(i);n.a},"7efe":function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"f",(function(){return l}));a("99af"),a("a623"),a("4de4"),a("4160"),a("c975"),a("d81d"),a("13d5"),a("ace4"),a("b6802"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("466d"),a("5319"),a("1276"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("159b"),a("ddb0"),a("2b3d");var i=a("0122");a("720d"),a("4360");function n(e,t){if(0===arguments.length)return null;var a,n=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"===Object(i["a"])(e)?a=e:(10===(""+e).length&&(e=1e3*parseInt(e)),a=new Date(e));var r={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()};return n.replace(/{(y|m|d|h|i|s|a)+}/g,(function(e,t){var a=r[t];return"a"===t?["日","一","二","三","四","五","六"][a]:(e.length>0&&a<10&&(a="0"+a),a||0)}))}function r(e){if(e||"object"===Object(i["a"])(e)){var t=e.constructor===Array?[]:{};return Object.keys(e).forEach((function(a){t[a]=e[a]&&"object"===Object(i["a"])(e[a])?r(e[a]):t[a]=e[a]})),t}console.error("argument type error")}function o(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),i=1;i<t;i++)a[i-1]=arguments[i];return a.reduce((function(e,t){return Object.keys(t).reduce((function(e,a){var i=t[a];return i.constructor===Object?e[a]=o(e[a]?e[a]:{},i):i.constructor===Array?e[a]=i.map((function(t,i){if(t.constructor===Object){var n=e[a]?e[a]:[];return o(n[i]?n[i]:{},t)}return t})):e[a]=i,e}),e)}),e)}function s(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"children",i=[],n=[];return e.forEach((function(e){e[t]&&-1===i.indexOf(e[t])&&i.push(e[t])})),i.forEach((function(i){var r={};r[t]=i,r[a]=e.filter((function(e){return i===e[t]})),n.push(r)})),n}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,a=1024,i=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],n=Math.floor(Math.log(e)/Math.log(a));return n>=0?"".concat(parseFloat((e/Math.pow(a,n)).toFixed(t))).concat(i[n]):"".concat(parseFloat(e.toFixed(t))).concat(i[0])}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,a=1e4,i=["","万","亿","兆","万兆","亿兆"],n=Math.floor(Math.log(e)/Math.log(a));return n>=0?"".concat(parseFloat((e/Math.pow(a,n)).toFixed(t))).concat(i[n]):"".concat(parseFloat(e.toFixed(t))).concat(i[0])}},"807e":function(e,t,a){},ab13:function(e,t,a){var i=a("b622"),n=i("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,"/./"[e](t)}catch(i){}}return!1}},caad:function(e,t,a){"use strict";var i=a("23e7"),n=a("4d64").includes,r=a("44d2"),o=a("ae40"),s=o("indexOf",{ACCESSORS:!0,1:0});i({target:"Array",proto:!0,forced:!s},{includes:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),r("includes")},fde8:function(e,t,a){"use strict";var i=a("807e"),n=a.n(i);n.a},ff1c:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container-wrapper"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"panel"},[a("div",{staticClass:"panel-heading panel-info"},[e._v(" 安全报表 "),a("div",{staticClass:"panel-heading-action"},[a("a",{ref:"panel-collapse",attrs:{href:"#"}},[a("i",{staticClass:"el-icon-minus"})])])]),a("div",{staticClass:"panel-widget"},[a("div",{staticClass:"panel-widget-body"},[a("el-form",{ref:"formTemplate",attrs:{model:e.query.asset,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:e.$t("report.show.asset.datetime"),prop:"assetType"}},[a("el-date-picker",{attrs:{type:"daterange",clearable:"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd","start-placeholder":e.$t("time.option.startDate"),"end-placeholder":e.$t("time.option.endTime"),size:"mini"},model:{value:e.query.asset.datetime,callback:function(t){e.$set(e.query.asset,"datetime",t)},expression:"query.asset.datetime"}})],1)],1)],1),a("div",{staticClass:"panel-widget-footer"},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:function(t){return e.clickViewReport("asset")}}},[e._v(" "+e._s(e.$t("button.query"))+" ")])],1)])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"panel"},[a("div",{staticClass:"panel-heading panel-info"},[e._v(" "+e._s(e.$t("report.show.comprehensive.reportName"))+" "),a("div",{staticClass:"panel-heading-action"},[a("a",{ref:"panel-collapse",attrs:{href:"#"}},[a("i",{staticClass:"el-icon-minus"})])])]),a("div",{staticClass:"panel-widget"},[a("div",{staticClass:"panel-widget-body"},[a("el-form",{ref:"formTemplate",attrs:{model:e.query.comprehensive,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:e.$t("report.show.comprehensive.datetime"),prop:"assetType"}},[a("el-date-picker",{attrs:{type:"daterange",clearable:"","value-format":"yyyy-MM-dd",format:"yyyy-MM-dd","start-placeholder":e.$t("time.option.startDate"),"end-placeholder":e.$t("time.option.endTime"),size:"mini"},model:{value:e.query.comprehensive.datetime,callback:function(t){e.$set(e.query.comprehensive,"datetime",t)},expression:"query.comprehensive.datetime"}})],1)],1)],1),a("div",{staticClass:"panel-widget-footer"},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:function(t){return e.clickViewReport("comprehensive")}}},[e._v(" "+e._s(e.$t("button.query"))+" ")])],1)])])])],1),a("preview-dialog",{attrs:{visible:e.dialog.visible,title:e.dialog.title,width:"70%",url:e.dialog.url},on:{"update:visible":function(t){return e.$set(e.dialog,"visible",t)}}})],1)},n=[],r=(a("d81d"),a("d3b7"),a("25f0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("custom-dialog",{ref:"dialogTemp",attrs:{visible:e.visible,title:e.title,width:e.width},on:{"on-close":e.clickCancelDialog}},[a("html-frame",{attrs:{"frame-url":e.url,height:"400px"}}),a("template",{slot:"action"},[a("fragment")],1)],2)}),o=[],s=a("d465"),c=a("212a"),l={components:{CustomDialog:s["a"],HtmlFrame:c["a"]},props:{visible:{required:!0,type:Boolean},title:{type:String,default:""},width:{type:String,default:"600"},url:{type:String,default:""}},data:function(){return{dialogVisible:this.visible}},watch:{visible:function(e){this.dialogVisible=e},dialogVisible:function(e){this.$emit("update:visible",e)}},methods:{clickCancelDialog:function(){this.$refs.dialogTemp.end(),this.dialogVisible=!1}}},u=l,d=a("2877"),f=Object(d["a"])(u,r,o,!1,null,null,null),p=f.exports,m=a("7efe"),h=a("4020");function v(){return Object(h["a"])({url:"/reportshow/combo/types",method:"get"})}function g(e){return Object(h["a"])({url:"/reportshow/queryReport",method:"post",data:e||{}})}var y={name:"ReportShow",components:{PreviewDialog:p},data:function(){return{query:{asset:{assetType:[],datetime:""},comprehensive:{datetime:""}},rules:{},options:{assetType:[]},dialog:{visible:!1,title:this.$t("report.show.dialog.title"),url:""}}},computed:{defaultTimeRange:function(){var e=new Date,t=new Date(e);t.setDate(e.getDate()-30);var a=[Object(m["d"])(t.getTime(),"{y}-{m}-{d} 00:00:00"),Object(m["d"])(e.getTime(),"{y}-{m}-{d} 23:59:59")],i=a[0],n=a[1];return[i,n]}},mounted:function(){this.query.asset.datetime=this.defaultTimeRange,this.query.comprehensive.datetime=this.defaultTimeRange,this.initOptions()},methods:{initOptions:function(){var e=this;v().then((function(t){e.options.assetType=t}))},clickViewReport:function(e){var t=this.handleQueryParams(e);this.viewReport(t)},handleQueryParams:function(e){var t={};if("asset"===e){this.query.asset.datetime=this.query.asset.datetime||["",""];var a=this.query.asset.assetType;Array.isArray(a)&&(a=a.map((function(e){return e[1]})).toString()),t={reportType:e,assetType:a,startTime:this.query.asset.datetime[0],endTime:this.query.asset.datetime[1]}}return"comprehensive"===e&&(this.query.comprehensive.datetime=this.query.comprehensive.datetime||["",""],t={reportType:e,startTime:this.query.comprehensive.datetime[0],endTime:this.query.comprehensive.datetime[1]}),t},viewReport:function(e){var t=this;g(e).then((function(e){if(e){var a="";t.dialog.url=a+e,t.dialog.visible=!0}}))}}},b=y,w=(a("6583"),Object(d["a"])(b,i,n,!1,null,"8917ddb8",null));t["default"]=w.exports}}]);