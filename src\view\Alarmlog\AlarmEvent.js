import React, { Component, Fragment } from 'react'
import { Card, Alert, Button, Table, Pagination, Row, Col, Form, Select, DatePicker, Input, Modal, Tabs, message, Icon } from 'antd'
import { connect } from 'dva'
import online from '@/assets/IconFont/online.png'
import unonline from '@/assets/IconFont/unonline.png'
import locale from 'antd/es/date-picker/locale/zh_CN'
import ExportJsonExcel from 'js-export-excel'
import styles from './log.less'
import moment from 'moment'

const { Column } = Table
const Option = Select.Option
const FormItem = Form.Item
const { RangePicker } = DatePicker
const { TabPane } = Tabs

@Form.create()
@connect(({ alarmEvent, loading }) => ({
  alarmEvent,
  loading: loading.models.alarmEvent,
}))
export default class AlarmEvent extends Component {
  constructor(props) {
    super(props)
    this.state = {
      currentPage: 1,
      excelCurrPage: 1,
      currentPageSize: 10,
      firstPageData: [],
      dateDuration: 1,
      excelPrevCreateAt: '',
      excelPrevMaxId: 0,
      alarmEventList: [],
      selectedRowKeys: [],
      excelSelRows: [] /*用于手工选导出*/,
      searchValue: '',
      isChPageNum: false,
      startValue: '',
      endValue: '',
      prevCreateAt: '',
      prevMaxId: 0,
      authority: '',
      headerShow: false,
      singleChoice: true,
      newSelectedRowKeys: [],
      totalRecords: 0,
      currentSelNum: 0,
      curPageSelNum: 0,
      notSelectedRowKeys: [],
      activeKey: '0',
      status: '0',
      expand: false,
    }
  }

  componentWillUnmount() {
    const { dispatch } = this.props
    //清空状态管理数据
    dispatch({
      type: 'alarmEvent/clearData',
      payload: {},
    })
  }

  componentDidMount() {
    const { dispatch } = this.props
    let that = this
    dispatch({
      type: 'alarmEvent/getSystemDate',
      callback(res) {
        const { now_time } = res
        if (now_time) {
          that.setState(
            {
              searchValue: {
                query_time_from: moment(now_time)
                  .startOf('day')
                  .format('YYYY-MM-DD HH:mm:ss'),
                query_time_to: moment(now_time)
                  .endOf('day')
                  .format('YYYY-MM-DD HH:mm:ss'),
                time_from: moment(now_time)
                  .startOf('day')
                  .format('YYYY-MM-DD HH:mm:ss'),
                time_to: moment(now_time)
                  .endOf('day')
                  .format('YYYY-MM-DD HH:mm:ss'),
              },
              startValue: moment(now_time)
                .startOf('day')
                .format('YYYY-MM-DD HH:mm:ss'),
              endValue: moment(now_time)
                .endOf('day')
                .format('YYYY-MM-DD HH:mm:ss'),
            },
            () => {
              that.getAlarmLogList((res) => {
                //如果有数据更新第一页缓存。
                if (res.items.length != 0) {
                  that.setState(
                    {
                      firstPageData: res.items,
                      prevCreateAt: res.max_data_time,
                      prevMaxId: res.max_id,
                    },
                    () => {
                      that.getAllAlarmLogList()
                    }
                  )
                }
              }, null)
            }
          )
        }
      },
    })
  }

  /**
   *
   *
   */
  getDataForTotal = (newSearchValue, dateTimeList) => {
    const { dispatch } = this.props
    const { prevCreateAt, prevMaxId } = this.state
    if (dateTimeList.length == 0) return true

    let that = this
    let newVal = dateTimeList.pop()
    newSearchValue.time_from = newVal[0]
    newSearchValue.time_to = newVal[1]
    newSearchValue.max_id = prevMaxId
    newSearchValue.max_data_time = prevCreateAt
    // newSearchValue.query_time_from = newVal[0];
    // newSearchValue.query_time_to = newVal[1];
    dispatch({
      type: 'alarmEvent/getDataForCount',
      payload: {
        queryParams: newSearchValue,
      },
      callback: (res) => {
        if (res.code == 0) {
          //判断是否成功
          let { total } = res.data

          if (total) {
            that.setState({
              totalRecords: parseInt(that.state.totalRecords + parseInt(total)),
            })
          }
          that.getDataForTotal(newSearchValue, dateTimeList)
        } else {
          message.error(res.message)
        }
      },
    })
  }

  /**
   * 获取告警日志数据列表
   */
  getAlarmLogList = (callback, newSearchValue) => {
    const { dispatch } = this.props
    let { searchValue, currentPage, prevMaxId, prevCreateAt, endValue, currentPageSize, selectedRowKeys, curPageSelNum, isChPageNum } = this.state

    let copySearchValue = {}
    let resSearchValue = {}
    if (currentPage != 1) {
      if (prevMaxId != 0) {
        copySearchValue['max_id'] = prevMaxId
        copySearchValue['max_data_time'] = prevCreateAt
      }
      searchValue.query_time_to = endValue
      if (searchValue.export) {
        delete searchValue.export
      }
      if (prevCreateAt != '') {
        copySearchValue['time_to'] = prevCreateAt
      }
      resSearchValue = Object.assign(searchValue, copySearchValue)
    } else {
      if (!isChPageNum) {
        if (searchValue.max_id) {
          delete searchValue.max_id
          delete searchValue.max_data_time
        }
      }

      if (searchValue.export) {
        delete searchValue.export
      }
      copySearchValue['time_to'] = endValue
      resSearchValue = Object.assign(searchValue, copySearchValue)
      this.setState({
        isChPageNum: false,
      })
    }

    dispatch({
      type: 'alarmEvent/getData',
      payload: {
        page: currentPage,
        per_page: currentPageSize,
        queryParams: newSearchValue ? newSearchValue : resSearchValue,
      },
      callback: (res) => {
        if (res.code == 0) {
          if (res.data.items) {
            curPageSelNum = res.data.items.filter((item) => selectedRowKeys.find((value) => value == item.id)).length
            this.setState({
              alarmEventList: res.data.items,
              curPageSelNum,
            })
          }
          if (callback) callback(res.data)
        } else {
          message.error(res.message)
        }
      },
    })
  }

  /**
   * 获取告警日志总数
   * @param callback
   */
  getAlarmLogTotal = (callback) => {
    const { searchValue, prevCreateAt, prevMaxId } = this.state
    const { dispatch } = this.props
    const newSearchValue = Object.assign({}, searchValue)

    // newSearchValue.time_to = prevCreateAt;
    newSearchValue.max_id = prevMaxId
    newSearchValue.max_data_time = prevCreateAt
    dispatch({
      type: 'alarmEvent/getData',
      payload: {
        queryParams: newSearchValue,
      },
      callback: (res) => {
        if (res.code == 0) {
          const { total } = res.data
          if (total) {
            //判断是否成功
            this.setState({
              totalRecords: total,
            })
          } else {
            this.setState({
              totalRecords: 0,
            })
          }
          if (callback) callback(res.data)
        } else {
          message.error(res.message)
        }
      },
    })
  }

  /**
   *表格选中项发生变化时的回调
   */
  onSelectChange = (selectedRowKeys, selectedRows) => {
    let { singleChoice, newSelectedRowKeys, excelSelRows, selectedRowKeys: currentSelectedRowKeys } = this.state

    /*用于删除的数据*/
    selectedRows.forEach((value) => {
      let isExist = newSelectedRowKeys.every((item) => item.id != value.id)
      if (isExist) {
        newSelectedRowKeys.push({ id: value.id, created_at: value.created_at })
      }
    })

    /*用户excel导出的数据*/
    selectedRows.forEach((value) => {
      let isExist = excelSelRows.every((item) => item.id != value.id)
      if (isExist) {
        excelSelRows.push(value)
      }
    })

    this.setState({
      selectedRowKeys,
      newSelectedRowKeys /*设置新的选中行*/,
      excelSelRows: excelSelRows /*设置选中要导出的行*/,
      currentSelNum: selectedRowKeys.length /*设置当前手工选中数量*/,
      headerShow: singleChoice == false && currentSelectedRowKeys.length == 0 ? false : true,
    })
  }

  /**
   * 表格用户手动选择/取消选择所有行的回调
   */
  onSelectAll = (selected, selectedRows, changeRows) => {
    let ids = changeRows.map((item) => {
      return {
        id: item.id,
        created_at: item.created_at,
      }
    })
    let { notSelectedRowKeys, singleChoice, currentSelNum, headerShow, excelSelRows } = this.state

    /*获取未选中的id列表*/
    if (!singleChoice) {
      if (!selected) {
        /*反选操作*/

        this.setState(
          {
            notSelectedRowKeys: notSelectedRowKeys.concat(ids),
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum - changeRows.length,
            })
          }
        )
      } else {
        /*选中操作*/

        let res = notSelectedRowKeys
        ids.forEach((value) => {
          res = res.filter((item) => value.id !== item.id)
        })

        this.setState(
          {
            headerShow: selectedRows.length === changeRows.length ? true : headerShow,
            notSelectedRowKeys: res,
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum + changeRows.length,
            })
          }
        )
      }
    } else {
      if (!selected) {
        let newSelRow = excelSelRows.filter((item) => !changeRows.some((value) => value.id == item.id))
        this.setState({
          excelSelRows: newSelRow,
          newSelectedRowKeys: newSelRow.map((item) => {
            return {
              id: item.id,
              created_at: item.created_at,
            }
          }),
        })
      }
    }

    //设置本页选择多少页数量
    this.setState({
      curPageSelNum: selectedRows.length,
    })
  }

  /**
   * 表格用户手动选择/取消选择某行的回调
   * @param record
   * @param selected
   * @param selectedRows
   * @param nativeEvent
   */
  onSelect = (record, selected, selectedRows) => {
    let { notSelectedRowKeys, singleChoice, currentSelNum, excelSelRows } = this.state
    if (!singleChoice) {
      /*获取未选中的id列表*/
      if (!selected) {
        const { id, created_at } = record
        let newSelRow = excelSelRows.filter((item) => item.id != id)
        this.setState(
          {
            notSelectedRowKeys: notSelectedRowKeys.concat({ id: id, created_at }),
            excelSelRows: newSelRow,
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum - 1,
            })
          }
        )
      } else {
        const { id } = record
        this.setState(
          {
            notSelectedRowKeys: notSelectedRowKeys.filter((item) => item.id !== id),
          },
          () => {
            this.setState({
              currentSelNum: currentSelNum + 1,
            })
          }
        )
      }
    } else {
      if (!selected) {
        const { id } = record
        let newSelRow = excelSelRows.filter((item) => item.id != id)
        this.setState({
          excelSelRows: newSelRow,
          newSelectedRowKeys: newSelRow.map((item) => {
            return {
              id: item.id,
              created_at: item.created_at,
            }
          }),
        })
      }
    }

    //设置本页选择数量
    this.setState({
      curPageSelNum: selectedRows.length,
    })
  }

  /**
   * 删除告警日志处理
   */
  handleDeleteClick = () => {
    const { dispatch } = this.props
    const { newSelectedRowKeys, notSelectedRowKeys, currentSelNum, singleChoice, searchValue } = this.state
    const that = this
    if (currentSelNum) {
      Modal.confirm({
        title: '删除',
        content: `确认删除这${currentSelNum}条数据吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          if (!that.state.singleChoice) {
            let params = {
              queryParams: searchValue,
            }
            /*判断是否全选 全选不传此参数，不全选传此参数*/
            if (notSelectedRowKeys.length !== 0) {
              params.device_log_not_selected_id_created_at_list = notSelectedRowKeys
            }

            dispatch({
              type: 'alarmEvent/DeleteDataByNotSelected',
              payload: params,
              callback: (res) => {
                let result = JSON.parse(res)

                if (result.code == 0) {
                  that.setState({
                    currentPage: 1,
                    headerShow: false,
                    notSelectedRowKeys: [],
                    excelSelRows: [],
                    selectedRowKeys: [],
                    newSelectedRowKeys: [],
                    currentSelNum: 0,
                    singleChoice: true,
                  })
                  message.success('删除成功')
                  that.getAlarmLogList((res) => {
                    //如果有数据更新第一页缓存。
                    if (res.items) {
                      that.setState({
                        firstPageData: res.items,
                      })
                    }
                    that.getAllAlarmLogList()
                  }, null)
                } else {
                  message.error(res.message)
                }
              },
            })
          } else {
            dispatch({
              type: 'alarmEvent/DeleteData',
              payload: {
                device_log_id_created_at_list: singleChoice == false ? '' : newSelectedRowKeys,
                queryParams: that.state.searchValue,
              },
              callback: (res) => {
                if (res.code == 0) {
                  that.setState({
                    currentPage: 1,
                    headerShow: false,
                    selectedRowKeys: [],
                    newSelectedRowKeys: [],
                    excelSelRows: [],
                    notSelectedRowKeys: [],
                    currentSelNum: 0,
                    singleChoice: true,
                  })
                  message.success('删除成功')
                  that.getAlarmLogList((res) => {
                    //如果有数据更新第一页缓存。
                    if (res.items) {
                      that.setState({
                        firstPageData: res.items,
                      })
                    }
                    that.getAllAlarmLogList()
                  }, null)
                } else {
                  message.error(res.message)
                }
              },
            })
          }
        },
      })
    } else {
      message.error('至少选中一条数据')
    }
  }

  /**
   * 添加账户模态框
   * @param modal
   * @constructor
   */
  AddModalBoxRef = (modal) => {
    this.addAccountModal = modal
  }

  /**
   * 导出excel表格
   */
  downloadExcel = () => {
    const { currentSelNum, notSelectedRowKeys, singleChoice, selectedRowKeys, excelSelRows } = this.state
    if (currentSelNum) {
      if (notSelectedRowKeys.length == 0 && singleChoice == true) {
        //部分导出
        let totalPageNum = Math.ceil(selectedRowKeys.length / 500)
        for (let i = 0; i < totalPageNum; i++) {
          let newExecelRows = excelSelRows.splice(0, 500)
          this.successDown(newExecelRows)
        }
        this.exportExcelSaveLog()
      } else {
        //全部导出
        this.getInitList()
      }

      this.setState({
        excelSelRows: [],
        selectedRowKeys: [],
        notSelectedRowKeys: [],
        newSelectedRowKeys: [],
        headerShow: false,
        currentSelNum: 0,
      })
    } else {
      message.error('至少选中一条数据')
    }
  }

  /**
   * 导出操作保存日志
   */
  exportExcelSaveLog = () => {
    const { dispatch } = this.props
    let { searchValue, endValue } = this.state

    let copySearchValue = {}
    copySearchValue['time_to'] = endValue
    copySearchValue['export'] = 1
    let resSearchValue = Object.assign(searchValue, copySearchValue)

    dispatch({
      type: 'alarmEvent/getData',
      payload: {
        _limit: 1,
        queryParams: resSearchValue,
      },
      callback: (res) => {
        if (res.code == 0) {
        } else {
          message.error(res.message)
        }
      },
    })
  }

  /**
   * 导出全部日志
   */
  getInitList = () => {
    const { dispatch } = this.props
    const { prevCreateAt, prevMaxId, searchValue, excelCurrPage, firstPageData } = this.state
    let copySearchValue = {}
    let resSearchValue = {}
    let pageSize = 500
    if (excelCurrPage != 1) {
      if (prevMaxId != 0) {
        copySearchValue['max_id'] = prevMaxId
      }
      if (prevCreateAt != '') {
        copySearchValue['time_to'] = prevCreateAt
      }
      resSearchValue = Object.assign(searchValue, copySearchValue)
      pageSize = 500
    } else {
      if (prevMaxId != 0) {
        copySearchValue['max_id'] = prevMaxId
      }
      if (searchValue.max_id) {
        delete searchValue.max_id
      }
      if (searchValue.export) {
        delete searchValue.export
      }
      if (prevCreateAt != '') {
        copySearchValue['time_to'] = prevCreateAt
        pageSize = 500 - firstPageData.length
      }
      resSearchValue = Object.assign(searchValue, copySearchValue)
    }

    dispatch({
      type: 'alarmEvent/getChoiceTimeData',
      payload: {
        per_page: pageSize,
        page: excelCurrPage,
        queryParams: resSearchValue,
      },
      callback: (res) => {
        let totalPageNum = Math.ceil(this.state.totalRecords / 500)

        if (totalPageNum == excelCurrPage) {
          this.setState({
            visible: false,
            excelCurrPage: 1,
            excelPrevCreateAt: '',
            excelPrevMaxId: 0,
            singleChoice: true,
          })
          this.successDown()
          this.exportExcelSaveLog()
          return
        }

        let isLast = Math.ceil(res.data.items.length / 500)

        if (res.data.items.length == 0) {
          message.error('该时间段内没有日志')
          this.setState({
            visible: true,
          })
        } else if (isLast == 1) {
          let { length } = res.data.items
          this.setState(
            {
              visible: false,
              excelCurrPage: excelCurrPage + 1,
              excelPrevCreateAt: excelCurrPage != 1 ? res.data.items[length - 1]['created_at'] : prevCreateAt,
              excelPrevMaxId: excelCurrPage != 1 ? res.data.items[length - 1]['id'] : prevMaxId,
            },
            () => {
              this.getInitList()
              this.successDown()
            }
          )
        }
      },
    })
  }

  /**
   * 部分导出excel
   */
  successDown = (manualSelRows) => {
    const {
      alarmEvent: { deviceTimeLogData },
    } = this.props
    let { selectedRowKeys, singleChoice, notSelectedRowKeys, firstPageData, excelCurrPage } = this.state
    let deviceLogList = []
    if (selectedRowKeys.length > 0 && singleChoice == true) {
      deviceLogList = manualSelRows
    } else if (notSelectedRowKeys.length !== 0) {
      let res = deviceTimeLogData.items
      notSelectedRowKeys.forEach((value) => {
        res = res.filter((item) => item.id != value.id)
      })
      deviceLogList = res
    } else {
      if (excelCurrPage == 1) {
        deviceLogList = firstPageData.concat(deviceTimeLogData.items)
      } else {
        deviceLogList = deviceTimeLogData.items
      }
    }

    let option = {}
    let dataTable = []
    if (deviceLogList) {
      for (let i = 0; i < deviceLogList.length; i++) {
        let obj
        obj = {
          序号: i == 0 ? 1 : Number(i) + 1,
          日期: deviceLogList[i].created_at,
          设备类型: deviceLogList[i].device_category_text,
          设备IP: deviceLogList[i].device_ip,
          协议: deviceLogList[i].protocol,
          详情: deviceLogList[i].device_status == 1 ? '设备上线' : '设备下线',
          事件名称: deviceLogList[i].log_category_cn,
          事件类型: deviceLogList[i].log_category_cn,
          事件级别: deviceLogList[i].level_text,
          事件描述: deviceLogList[i].detail,
          发生时间: deviceLogList[i].log_time,
          事件特征: deviceLogList[i].log_category_cn,
        }
        dataTable.push(obj)
      }
    }
    option.fileName = moment().format('YYYY-MM-DD HH:mm:ss') + '告警日志'
    option.datas = [
      {
        sheetData: dataTable,
        sheetName: 'sheet',
        sheetFilter: ['序号', '日期', '设备类型', '设备IP', '协议', '详情', '事件名称', '事件类型', '事件级别', '事件描述', '发生时间', '事件特征'],
        sheetHeader: ['序号', '日期', '设备类型', '设备IP', '协议', '详情', '事件名称', '事件类型', '事件级别', '事件描述', '发生时间', '事件特征'],
      },
    ]

    let toExcel = new ExportJsonExcel(option)
    toExcel.saveExcel()
  }

  /**
   * 禁用日期设置
   * @param current
   * @returns {*|boolean}
   */
  disabledDate = (current) => {
    var oldTime = new Date('1970/1/1 00:00:01').getTime()
    return current && current < oldTime
  }

  /**
   * 时间改变处理
   * @param field
   * @param value
   */
  onChange = (field, value) => {
    this.setState({
      [field]: value,
    })
  }

  /**
   * 页面改变事件处理
   * @param pageNumber
   */
  handlePageChange = (pageNumber) => {
    const { firstPageData, singleChoice, selectedRowKeys } = this.state
    let that = this
    if (singleChoice) {
      this.setState(
        {
          currentPage: pageNumber,
        },
        () => {
          if (pageNumber != 1) {
            that.getAlarmLogList()
          } else {
            that.setState({
              alarmEventList: firstPageData,
            })
          }
        }
      )
    } else {
      this.setState(
        {
          currentPage: pageNumber,
        },
        () => {
          if (pageNumber != 1) {
            that.getAlarmLogList((res) => {
              that.setState({
                selectedRowKeys: [...new Set(selectedRowKeys.concat(res.items.map((item) => item.id)))],
              })
            })
          } else {
            that.setState({
              alarmEventList: firstPageData,
              selectedRowKeys: [...new Set(selectedRowKeys.concat(firstPageData.map((item) => item.id)))],
            })
          }
        }
      )
    }
  }

  /**
   * 分页大小改变事件处理
   * @param current
   * @param pageSize
   */
  onShowSizeChange = (current, pageSize) => {
    const { selectedRowKeys, singleChoice } = this.state

    let that = this
    if (singleChoice) {
      this.setState(
        {
          currentPage: 1,
          currentPageSize: pageSize,
          isChPageNum: true,
        },
        () => {
          that.getAlarmLogList((res) => {
            //如果有数据更新第一页缓存。
            if (res.items.length != 0) {
              that.setState({
                firstPageData: res.items,
              })
            }
          }, null)
        }
      )
    } else {
      this.setState(
        {
          currentPage: 1,
          currentPageSize: pageSize,
          isChPageNum: true,
        },
        () => {
          that.getAlarmLogList((res) => {
            //如果有数据更新第一页缓存。
            if (res.items.length != 0) {
              let curSelRowKeys = selectedRowKeys.concat(res.items.map((item) => item.id))
              that.setState({
                firstPageData: res.items,
                selectedRowKeys: [...new Set(curSelRowKeys)],
              })
            }
          }, null)
        }
      )
    }
  }

  /**
   * 显示数据总数
   * @param total
   * @returns {string}
   */
  showTotal = (total) => {
    //获取总数状态
    const { totalRecords } = this.state
    return `总数据${totalRecords}条`
  }

  /**
   * 获取所有告警日志列表方法，含有多天轮询请求逻辑
   */
  getAllAlarmLogList = () => {
    const { searchValue, dateDuration } = this.state
    let that = this

    //判断是否时间间隔大于一天，大于一天启用websocket方式获取总记录数，否则按原有总记录数计算规则
    if (dateDuration <= 1) {
      that.getAlarmLogTotal(null)
    } else {
      //获取天分割的日期时间范围数组
      let dateTimeList = that.convertDateTimeArr(that.state.startValue, that.state.endValue, dateDuration)
      //调用第一个时间间隔查询并渲染数据
      that.setState(
        {
          totalRecords: 0,
        },
        async () => {
          that.getDataForTotal(searchValue, dateTimeList)
        }
      )
    }
  }

  /**
   * 搜索条件处理
   */
  handleAdvancedSearch = () => {
    const { form } = this.props
    let { searchValue, startValue, endValue } = this.state
    let that = this
    //定义间隔条件
    let duration = 1

    form.validateFields((err, values) => {
      if (!err) {
        if (startValue) {
          searchValue.query_time_from = startValue
          searchValue.query_time_to = endValue
          searchValue.time_from = startValue
          searchValue.time_to = endValue
          duration = parseInt(moment(endValue, 'YYYY-MM-DD HH:mm:ss').diff(moment(startValue, 'YYYY-MM-DD HH:mm:ss'), 'days')) + 1
        } else {
          message.error('请选择起止日期！')
          return
        }

        searchValue.category = values.protocol
        searchValue.ip = values.originIp ? values.originIp : ''
        searchValue.detail = values.detail ? values.detail : ''

        this.setState(
          {
            searchValue: searchValue,
            currentPage: 1,
            headerShow: false,
            totalRecords: 0,
            alarmEventList: [],
            dateDuration: duration,
          },
          () => {
            that.getAlarmLogList((res) => {
              if (res.items.length != 0) {
                that.setState(
                  {
                    firstPageData: res.items,
                    prevCreateAt: res.max_data_time,
                    prevMaxId: res.max_id,
                  },
                  () => {
                    that.getAllAlarmLogList()
                  }
                )
              }
            }, searchValue)
          }
        )
      }
    })
  }

  handleReset = () => {
    const { resetFields } = this.props.form
    resetFields()
  }
  /**
   * 将开始结束时间范围转化为以天为单位的日期时间范围数组
   * @param startDateTime
   * @param endDateTime
   */
  convertDateTimeArr = (startDateTime, endDateTime, duration) => {
    let dateTimeList = []
    //计算开始天范围
    dateTimeList.push([
      startDateTime,
      moment(startDateTime)
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss'),
    ])
    //生成当前天日期
    let curDate = moment(startDateTime).add(1, 'day')
    for (let i = 1; i < duration - 1; i++) {
      dateTimeList.push([
        moment(curDate)
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
        moment(curDate)
          .endOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      ])
      curDate = moment(curDate).add(1, 'day')
    }
    //计算结束天范围
    dateTimeList.push([
      moment(endDateTime)
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss'),
      endDateTime,
    ])
    return dateTimeList
  }

  /**
   * 时期范围变化处理
   * @param dates
   * @param dateStrings
   */
  onRangChange = (dates, dateStrings) => {
    this.setState({
      startValue: dateStrings[0],
      endValue: dateStrings[1],
    })
  }

  /**
   * 全选取消全选事件处理
   * */
  cleanSelectedKeys = (flag) => {
    const { newSelectedRowKeys, totalRecords, notSelectedRowKeys, alarmEventList } = this.state

    /*判断当前页选中一个点击选择全部情况*/
    let nextSelectedRowKeys = newSelectedRowKeys.length == totalRecords ? newSelectedRowKeys : alarmEventList.map((item) => item.id)
    this.setState({
      singleChoice: flag ? false : true,
      headerShow: !flag ? false : true,
      notSelectedRowKeys: flag ? notSelectedRowKeys : [],
      selectedRowKeys: flag ? nextSelectedRowKeys : [],
      currentSelNum: flag ? totalRecords - notSelectedRowKeys.length : 0,
    })
  }

  handleTab = (key) => {}

  onEdit = (targetKey, action) => {
    this[action](targetKey)
  }

  handleSwitch = (status) => {
    this.setState({ status })
  }

  onTabChanged = (key) => {
    this.setState({ tabKey: key })
    this.props.history.replace({
      pathname: '/my/url/' + key,
      state: {
        tabKey: key,
      },
    })
  }

  render() {
    const { getFieldDecorator } = this.props.form
    const { loading } = this.props
    let {
      selectedRowKeys,
      currentPage,
      currentPageSize,
      headerShow,
      singleChoice,
      notSelectedRowKeys,
      currentSelNum,
      startValue,
      endValue,
      curPageSelNum,
      totalRecords,
      alarmEventList,
      status,
      activeKey,
    } = this.state
    if (alarmEventList) {
      for (var i = 0; i < alarmEventList.length; i++) {
        alarmEventList[i].eventName = alarmEventList[i].log_category_cn
        alarmEventList[i].eventFeature = alarmEventList[i].log_category_cn
        alarmEventList[i]['number'] = (currentPage - 1) * currentPageSize + i + 1
      }
    }

    /*第一页本页选中数量重新计算*/
    curPageSelNum = alarmEventList.filter((item) => selectedRowKeys.find((value) => value == item.id)).length

    //过滤未选中id列表
    let curSelectedRowKeys =
      notSelectedRowKeys.length != 0 ? selectedRowKeys.filter((item) => !notSelectedRowKeys.some((value) => value.id == item)) : selectedRowKeys
    //初始化选择表格属性
    const rowSelection = {
      selectedRowKeys: typeof curSelectedRowKeys[0] === 'number' ? curSelectedRowKeys : curSelectedRowKeys.map((item) => item.id),
      onChange: this.onSelectChange,
      onSelectAll: this.onSelectAll,
      onSelect: this.onSelect,
    }
    return (
      <Fragment>
        <div className={styles.title}>告警日志</div>
        <Card style={{ borderRadius: 8 }} bordered={false}>
          <Form className="searchBg" labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
            <Row>
              <Col span={8}>
                <FormItem label="起止日期" style={{ marginBottom: 15 }}>
                  {getFieldDecorator('dateRange', {
                    rules: [
                      {
                        required: true,
                        message: '请选择起止日期',
                      },
                    ],
                    initialValue: [moment(startValue, 'YYYY-MM-DD HH:mm:ss'), moment(endValue, 'YYYY-MM-DD HH:mm:ss')],
                  })(
                    <RangePicker
                      disabledDate={this.disabledDate}
                      showTime
                      locale={locale}
                      format="YYYY-MM-DD HH:mm:ss"
                      onChange={this.onRangChange}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="设备类型" colon={true}>
                  {getFieldDecorator('protocol', {
                    initialValue: '',
                  })(
                    <Select>
                      <Option value="">全部</Option>
                      <Option value="1">工业防火墙</Option>
                      <Option value="2">工控监测审计</Option>
                      <Option value="4">哨兵系统</Option>
                    </Select>
                  )}
                </FormItem>
              </Col>
              {this.state.expand ? (
                <>
                  <Col span={8}>
                    <FormItem label="设备IP">
                      {getFieldDecorator('originIp', {
                        rules: [
                          {
                            pattern: /^[\u4e00-\u9fa5A-Za-z0-9\-\_\.]*$/,
                            message: '请输入汉字、字母、数字、短横线、点或下划线',
                          },
                        ],
                      })(<Input maxLength={50} placeholder="请输入设备IP" />)}
                    </FormItem>
                  </Col>
                  <Col span={8}>
                    <FormItem label="事件描述">{getFieldDecorator('detail')(<Input maxLength={50} placeholder="请输入事件描述" />)}</FormItem>
                  </Col>
                </>
              ) : null}
              <Col span={8} className="searchBtn">
                <Button type="primary" loading={loading} onClick={this.handleAdvancedSearch}>
                  查询
                </Button>
                <Button style={{ marginLeft: 15 }} onClick={this.handleReset}>
                  清除
                </Button>
                <a
                  className="searchOper"
                  onClick={() => {
                    this.setState({
                      expand: !this.state.expand,
                    })
                  }}
                >
                  {this.state.expand ? (
                    <>
                      隐藏 <Icon type="down" />
                    </>
                  ) : (
                    <>
                      展开 <Icon type="up" />
                    </>
                  )}
                </a>
              </Col>
            </Row>
          </Form>
        </Card>
        <div style={{ marginBottom: 20, marginTop: 20 }}>
          <Button type="primary" style={{ marginRight: 8 }} onClick={this.downloadExcel}>
            导出日志
          </Button>
        </div>
        <Card bordered={false} style={{ borderRadius: 8 }} className="TableContainer">
          <div className={styles.standardTable} style={{ display: (headerShow && curPageSelNum > 0) || !singleChoice ? '' : 'none' }}>
            <div className={styles.tableAlert} style={{ display: singleChoice ? '' : 'none' }}>
              <Alert
                message={
                  <Fragment>
                    已选择本页<a style={{ fontWeight: 600 }}>{curPageSelNum}</a> 项，
                    <a
                      onClick={() => {
                        this.cleanSelectedKeys(true)
                      }}
                      style={{ marginLeft: 0 }}
                    >
                      选择全部{totalRecords}项目
                    </a>
                  </Fragment>
                }
                type="info"
                showIcon
              />
            </div>
            <div className={styles.tableAlert} style={{ display: singleChoice ? 'none' : '' }}>
              <Alert
                message={
                  <Fragment>
                    已选择{singleChoice == false && notSelectedRowKeys.length == 0 ? '全部' : ''} <a style={{ fontWeight: 600 }}>{currentSelNum}</a>{' '}
                    项，
                    <a
                      onClick={() => {
                        this.cleanSelectedKeys(false)
                      }}
                      style={{ marginLeft: 0 }}
                    >
                      取消选择
                    </a>
                  </Fragment>
                }
                type="info"
                showIcon
              />
            </div>
          </div>
          <Table
            rowSelection={rowSelection}
            className={styles.tableHeader}
            dataSource={alarmEventList}
            loading={loading}
            rowKey={(record) => record.id}
            pagination={false}
            locale={{ emptyText: '暂无数据信息' }}
            scroll={{ x: 1500 }}
          >
            <Column title="序号" dataIndex="number" key="number" width={50} />
            <Column title="日期" dataIndex="created_at" key="created_at" width={140} />
            <Column title="设备类型" dataIndex="device_category_text" key="device_category_text" width={80} />
            <Column title="设备IP" dataIndex="device_ip" key="device_ip" width={80} />
            <Column title="协议" dataIndex="protocol" key="protocol" width={50} />
            <Column title="事件名称" dataIndex="eventName" key="eventName" width={70} />
            <Column title="事件类型" dataIndex="log_category_cn" key="log_category_cn" width={70} />
            <Column title="事件级别" dataIndex="level_text" key="level_text" width={70} />
            <Column title="事件描述" dataIndex="detail" key="detail" width={250} />
            <Column title="发生时间" dataIndex="log_time" key="log_time" width={140} />
            <Column title="事件特征" dataIndex="eventFeature" key="eventFeature" width={70} />
          </Table>
        </Card>
        <Row type="flex" justify="end">
          <Pagination
            current={currentPage}
            showQuickJumper
            showSizeChanger
            onShowSizeChange={this.onShowSizeChange}
            total={totalRecords}
            showTotal={this.showTotal}
            onChange={this.handlePageChange}
          />
        </Row>
      </Fragment>
    )
  }
}
