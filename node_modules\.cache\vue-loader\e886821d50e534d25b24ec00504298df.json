{"remainingRequest": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\workspace\\smp\\src\\view\\AuditoldVue\\StrategyCollection\\components\\addStrstegyCollection.vue", "mtime": 1750388182601}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745219674875}, {"path": "D:\\workspace\\smp\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745219675074}, {"path": "D:\\workspace\\smp\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745219693993}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}