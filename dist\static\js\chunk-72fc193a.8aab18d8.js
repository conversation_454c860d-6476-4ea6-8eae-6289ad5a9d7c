(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72fc193a"],{"273c":function(e,t,a){"use strict";var r=a("af68"),n=a.n(r);n.a},"2ca0":function(e,t,a){"use strict";var r=a("23e7"),n=a("06cf").f,o=a("50c4"),c=a("5a34"),i=a("1d80"),s=a("ab13"),u=a("c430"),l="".startsWith,d=Math.min,p=s("startsWith"),f=!u&&!p&&!!function(){var e=n(String.prototype,"startsWith");return e&&!e.writable}();r({target:"String",proto:!0,forced:!f&&!p},{startsWith:function(e){var t=String(i(this));c(e);var a=o(d(arguments.length>1?arguments[1]:void 0,t.length)),r=String(e);return l?l.call(t,r,a):t.slice(a,a+r.length)===r}})},"5a34":function(e,t,a){var r=a("44e7");e.exports=function(e){if(r(e))throw TypeError("The method doesn't accept regular expressions");return e}},"932f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"backup-container"},[a("div",{staticClass:"backup-content"},[a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"135px"}},[a("el-form-item",{attrs:{label:"备份配置方式",prop:"backup_mode"}},[a("el-radio-group",{model:{value:e.form.backup_mode,callback:function(t){e.$set(e.form,"backup_mode",t)},expression:"form.backup_mode"}},[a("el-radio",{attrs:{label:1}},[e._v("手动配置")]),a("el-radio",{attrs:{label:2}},[e._v("自动配置")])],1)],1),a("el-form-item",{attrs:{label:"时间",prop:"retain_months"}},[a("el-input-number",{attrs:{size:"small",min:1,max:120,precision:0},model:{value:e.form.retain_months,callback:function(t){e.$set(e.form,"retain_months",t)},expression:"form.retain_months"}}),a("span",{staticClass:"month-text"},[e._v("个月")])],1),a("el-form-item",{attrs:{label:"安全审计",prop:"audit_tables"}},[a("el-checkbox-group",{model:{value:e.form.audit_tables,callback:function(t){e.$set(e.form,"audit_tables",t)},expression:"form.audit_tables"}},e._l(e.options,(function(t){return a("el-checkbox",{key:t.value,attrs:{label:t.value}},[e._v(" "+e._s(t.label)+" ")])})),1)],1),a("el-form-item",{attrs:{label:" "}},[a("el-button",{staticClass:"cancel-button",on:{click:e.handleCancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSave}},[e._v("应用")])],1)],1)],1)])])},n=[],o=(a("ac1f"),a("1276"),a("96cf"),a("c964")),c=a("ee97");function i(e){return Object(c["a"])({url:"/api/ieg/v1/backup_audit_log/set_config",method:"post",data:e})}function s(){return Object(c["a"])({url:"/api/ieg/v1/backup_audit_log/info",method:"get"})}var u=a("d2c9"),l=[{label:"终端操作审计",value:"audit_client_log"},{label:"白名单审计",value:"audit_white_list"},{label:"外设管控审计",value:"audit_peripheral"},{label:"文件防护审计",value:"audit_file_protect"},{label:"注册表防护审计",value:"audit_registry"},{label:"网络防护审计",value:"audit_network"},{label:"进程防护审计",value:"audit_process_safe"},{label:"违规外联审计",value:"audit_external_link"},{label:"主机健康审计",value:"audit_host_health"}],d={name:"BackupRecovery",data:function(){return{form:{backup_mode:1,audit_tables:[],retain_months:1},options:l,rules:{audit_tables:[{required:!0,message:"请选择审计列表",trigger:"change"}]},valid:!1}},watch:{"form.backup_mode":{handler:function(){var e=this;this.$refs.form&&this.$nextTick((function(){e.$refs.form.validate((function(t){e.valid=t})),e.$refs.form.clearValidate()}))}},form:{deep:!0,handler:function(){var e=this;this.$refs.form&&(this.$refs.form.validate((function(t){e.valid=t})),this.$refs.form.clearValidate())}}},mounted:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(u["a"])();case 2:e.getInfo();case 3:case"end":return t.stop()}}),t)})))()},methods:{getInfo:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,s();case 3:a=t.sent,0===a.code&&(e.form={backup_mode:a.data.backup_mode,audit_tables:a.data.audit_tables.split(","),retain_months:a.data.retain_months}),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取备份信息失败:",t.t0),e.$message.error("获取备份信息失败");case 11:case"end":return t.stop()}}),t,null,[[0,7]])})))()},handleSave:function(){var e=this;this.$refs.form.validate((function(t){t&&e.$confirm("确定应用本次备份吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",center:!0}).then(Object(o["a"])(regeneratorRuntime.mark((function t(){var a,r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={backup_mode:e.form.backup_mode,retain_months:e.form.retain_months,audit_tables:e.form.audit_tables},t.next=4,i(a);case 4:r=t.sent,0===r.code&&(e.$message.success(r.msg||"保存成功"),e.valid=!1),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("保存备份配置失败:",t.t0),e.$message.error("保存备份配置失败");case 12:case"end":return t.stop()}}),t,null,[[0,8]])})))).catch((function(){}))}))},handleCancel:function(){var e=this;this.$confirm("确定取消本次备份吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",center:!0}).then((function(){e.getInfo()})).catch((function(){}))}}},p=d,f=(a("273c"),a("2877")),m=Object(f["a"])(p,r,n,!1,null,"533ab696",null);t["default"]=m.exports},ab13:function(e,t,a){var r=a("b622"),n=r("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(a){try{return t[n]=!1,"/./"[e](t)}catch(r){}}return!1}},af68:function(e,t,a){},bae8:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o}));var r=a("ee97");function n(e){return Object(r["a"])({url:"/api/ieg/v1/login/login_in",method:"post",data:e})}function o(){return Object(r["a"])({url:"/api/ieg/v1/system/info",method:"get"})}},d2c9:function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("d3b7"),a("25f0"),a("96cf");var r=a("c964"),n=a("bae8"),o=6e5;function c(){var e=localStorage.getItem("hg_token_timestamp");if(!e)return!1;var t=(new Date).getTime(),a=parseInt(e),r=t-a;return r<o}function i(){return s.apply(this,arguments)}function s(){return s=Object(r["a"])(regeneratorRuntime.mark((function e(){var t,a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=localStorage.getItem("hg_token"),!t){e.next=7;break}if(a=c(),!a){e.next=6;break}return console.log("本地token有效，无需重新登录"),e.abrupt("return",t);case 6:console.log("本地token已过期，需要重新登录");case 7:return e.prev=7,e.next=10,Object(n["b"])({name:"admin",password:"123456"});case 10:if(r=e.sent,!(r&&0===r.code&&r.data&&r.data.token)){e.next=18;break}return console.log("隐式登录成功，获取到新token"),localStorage.setItem("hg_token",r.data.token),localStorage.setItem("hg_token_timestamp",(new Date).getTime().toString()),e.abrupt("return",r.data.token);case 18:return console.error("隐式登录失败:",r),e.abrupt("return","");case 20:e.next=26;break;case 22:return e.prev=22,e.t0=e["catch"](7),console.error("隐式登录出错:",e.t0),e.abrupt("return","");case 26:case"end":return e.stop()}}),e,null,[[7,22]])}))),s.apply(this,arguments)}},ee97:function(e,t,a){"use strict";a("99af"),a("c975"),a("a9e3"),a("d3b7"),a("ac1f"),a("5319"),a("2ca0");var r=a("bc3a"),n=a.n(r),o=a("4360"),c=a("a18c"),i=a("a47e"),s=a("f7b5"),u=a("f907"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",r=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),l=r.NODE_ENV,d=r.VUE_APP_IS_MOCK,p=r.VUE_APP_BASE_API,f="true"===d?"":p;"production"===l&&(f="");var m={baseURL:f,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===l&&(m.timeout=a),t){case"upload":m.headers["Content-Type"]="multipart/form-data",m["processData"]=!1,m["contentType"]=!1;break;case"download":m["responseType"]="blob";break;case"eventSource":break;default:break}var b=n.a.create(m);return b.interceptors.request.use((function(e){var t=o["a"].getters.token;if(""!==t&&e.url.startsWith("/api/ieg/")){var a=localStorage.getItem("hg_token");a&&(e.headers["authtoken"]=a)}return e}),(function(e){Object(s["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:e,print:!0}),Promise.reject("response-err:"+e)})),b.interceptors.response.use((function(e){var a=void 0===e.headers["code"]?200:Number(e.headers["code"]),r=function(){Object(s["a"])({i18nCode:"logout.message",type:"error"},(function(){c["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),c["a"].replace({path:"/login"}))}))},n=function(){var t=function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",r=arguments.length>2?arguments[2]:void 0,n="";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n="error"),e.data.code>=2e3&&e.data.code<3e3&&(n="warning"),Object(s["a"])({i18nCode:"ajax.".concat(a,".").concat(t),type:n}),Promise.reject("response-err-status:".concat(r||u["a"][a][t]," \nerr-question: ").concat(i["a"].t("ajax.".concat(a,".").concat(t))))};switch(e.data.code){case u["a"].exception.system:t("system");break;case u["a"].exception.server:t("server");break;case u["a"].exception.session:r();break;case u["a"].exception.access:r();break;case u["a"].exception.certification:t("certification");break;case u["a"].exception.auth:t("auth"),c["a"].replace({path:"/401"});break;case u["a"].exception.token:t("token");break;case u["a"].exception.param:t("param");break;case u["a"].exception.idempotency:t("idempotency");break;case u["a"].exception.ip:t("ip"),o["a"].dispatch("user/reset"),c["a"].replace({path:"/login"});break;case u["a"].exception.upload:t("upload");break;case u["a"].attack.xss:t("xss","attack");break;default:t("code","exception",-1);break}};switch(t){case"upload":if(0===a)return e.data.data;n();break;case"download":if(0===a)return{data:e.data,fileName:decodeURI(e.headers["file-name"])};n();break;default:if(0===e.data.code)return e.data;n();break}}),(function(e){var a=function(){Object(s["a"])({i18nCode:"logout.message",type:"error"},(function(){c["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),c["a"].replace({path:"/login"}))}))};return"upload"===t?(Object(s["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),e.response&&403==e.response.status&&a(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(i["a"].t("ajax.service.upload")))):(Object(s["a"])({i18nCode:"ajax.service.timeout",type:"error"}),e.response&&403==e.response.status&&a(),Promise.reject("response-err-status:".concat(e," \nerr-question: ").concat(i["a"].t("ajax.service.timeout"))))})),b(e)};t["a"]=l}}]);