(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-75fd107c"],{"2ca0":function(e,t,r){"use strict";var a=r("23e7"),n=r("06cf").f,o=r("50c4"),i=r("5a34"),s=r("1d80"),c=r("ab13"),u=r("c430"),l="".startsWith,p=Math.min,d=c("startsWith"),m=!u&&!d&&!!function(){var e=n(String.prototype,"startsWith");return e&&!e.writable}();a({target:"String",proto:!0,forced:!m&&!d},{startsWith:function(e){var t=String(s(this));i(e);var r=o(p(arguments.length>1?arguments[1]:void 0,t.length)),a=String(e);return l?l.call(t,a,r):t.slice(r,r+a.length)===a}})},"39e2":function(e,t,r){"use strict";var a=r("8c3b5"),n=r.n(a);n.a},"5a34":function(e,t,r){var a=r("44e7");e.exports=function(e){if(a(e))throw TypeError("The method doesn't accept regular expressions");return e}},"8c3b5":function(e,t,r){},a4cc:function(e,t,r){"use strict";var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"terminal-groups"},[r("div",{staticClass:"terminal-groups-sidebar"},[r("div",{staticClass:"terminal-groups-header"},[r("span",[e._v("终端分组")]),e.isEdit?r("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v(" 新增 ")]):e._e()],1),r("div",{staticClass:"terminal-groups-tree"},[r("div",[r("div",{staticClass:"terminal-groups-expand"},[r("i",{staticClass:"cursor-pointer",class:e.expand?"el-icon-arrow-up":"el-icon-arrow-down",on:{click:function(t){e.expand=!e.expand}}}),r("span",{class:["terminal-groups-toper","cursor-pointer",-1===e.cursorId?"cursor-text-primary":""],on:{click:function(t){return e.changeTerminalBlock(-1,"全部终端")}}},[e._v(" 全部终端（"+e._s(e.total)+"） ")])])]),r("div",{directives:[{name:"show",rawName:"v-show",value:!e.expand,expression:"!expand"}]},e._l(e.groups,(function(t){return r("div",{key:t.id,staticClass:"terminal-groups-item",class:{active:e.cursorId===t.id}},[r("span",{on:{click:function(r){return e.changeTerminalBlock(t.id,t.name)}}},[e._v(e._s(t.name)+"（"+e._s(t.terminal_count)+"）")]),e.isEdit?r("div",{staticClass:"terminal-groups-actions"},[r("i",{staticClass:"el-icon-edit",on:{click:function(r){return r.stopPropagation(),e.handleEdit(t)}}}),r("i",{staticClass:"el-icon-delete",on:{click:function(r){return r.stopPropagation(),e.handleDelete(t)}}})]):e._e()])})),0)]),r("div",{staticClass:"terminal-groups-buttons"},[e.isEdit?r("el-button",{staticClass:"mb-3",staticStyle:{width:"100%"},on:{click:e.handleAdd}},[e._v(" 新建分组 ")]):e._e(),e.isEdit?r("div",{staticClass:"terminal-groups-import-export"},[r("el-button",{staticClass:"import-button",on:{click:e.importCsv}},[e._v("导入")]),r("el-button",{staticClass:"export-button",on:{click:e.exportCsv}},[e._v("导出")])],1):e._e()],1)]),r("div",{staticClass:"terminal-groups-content"},[e._t("default")],2),r("el-dialog",{attrs:{title:e.dialogTitle,visible:e.dialogVisible,width:"30%","close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"分组名称",prop:"name"}},[r("el-input",{attrs:{placeholder:"请输入分组名称",maxlength:"32","show-word-limit":""},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),r("el-form-item",{attrs:{label:"描述",prop:"desc"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入描述",maxlength:"255","show-word-limit":""},model:{value:e.form.desc,callback:function(t){e.$set(e.form,"desc",t)},expression:"form.desc"}})],1)],1),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),r("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确 定")])],1)],1),r("el-dialog",{attrs:{title:"文件导入分组",visible:e.importVisible,width:"30%","close-on-click-modal":!1},on:{"update:visible":function(t){e.importVisible=t}}},[r("div",{staticClass:"import-dialog-content"},[r("div",{staticClass:"upload-area"},[r("el-upload",{attrs:{accept:".csv","before-upload":e.beforeUpload,"file-list":e.fileList,limit:1,"auto-upload":!1,action:"#"}},[r("el-button",{attrs:{size:"small"}},[r("i",{staticClass:"el-icon-upload"}),e._v(" 选取文件 ")])],1),r("span",{staticClass:"upload-tip"},[e._v(" 仅支持.CSV格式， "),r("a",{attrs:{href:"javascript:;"},on:{click:e.downloadTemplate}},[e._v("下载模板")])])],1),r("div",{staticClass:"import-tip"},[e._v(" 保留原有分组信息，终端信息或分组信息冲突时，以原有信息为主 ")])]),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.handleImportCancel}},[e._v("取 消")]),r("el-button",{attrs:{type:"primary",loading:e.importLoading},on:{click:e.handleImportSave}},[e._v("确 定")])],1)])],1)},n=[],o=(r("b0c0"),r("d3b7"),r("3ca3"),r("ddb0"),r("2b3d"),r("96cf"),r("c964")),i=r("ee97");function s(e){return c.apply(this,arguments)}function c(){return c=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(i["a"])({url:"/api/ieg/v1/terminal_group/list",method:"post",data:t||{}}));case 1:case"end":return e.stop()}}),e)}))),c.apply(this,arguments)}function u(e){return l.apply(this,arguments)}function l(){return l=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(i["a"])({url:"/api/ieg/v1/terminal_group/update",method:"post",data:t||{}}));case 1:case"end":return e.stop()}}),e)}))),l.apply(this,arguments)}function p(e){return d.apply(this,arguments)}function d(){return d=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(i["a"])({url:"/api/ieg/v1/terminal_group/delete",method:"delete",data:t||{}}));case 1:case"end":return e.stop()}}),e)}))),d.apply(this,arguments)}function m(e){return f.apply(this,arguments)}function f(){return f=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(i["a"])({url:"/api/ieg/v1/terminal_group/create",method:"post",data:t||{}}));case 1:case"end":return e.stop()}}),e)}))),f.apply(this,arguments)}function g(e){return h.apply(this,arguments)}function h(){return h=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(i["a"])({url:"/api/ieg/v1/terminal_group/import_groups",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}}));case 1:case"end":return e.stop()}}),e)}))),h.apply(this,arguments)}var b={name:"TerminalGroups",props:{isEdit:{type:Boolean,default:!1}},data:function(){return{groups:[],total:0,cursorId:-1,cursorName:"全部终端",expand:!1,dialogVisible:!1,dialogTitle:"新增分组",form:{id:void 0,name:"",desc:""},rules:{name:[{required:!0,message:"请输入分组名称",trigger:"blur"},{max:32,message:"长度不能超过32个字符",trigger:"blur"}],desc:[{max:255,message:"长度不能超过255个字符",trigger:"blur"}]},importVisible:!1,importLoading:!1,fileList:[]}},mounted:function(){this.getGroups()},methods:{getGroups:function(){var e=arguments,t=this;return Object(o["a"])(regeneratorRuntime.mark((function r(){var a,n;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return a=e.length>0&&void 0!==e[0]&&e[0],r.prev=1,r.next=4,s({show_terminal_count:!0});case 4:n=r.sent,n&&0===n.code?(t.groups=n.data.list,t.total=n.data.total,t.$emit("groups",t.groups),a&&t.$emit("changeTerminalGroup")):t.$message.error(n&&n.msg||"获取分组列表失败"),r.next=12;break;case 8:r.prev=8,r.t0=r["catch"](1),console.error("获取分组列表失败:",r.t0),t.$message.error("获取分组列表失败");case 12:case"end":return r.stop()}}),r,null,[[1,8]])})))()},changeTerminalBlock:function(e,t){this.cursorId=e,this.cursorName=t,this.$emit("terminalGroupId",e,t)},handleAdd:function(){this.dialogTitle="新增分组",this.form={id:void 0,name:"",desc:""},this.dialogVisible=!0},handleEdit:function(e){this.dialogTitle="编辑分组",this.form={id:e.id,name:e.name,desc:e.desc},this.dialogVisible=!0},handleDelete:function(e){var t=this;this.$confirm("确定要删除该分组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(o["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,p({id:e.id});case 3:a=r.sent,a&&0===a.code?(t.$message.success(a.msg||"删除成功"),t.getGroups(!0),t.cursorId===e.id&&t.changeTerminalBlock(-1,"全部终端")):t.$message.error(a&&a.msg||"删除失败"),r.next=11;break;case 7:r.prev=7,r.t0=r["catch"](0),console.error("删除失败:",r.t0),t.$message.error("删除失败");case 11:case"end":return r.stop()}}),r,null,[[0,7]])})))).catch((function(){}))},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(r){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r){t.next=2;break}return t.abrupt("return");case 2:if(t.prev=2,!e.form.id){t.next=9;break}return t.next=6,u({id:e.form.id,group_name:e.form.name});case 6:a=t.sent,t.next=12;break;case 9:return t.next=11,m({group_name:e.form.name});case 11:a=t.sent;case 12:a&&0===a.code?(e.$message.success(a.msg||(e.form.id?"编辑成功":"新增成功")),e.dialogVisible=!1,e.getGroups(!0)):e.$message.error(a&&a.msg||(e.form.id?"编辑失败":"新增失败")),t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](2),console.error(e.form.id?"编辑失败:":"新增失败:",t.t0),e.$message.error(e.form.id?"编辑失败":"新增失败");case 19:case"end":return t.stop()}}),t,null,[[2,15]])})));return function(e){return t.apply(this,arguments)}}())},importCsv:function(){this.importVisible=!0,this.fileList=[]},handleImportCancel:function(){this.importVisible=!1,this.fileList=[]},beforeUpload:function(e){return this.fileList=[e],!1},handleImportSave:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var r,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==e.fileList.length){t.next=3;break}return e.$message.error("请选择上传文件"),t.abrupt("return");case 3:return e.importLoading=!0,t.prev=4,r=new FormData,r.append("upload_file",e.fileList[0]),t.next=9,g(r);case 9:a=t.sent,a&&0===a.code?(e.$message.success(a.msg||"导入成功"),e.importVisible=!1,e.getGroups(!0)):e.$message.error(a&&a.msg||"导入失败"),t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](4),console.error("导入失败:",t.t0),e.$message.error("导入失败");case 17:return t.prev=17,e.importLoading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,null,[[4,13,17,20]])})))()},downloadTemplate:function(){window.open("/api/ieg/v1/terminal_group/download_group_template")},exportCsv:function(){var e={name:"分组名"};this.exportToCsv(e,this.groups)},exportToCsv:function(e,t){var r="";for(var a in e)r+="".concat(e[a],",");for(var n in r="".concat(r.substring(0,r.length-1),"\r\n"),t){for(var o in e)r+="".concat(t[n][o],",");r="".concat(r.substring(0,r.length-1),"\r\n")}var i=new Blob(["\ufeff".concat(r)]),s=window.URL||window.webkitURL||window,c=s.createObjectURL(i),u=document.createElement("a");u.href=c,u.download="groups.csv",u.click(),s.revokeObjectURL(c)}}},v=b,k=(r("39e2"),r("2877")),x=Object(k["a"])(v,a,n,!1,null,"1af7e48c",null);t["a"]=x.exports},ab13:function(e,t,r){var a=r("b622"),n=a("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,"/./"[e](t)}catch(a){}}return!1}},bae8:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return o}));var a=r("ee97");function n(e){return Object(a["a"])({url:"/api/ieg/v1/login/login_in",method:"post",data:e})}function o(){return Object(a["a"])({url:"/api/ieg/v1/system/info",method:"get"})}},d2c9:function(e,t,r){"use strict";r.d(t,"a",(function(){return s}));r("d3b7"),r("25f0"),r("96cf");var a=r("c964"),n=r("bae8"),o=6e5;function i(){var e=localStorage.getItem("hg_token_timestamp");if(!e)return!1;var t=(new Date).getTime(),r=parseInt(e),a=t-r;return a<o}function s(){return c.apply(this,arguments)}function c(){return c=Object(a["a"])(regeneratorRuntime.mark((function e(){var t,r,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=localStorage.getItem("hg_token"),!t){e.next=7;break}if(r=i(),!r){e.next=6;break}return console.log("本地token有效，无需重新登录"),e.abrupt("return",t);case 6:console.log("本地token已过期，需要重新登录");case 7:return e.prev=7,e.next=10,Object(n["b"])({name:"admin",password:"123456"});case 10:if(a=e.sent,!(a&&0===a.code&&a.data&&a.data.token)){e.next=18;break}return console.log("隐式登录成功，获取到新token"),localStorage.setItem("hg_token",a.data.token),localStorage.setItem("hg_token_timestamp",(new Date).getTime().toString()),e.abrupt("return",a.data.token);case 18:return console.error("隐式登录失败:",a),e.abrupt("return","");case 20:e.next=26;break;case 22:return e.prev=22,e.t0=e["catch"](7),console.error("隐式登录出错:",e.t0),e.abrupt("return","");case 26:case"end":return e.stop()}}),e,null,[[7,22]])}))),c.apply(this,arguments)}},ee97:function(e,t,r){"use strict";r("99af"),r("c975"),r("a9e3"),r("d3b7"),r("ac1f"),r("5319"),r("2ca0");var a=r("bc3a"),n=r.n(a),o=r("4360"),i=r("a18c"),s=r("a47e"),c=r("f7b5"),u=r("f907"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",a=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),l=a.NODE_ENV,p=a.VUE_APP_IS_MOCK,d=a.VUE_APP_BASE_API,m="true"===p?"":d;"production"===l&&(m="");var f={baseURL:m,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===l&&(f.timeout=r),t){case"upload":f.headers["Content-Type"]="multipart/form-data",f["processData"]=!1,f["contentType"]=!1;break;case"download":f["responseType"]="blob";break;case"eventSource":break;default:break}var g=n.a.create(f);return g.interceptors.request.use((function(e){var t=o["a"].getters.token;if(""!==t&&e.url.startsWith("/api/ieg/")){var r=localStorage.getItem("hg_token");r&&(e.headers["authtoken"]=r)}return e}),(function(e){Object(c["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:e,print:!0}),Promise.reject("response-err:"+e)})),g.interceptors.response.use((function(e){var r=void 0===e.headers["code"]?200:Number(e.headers["code"]),a=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))},n=function(){var t=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",a=arguments.length>2?arguments[2]:void 0,n="";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n="error"),e.data.code>=2e3&&e.data.code<3e3&&(n="warning"),Object(c["a"])({i18nCode:"ajax.".concat(r,".").concat(t),type:n}),Promise.reject("response-err-status:".concat(a||u["a"][r][t]," \nerr-question: ").concat(s["a"].t("ajax.".concat(r,".").concat(t))))};switch(e.data.code){case u["a"].exception.system:t("system");break;case u["a"].exception.server:t("server");break;case u["a"].exception.session:a();break;case u["a"].exception.access:a();break;case u["a"].exception.certification:t("certification");break;case u["a"].exception.auth:t("auth"),i["a"].replace({path:"/401"});break;case u["a"].exception.token:t("token");break;case u["a"].exception.param:t("param");break;case u["a"].exception.idempotency:t("idempotency");break;case u["a"].exception.ip:t("ip"),o["a"].dispatch("user/reset"),i["a"].replace({path:"/login"});break;case u["a"].exception.upload:t("upload");break;case u["a"].attack.xss:t("xss","attack");break;default:t("code","exception",-1);break}};switch(t){case"upload":if(0===r)return e.data.data;n();break;case"download":if(0===r)return{data:e.data,fileName:decodeURI(e.headers["file-name"])};n();break;default:if(0===e.data.code)return e.data;n();break}}),(function(e){var r=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(o["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))};return"upload"===t?(Object(c["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),e.response&&403==e.response.status&&r(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(s["a"].t("ajax.service.upload")))):(Object(c["a"])({i18nCode:"ajax.service.timeout",type:"error"}),e.response&&403==e.response.status&&r(),Promise.reject("response-err-status:".concat(e," \nerr-question: ").concat(s["a"].t("ajax.service.timeout"))))})),g(e)};t["a"]=l}}]);