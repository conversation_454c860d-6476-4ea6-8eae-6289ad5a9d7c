(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-78519dfb"],{"0133":function(e,t,r){"use strict";var a=r("e5be"),n=r.n(a);n.a},"23e8":function(e,t,r){},"2ca0":function(e,t,r){"use strict";var a=r("23e7"),n=r("06cf").f,s=r("50c4"),i=r("5a34"),o=r("1d80"),c=r("ab13"),u=r("c430"),l="".startsWith,d=Math.min,p=c("startsWith"),f=!u&&!p&&!!function(){var e=n(String.prototype,"startsWith");return e&&!e.writable}();a({target:"String",proto:!0,forced:!f&&!p},{startsWith:function(e){var t=String(o(this));i(e);var r=s(d(arguments.length>1?arguments[1]:void 0,t.length)),a=String(e);return l?l.call(t,a,r):t.slice(r,r+a.length)===a}})},"45fc":function(e,t,r){"use strict";var a=r("23e7"),n=r("b727").some,s=r("a640"),i=r("ae40"),o=s("some"),c=i("some");a({target:"Array",proto:!0,forced:!o||!c},{some:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},"5a0c":function(e,t,r){!function(t,r){e.exports=r()}(0,(function(){"use strict";var e=1e3,t=6e4,r=36e5,a="millisecond",n="second",s="minute",i="hour",o="day",c="week",u="month",l="quarter",d="year",p="date",f="Invalid Date",h=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,g={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||t[0])+"]"}},b=function(e,t,r){var a=String(e);return!a||a.length>=t?e:""+Array(t+1-a.length).join(r)+e},v={s:b,z:function(e){var t=-e.utcOffset(),r=Math.abs(t),a=Math.floor(r/60),n=r%60;return(t<=0?"+":"-")+b(a,2,"0")+":"+b(n,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var a=12*(r.year()-t.year())+(r.month()-t.month()),n=t.clone().add(a,u),s=r-n<0,i=t.clone().add(a+(s?-1:1),u);return+(-(a+(r-n)/(s?n-i:i-n))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:u,y:d,w:c,d:o,D:p,h:i,m:s,s:n,ms:a,Q:l}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},w="en",y={};y[w]=g;var x="$isDayjsObject",D=function(e){return e instanceof S||!(!e||!e[x])},$=function e(t,r,a){var n;if(!t)return w;if("string"==typeof t){var s=t.toLowerCase();y[s]&&(n=s),r&&(y[s]=r,n=s);var i=t.split("-");if(!n&&i.length>1)return e(i[0])}else{var o=t.name;y[o]=t,n=o}return!a&&n&&(w=n),n||!a&&w},k=function(e,t){if(D(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new S(r)},_=v;_.l=$,_.i=D,_.w=function(e,t){return k(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var S=function(){function g(e){this.$L=$(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[x]=!0}var b=g.prototype;return b.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(_.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var a=t.match(h);if(a){var n=a[2]-1||0,s=(a[7]||"0").substring(0,3);return r?new Date(Date.UTC(a[1],n,a[3]||1,a[4]||0,a[5]||0,a[6]||0,s)):new Date(a[1],n,a[3]||1,a[4]||0,a[5]||0,a[6]||0,s)}}return new Date(t)}(e),this.init()},b.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},b.$utils=function(){return _},b.isValid=function(){return!(this.$d.toString()===f)},b.isSame=function(e,t){var r=k(e);return this.startOf(t)<=r&&r<=this.endOf(t)},b.isAfter=function(e,t){return k(e)<this.startOf(t)},b.isBefore=function(e,t){return this.endOf(t)<k(e)},b.$g=function(e,t,r){return _.u(e)?this[t]:this.set(r,e)},b.unix=function(){return Math.floor(this.valueOf()/1e3)},b.valueOf=function(){return this.$d.getTime()},b.startOf=function(e,t){var r=this,a=!!_.u(t)||t,l=_.p(e),f=function(e,t){var n=_.w(r.$u?Date.UTC(r.$y,t,e):new Date(r.$y,t,e),r);return a?n:n.endOf(o)},h=function(e,t){return _.w(r.toDate()[e].apply(r.toDate("s"),(a?[0,0,0,0]:[23,59,59,999]).slice(t)),r)},m=this.$W,g=this.$M,b=this.$D,v="set"+(this.$u?"UTC":"");switch(l){case d:return a?f(1,0):f(31,11);case u:return a?f(1,g):f(0,g+1);case c:var w=this.$locale().weekStart||0,y=(m<w?m+7:m)-w;return f(a?b-y:b+(6-y),g);case o:case p:return h(v+"Hours",0);case i:return h(v+"Minutes",1);case s:return h(v+"Seconds",2);case n:return h(v+"Milliseconds",3);default:return this.clone()}},b.endOf=function(e){return this.startOf(e,!1)},b.$set=function(e,t){var r,c=_.p(e),l="set"+(this.$u?"UTC":""),f=(r={},r[o]=l+"Date",r[p]=l+"Date",r[u]=l+"Month",r[d]=l+"FullYear",r[i]=l+"Hours",r[s]=l+"Minutes",r[n]=l+"Seconds",r[a]=l+"Milliseconds",r)[c],h=c===o?this.$D+(t-this.$W):t;if(c===u||c===d){var m=this.clone().set(p,1);m.$d[f](h),m.init(),this.$d=m.set(p,Math.min(this.$D,m.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},b.set=function(e,t){return this.clone().$set(e,t)},b.get=function(e){return this[_.p(e)]()},b.add=function(a,l){var p,f=this;a=Number(a);var h=_.p(l),m=function(e){var t=k(f);return _.w(t.date(t.date()+Math.round(e*a)),f)};if(h===u)return this.set(u,this.$M+a);if(h===d)return this.set(d,this.$y+a);if(h===o)return m(1);if(h===c)return m(7);var g=(p={},p[s]=t,p[i]=r,p[n]=e,p)[h]||1,b=this.$d.getTime()+a*g;return _.w(b,this)},b.subtract=function(e,t){return this.add(-1*e,t)},b.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||f;var a=e||"YYYY-MM-DDTHH:mm:ssZ",n=_.z(this),s=this.$H,i=this.$m,o=this.$M,c=r.weekdays,u=r.months,l=r.meridiem,d=function(e,r,n,s){return e&&(e[r]||e(t,a))||n[r].slice(0,s)},p=function(e){return _.s(s%12||12,e,"0")},h=l||function(e,t,r){var a=e<12?"AM":"PM";return r?a.toLowerCase():a};return a.replace(m,(function(e,a){return a||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return _.s(t.$y,4,"0");case"M":return o+1;case"MM":return _.s(o+1,2,"0");case"MMM":return d(r.monthsShort,o,u,3);case"MMMM":return d(u,o);case"D":return t.$D;case"DD":return _.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return d(r.weekdaysMin,t.$W,c,2);case"ddd":return d(r.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(s);case"HH":return _.s(s,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return h(s,i,!0);case"A":return h(s,i,!1);case"m":return String(i);case"mm":return _.s(i,2,"0");case"s":return String(t.$s);case"ss":return _.s(t.$s,2,"0");case"SSS":return _.s(t.$ms,3,"0");case"Z":return n}return null}(e)||n.replace(":","")}))},b.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},b.diff=function(a,p,f){var h,m=this,g=_.p(p),b=k(a),v=(b.utcOffset()-this.utcOffset())*t,w=this-b,y=function(){return _.m(m,b)};switch(g){case d:h=y()/12;break;case u:h=y();break;case l:h=y()/3;break;case c:h=(w-v)/6048e5;break;case o:h=(w-v)/864e5;break;case i:h=w/r;break;case s:h=w/t;break;case n:h=w/e;break;default:h=w}return f?h:_.a(h)},b.daysInMonth=function(){return this.endOf(u).$D},b.$locale=function(){return y[this.$L]},b.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),a=$(e,t,!0);return a&&(r.$L=a),r},b.clone=function(){return _.w(this.$d,this)},b.toDate=function(){return new Date(this.valueOf())},b.toJSON=function(){return this.isValid()?this.toISOString():null},b.toISOString=function(){return this.$d.toISOString()},b.toString=function(){return this.$d.toUTCString()},g}(),I=S.prototype;return k.prototype=I,[["$ms",a],["$s",n],["$m",s],["$H",i],["$W",o],["$M",u],["$y",d],["$D",p]].forEach((function(e){I[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),k.extend=function(e,t){return e.$i||(e(t,S,k),e.$i=!0),k},k.locale=$,k.isDayjs=D,k.unix=function(e){return k(1e3*e)},k.en=y[w],k.Ls=y,k.p={},k}))},"5a34":function(e,t,r){var a=r("44e7");e.exports=function(e){if(a(e))throw TypeError("The method doesn't accept regular expressions");return e}},6637:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"router-wrap-table"},[r("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[r("el-tab-pane",{attrs:{label:"设备列表",name:"0"}},[r("header",{staticClass:"table-header"},[r("section",{staticClass:"table-header-main"},[r("section",{staticClass:"table-header-search"},[r("section",{directives:[{name:"show",rawName:"v-show",value:!e.isShow,expression:"!isShow"}],staticClass:"table-header-search-input"},[r("el-input",{attrs:{clearable:"",placeholder:"设备名称","prefix-icon":"soc-icon-search"},on:{change:e.handleQuery},model:{value:e.queryInput.fireName,callback:function(t){e.$set(e.queryInput,"fireName",t)},expression:"queryInput.fireName"}})],1),r("section",{staticClass:"table-header-search-button"},[e.isShow?e._e():r("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),r("el-button",{on:{click:e.toggleShow}},[e._v(" 高级搜索 "),r("i",{staticClass:"el-icon--right",class:e.isShow?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),r("section",{staticClass:"table-header-button"},[r("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新建设备")]),r("el-button",{attrs:{type:"danger"},on:{click:e.handleBatchDelete}},[e._v("批量删除")])],1)]),r("section",{staticClass:"table-header-extend"},[r("el-collapse-transition",[r("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"isShow"}]},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:6}},[r("el-input",{attrs:{clearable:"",placeholder:"设备名称"},on:{change:e.handleQuery},model:{value:e.queryInput.fireName,callback:function(t){e.$set(e.queryInput,"fireName",t)},expression:"queryInput.fireName"}})],1),r("el-col",{attrs:{span:6}},[r("el-input",{attrs:{clearable:"",placeholder:"设备IP"},on:{change:e.handleQuery},model:{value:e.queryInput.ip,callback:function(t){e.$set(e.queryInput,"ip",t)},expression:"queryInput.ip"}})],1),r("el-col",{attrs:{span:6}},[r("el-select",{attrs:{clearable:"",placeholder:"在线状态"},on:{change:e.handleQuery},model:{value:e.queryInput.onlinStatus,callback:function(t){e.$set(e.queryInput,"onlinStatus",t)},expression:"queryInput.onlinStatus"}},[r("el-option",{attrs:{label:"全部",value:""}}),r("el-option",{attrs:{label:"在线",value:"1"}}),r("el-option",{attrs:{label:"离线",value:"0"}})],1)],1)],1),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:24,align:"right"}},[r("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),r("el-button",{on:{click:e.handleReset}},[e._v("重置")]),r("el-button",{attrs:{icon:"soc-icon-scroller-top-all"},on:{click:e.toggleShow}})],1)],1)],1)])],1)]),r("main",{staticClass:"table-body"},[r("section",{staticClass:"table-body-header"},[r("h2",{staticClass:"table-body-title"},[e._v("防火墙设备管理")])]),r("section",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-body-main"},[r("el-table",{attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"序号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s((e.pagination.currentPage-1)*e.pagination.pageSize+t.$index+1)+" ")]}}])}),r("el-table-column",{attrs:{prop:"notes",label:"设备名称","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(r){return e.handleView(t.row)}}},[e._v(" "+e._s(t.row.notes)+" ")])]}}])}),r("el-table-column",{attrs:{prop:"category_text",label:"设备类型"}}),r("el-table-column",{attrs:{label:"在线状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{class:1===t.row.status?"status-online":"status-offline"},[r("i",{class:1===t.row.status?"el-icon-success":"el-icon-error"}),e._v(" "+e._s(1===t.row.status?"在线":"离线")+" ")])]}}])}),r("el-table-column",{attrs:{prop:"syl_cpu",label:"CPU率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.syl_cpu?r("el-progress",{attrs:{percentage:parseInt(t.row.syl_cpu),"stroke-width":8,color:"#52C41A"}}):e._e()]}}])}),r("el-table-column",{attrs:{prop:"syl_nc",label:"内存率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.syl_nc?r("el-progress",{attrs:{percentage:parseInt(t.row.syl_nc),"stroke-width":8,color:"#4C24ED"}}):e._e()]}}])}),r("el-table-column",{attrs:{prop:"syl_disk",label:"磁盘率",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.syl_disk?r("el-progress",{attrs:{percentage:parseInt(t.row.syl_disk),"stroke-width":8,color:"#1373F1"}}):e._e()]}}])}),r("el-table-column",{attrs:{prop:"remark",label:"备注"}}),r("el-table-column",{attrs:{label:"操作",width:"280",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"action-buttons"},[r("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(r){return e.handleEdit(t.row)}}},[e._v("编辑")]),r("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")]),r("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(r){return e.handlePing(t.row)}}},[e._v("Ping")]),r("el-button",{staticClass:"el-button--blue",attrs:{type:"text"},on:{click:function(r){return e.handleUserManage(t.row)}}},[e._v("用户管理")])],1)]}}])})],1)],1)]),r("footer",{staticClass:"table-footer"},[e.pagination.visible?r("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.pagination.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pagination.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handlePageChange}}):e._e()],1)]),e._l(e.panes,(function(e){return r("el-tab-pane",{key:e.name,attrs:{label:e.title,name:e.name,closable:!0}},[r("iframe",{attrs:{src:e.content,width:"100%",height:"600px",frameborder:"0"}})])}))],2),r("add-device-modal",{attrs:{visible:e.addModalVisible,"current-data":e.currentData},on:{"update:visible":function(t){e.addModalVisible=t},"on-submit":e.handleAddSubmit}}),r("user-manage-modal",{attrs:{visible:e.userModalVisible,"current-data":e.currentData},on:{"update:visible":function(t){e.userModalVisible=t},"on-submit":e.handleUserSubmit}})],1)},n=[],s=(r("a623"),r("4de4"),r("7db0"),r("4160"),r("a15b"),r("d81d"),r("45fc"),r("b0c0"),r("d3b7"),r("ac1f"),r("25f0"),r("5319"),r("1276"),r("159b"),r("f3f3")),i=(r("96cf"),r("c964")),o=r("c9d9");function c(e){var t={};if(e.queryParams){var r=e.queryParams;r.fireName&&(t.name=r.fireName),r.group_id&&(t.group_id=r.group_id),r.originIp&&(t.ip=r.originIp),r.onlinStatus&&(t.status=r.onlinStatus)}return t.category=e.type,t.page=e._page,t.per_page=e._limit,Object(o["a"])({url:"/api2/device/list",method:"get",params:t||{}})}function u(e){return Object(o["a"])({url:"/api2/device/add",method:"post",data:e||{}})}function l(e){return Object(o["a"])({url:"/api2/device/edit",method:"post",data:e||{}})}function d(e){return Object(o["a"])({url:"/api2/device/delete",method:"post",data:e||{}})}function p(e){return Object(o["a"])({url:"/api2/device/batchDelete",method:"post",data:e||{}})}function f(e){return Object(o["a"])({url:"/api2/device/ping",method:"post",data:e||{}})}function h(e){return Object(o["a"])({url:"/api2/device/device_topology",method:"get",data:e||{}})}function m(e){return Object(o["a"])({url:"/dev/topo/set",method:"post",data:e||{}})}function g(e){return Object(o["a"])({url:"/api2/device/user/list",method:"post",data:e||{}})}function b(e){return Object(o["a"])({url:"/api2/device/user/add",method:"post",data:e||{}})}function v(e){return Object(o["a"])({url:"/api2/device/user/delete",method:"post",data:e||{}})}var w=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:"添加新设备",visible:e.dialogVisible,width:"650px","before-close":e.handleClose,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[r("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"form",attrs:{model:e.formData,rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"设备名称",prop:"name"}},[r("el-input",{attrs:{placeholder:"请输入设备名称",maxlength:"50","show-word-limit":""},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),r("el-form-item",{attrs:{label:"设备分组",prop:"groupId"}},[r("el-tree-select",{staticStyle:{width:"100%"},attrs:{data:e.groupData,props:e.treeProps,placeholder:"请选择分组","check-strictly":!0},model:{value:e.formData.groupId,callback:function(t){e.$set(e.formData,"groupId",t)},expression:"formData.groupId"}})],1),r("el-form-item",{attrs:{label:"设备IP",prop:"originIp"}},[r("el-input",{attrs:{placeholder:"请输入IP地址"},on:{blur:e.validateIP},model:{value:e.formData.originIp,callback:function(t){e.$set(e.formData,"originIp",t)},expression:"formData.originIp"}}),e.showIPError?r("span",{staticStyle:{color:"red","font-size":"12px"}},[e._v("IP地址不能为空")]):e._e()],1),r("el-form-item",{attrs:{label:"端口",prop:"port"}},[r("el-input",{attrs:{placeholder:"请输入端口",type:"number"},model:{value:e.formData.port,callback:function(t){e.$set(e.formData,"port",t)},expression:"formData.port"}})],1),r("el-form-item",{attrs:{label:"用户名",prop:"username"}},[r("el-input",{attrs:{placeholder:"请输入用户名"},model:{value:e.formData.username,callback:function(t){e.$set(e.formData,"username",t)},expression:"formData.username"}})],1),r("el-form-item",{attrs:{label:"密码",prop:"password"}},[r("el-input",{attrs:{type:"password",placeholder:"请输入密码","show-password":""},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1),r("el-form-item",{attrs:{label:"设备类型",prop:"deviceType"}},[r("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.formData.deviceType,callback:function(t){e.$set(e.formData,"deviceType",t)},expression:"formData.deviceType"}},[r("el-option",{attrs:{label:"防火墙",value:"firewall"}}),r("el-option",{attrs:{label:"路由器",value:"router"}}),r("el-option",{attrs:{label:"交换机",value:"switch"}})],1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",rows:3,maxlength:"100","show-word-limit":""},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.handleClose}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.handleSubmit}},[e._v("确认")])],1)],1)},y=[],x={name:"AddDeviceModal",props:{visible:{type:Boolean,default:!1},currentData:{type:Object,default:function(){return{}}},groupData:{type:Array,default:function(){return[]}}},data:function(){return{loading:!1,submitLoading:!1,showIPError:!1,formData:{id:null,name:"",groupId:"",originIp:"",port:"443",username:"",password:"",deviceType:"firewall",remark:""},rules:{name:[{required:!0,message:"请输入设备名称",trigger:"blur"},{pattern:/^[\u4e00-\u9fa5A-Za-z0-9\-\_]*$/,message:"请输入汉字、字母、数字、短横线或下划线",trigger:"blur"}],groupId:[{required:!0,message:"请选择设备分组",trigger:"change"}],originIp:[{required:!0,message:"请输入IP地址",trigger:"blur"},{pattern:/^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/,message:"IP地址格式不正确",trigger:"blur"}],port:[{required:!0,message:"请输入端口",trigger:"blur"},{pattern:/^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,message:"端口范围: 1-65535",trigger:"blur"}],username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],deviceType:[{required:!0,message:"请选择设备类型",trigger:"change"}]},treeProps:{children:"childList",label:"groupName",value:"id"}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}},isEdit:function(){return this.currentData&&this.currentData.id}},watch:{visible:function(e){e&&this.initForm()}},methods:{initForm:function(){var e=this;this.isEdit?this.formData={id:this.currentData.id,name:this.currentData.fireName||"",groupId:this.currentData.groupId||"",originIp:this.currentData.originIp||"",port:this.currentData.port||"443",username:this.currentData.username||"",password:this.currentData.password||"",deviceType:this.currentData.deviceType||"firewall",remark:this.currentData.remark||""}:this.formData={id:null,name:"",groupId:"",originIp:"",port:"443",username:"",password:"",deviceType:"firewall",remark:""},this.showIPError=!1,this.$nextTick((function(){e.$refs.form&&e.$refs.form.clearValidate()}))},validateIP:function(){this.showIPError="..."===this.formData.originIp},handleSubmit:function(){var e=this;this.$refs.form.validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(r){var a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r||e.showIPError){t.next=22;break}if(e.submitLoading=!0,t.prev=2,n=Object(s["a"])(Object(s["a"])({},e.formData),{},{fireName:e.formData.name}),!e.isEdit){t.next=10;break}return t.next=7,l(n);case 7:a=t.sent,t.next=13;break;case 10:return t.next=12,u(n);case 12:a=t.sent;case 13:0===a.retcode?(e.$message.success("操作成功"),e.$emit("on-submit"),e.handleClose()):e.$message.error(a.msg),t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](2),e.$message.error("操作失败");case 19:return t.prev=19,e.submitLoading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,null,[[2,16,19,22]])})));return function(e){return t.apply(this,arguments)}}())},handleClose:function(){this.dialogVisible=!1}}},D=x,$=(r("9ffe"),r("2877")),k=Object($["a"])(D,w,y,!1,null,"665fa9e0",null),_=k.exports,S=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:"用户管理",visible:e.dialogVisible,width:"800px","before-close":e.handleClose,"close-on-click-modal":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[r("div",{staticClass:"user-manage-content"},[r("div",{staticClass:"toolbar"},[r("el-button",{attrs:{type:"primary"},on:{click:e.handleAddUser}},[e._v("新增用户")]),r("el-button",{attrs:{type:"danger"},on:{click:e.handleBatchDelete}},[e._v("批量删除")])],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList,size:"mini",height:"400"},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"序号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),r("el-table-column",{attrs:{prop:"username",label:"用户名"}}),r("el-table-column",{attrs:{prop:"role",label:"角色"}}),r("el-table-column",{attrs:{prop:"status",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{class:1===t.row.status?"status-active":"status-inactive"},[e._v(" "+e._s(1===t.row.status?"启用":"禁用")+" ")])]}}])}),r("el-table-column",{attrs:{prop:"createTime",label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.createTime))+" ")]}}])}),r("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.handleEditUser(t.row)}}},[e._v("编辑")]),r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.handleDeleteUser(t.row)}}},[e._v("删除")])]}}])})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.handleClose}},[e._v("关闭")])],1),r("el-dialog",{attrs:{title:e.userFormTitle,visible:e.userFormVisible,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.userFormVisible=t}}},[r("el-form",{ref:"userForm",attrs:{model:e.userFormData,rules:e.userFormRules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"用户名",prop:"username"}},[r("el-input",{attrs:{placeholder:"请输入用户名"},model:{value:e.userFormData.username,callback:function(t){e.$set(e.userFormData,"username",t)},expression:"userFormData.username"}})],1),r("el-form-item",{attrs:{label:"密码",prop:"password"}},[r("el-input",{attrs:{type:"password",placeholder:"请输入密码","show-password":""},model:{value:e.userFormData.password,callback:function(t){e.$set(e.userFormData,"password",t)},expression:"userFormData.password"}})],1),r("el-form-item",{attrs:{label:"角色",prop:"role"}},[r("el-select",{attrs:{placeholder:"请选择角色"},model:{value:e.userFormData.role,callback:function(t){e.$set(e.userFormData,"role",t)},expression:"userFormData.role"}},[r("el-option",{attrs:{label:"管理员",value:"admin"}}),r("el-option",{attrs:{label:"操作员",value:"operator"}}),r("el-option",{attrs:{label:"查看者",value:"viewer"}})],1)],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{model:{value:e.userFormData.status,callback:function(t){e.$set(e.userFormData,"status",t)},expression:"userFormData.status"}},[r("el-radio",{attrs:{label:1}},[e._v("启用")]),r("el-radio",{attrs:{label:0}},[e._v("禁用")])],1)],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.userFormVisible=!1}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.userSubmitLoading},on:{click:e.handleUserSubmit}},[e._v("确认")])],1)],1)],1)},I=[],O=r("5a0c"),M=r.n(O),T={name:"UserManageModal",props:{visible:{type:Boolean,default:!1},currentData:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,userList:[],selectedUsers:[],userFormVisible:!1,userFormTitle:"新增用户",userSubmitLoading:!1,userFormData:{id:null,username:"",password:"",role:"operator",status:1},userFormRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],role:[{required:!0,message:"请选择角色",trigger:"change"}]}}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{visible:function(e){e&&this.loadUserList()}},methods:{loadUserList:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.currentData.id){t.next=2;break}return t.abrupt("return");case 2:return e.loading=!0,t.prev=3,t.next=6,g({deviceId:e.currentData.id});case 6:r=t.sent,0===r.retcode?e.userList=r.data||[]:e.$message.error(r.msg),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](3),e.$message.error("获取用户列表失败");case 13:return t.prev=13,e.loading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[3,10,13,16]])})))()},handleAddUser:function(){var e=this;this.userFormTitle="新增用户",this.userFormData={id:null,username:"",password:"",role:"operator",status:1},this.userFormVisible=!0,this.$nextTick((function(){e.$refs.userForm&&e.$refs.userForm.clearValidate()}))},handleEditUser:function(e){var t=this;this.userFormTitle="编辑用户",this.userFormData={id:e.id,username:e.username,password:"",role:e.role,status:e.status},this.userFormVisible=!0,this.$nextTick((function(){t.$refs.userForm&&t.$refs.userForm.clearValidate()}))},handleDeleteUser:function(e){var t=this;this.$confirm("确定要删除该用户吗？","删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,v({deviceId:t.currentData.id,userId:e.id});case 3:a=r.sent,0===a.retcode?(t.$message.success("删除成功"),t.loadUserList()):t.$message.error(a.msg),r.next=10;break;case 7:r.prev=7,r.t0=r["catch"](0),t.$message.error("删除失败");case 10:case"end":return r.stop()}}),r,null,[[0,7]])}))))},handleBatchDelete:function(){var e=this;0!==this.selectedUsers.length?this.$confirm("确定要删除选中的 ".concat(this.selectedUsers.length," 个用户吗？"),"批量删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){var r,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,r=e.selectedUsers.map((function(e){return e.id})),t.next=4,v({deviceId:e.currentData.id,userIds:r.join(",")});case 4:a=t.sent,0===a.retcode?(e.$message.success("删除成功"),e.loadUserList()):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.$message.error("删除失败");case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))):this.$message.warning("请选择要删除的用户")},handleUserSubmit:function(){var e=this;this.$refs.userForm.validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(r){var a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r){t.next=16;break}return e.userSubmitLoading=!0,t.prev=2,a=Object(s["a"])(Object(s["a"])({},e.userFormData),{},{deviceId:e.currentData.id}),t.next=6,b(a);case 6:n=t.sent,0===n.retcode?(e.$message.success("操作成功"),e.userFormVisible=!1,e.loadUserList()):e.$message.error(n.msg),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2),e.$message.error("操作失败");case 13:return t.prev=13,e.userSubmitLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[2,10,13,16]])})));return function(e){return t.apply(this,arguments)}}())},handleSelectionChange:function(e){this.selectedUsers=e},formatTime:function(e){return e?M()(e).format("YYYY-MM-DD HH:mm:ss"):"-"},handleClose:function(){this.dialogVisible=!1,this.userList=[],this.selectedUsers=[]}}},C=T,j=(r("9d3c"),Object($["a"])(C,S,I,!1,null,"463a79aa",null)),F=j.exports;function P(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,r="x"===e?t:3&t|8;return r.toString(16)}))}var R={name:"DeviceList",components:{AddDeviceModal:_,UserManageModal:F},data:function(){return{activeTab:"0",isShow:!1,loading:!1,queryInput:{fireName:"",ip:"",onlinStatus:""},tableData:[],selectedRows:[],pagination:{total:0,pageSize:10,currentPage:1,visible:!0},addModalVisible:!1,userModalVisible:!1,currentData:null,panes:[],timer:null,topoData:{}}},mounted:function(){this.getFirewallDeviceList(),this.getTopoDataFromServer(),this.startTimer()},beforeDestroy:function(){this.clearTimer()},methods:{toggleShow:function(){this.isShow=!this.isShow},startTimer:function(){var e=this;this.timer=setInterval((function(){e.getFirewallDeviceList()}),3e4)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},getFirewallDeviceList:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){var r,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,r={_limit:e.pagination.pageSize,_page:e.pagination.currentPage,queryParams:e.buildQueryParams(),type:1},t.prev=2,t.next=5,c(r);case 5:a=t.sent,0===a.code?(e.tableData=a.data.items||[],e.pagination.total=a.data.total||0,e.selectedRows=[]):e.$message.error(a.message),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),e.$message.error("获取设备列表失败");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[2,9,12,15]])})))()},buildQueryParams:function(){var e={};return this.queryInput.fireName&&(e.fireName=this.queryInput.fireName),this.queryInput.ip&&(e.originIp=this.queryInput.ip),""!==this.queryInput.onlinStatus&&(e.onlinStatus=this.queryInput.onlinStatus),e},handleTabClick:function(e){this.activeTab=e.name,e.name},handleQuery:function(){this.pagination.currentPage=1,this.getFirewallDeviceList()},handleReset:function(){this.queryInput={fireName:"",ip:"",onlinStatus:""},this.handleQuery()},handleAdd:function(){this.currentData=null,this.addModalVisible=!0},handleEdit:function(e){this.currentData=e,this.addModalVisible=!0},handleView:function(e){1==e.status?window.open("https://".concat(e.ip)):this.$message.error("设备不在线，无法查看!")},handleDelete:function(e){var t=this;this.$confirm("确定要删除该设备吗?删除后不可恢复","删除",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,d({device_id:e.id});case 3:a=r.sent,0===a.retcode?(t.$message.success("删除成功"),t.getFirewallDeviceList(),t.delDeviceNode([e.ip])):t.$message.error(a.msg),r.next=10;break;case 7:r.prev=7,r.t0=r["catch"](0),t.$message.error("删除失败");case 10:case"end":return r.stop()}}),r,null,[[0,7]])})))).catch((function(){}))},handleBatchDelete:function(){var e=this;0!==this.selectedRows.length?this.$confirm("确定要删除选中设备吗?删除后不可恢复","删除",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function t(){var r,a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,r=e.selectedRows.map((function(e){return e.id})),t.next=4,p({device_ids:r});case 4:a=t.sent,0===a.retcode?(e.$message.success("删除成功"),e.getFirewallDeviceList(),n=e.selectedRows.map((function(e){return e.ip})),e.delDeviceNode(n)):e.$message.error(a.msg),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.$message.error("删除失败");case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))).catch((function(){})):this.$message.error("至少选中一条数据")},handlePing:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,t.$message.info("正在测试连接..."),r.next=4,f({ip:e.ip});case 4:a=r.sent,0===a.retcode?t.$message.success("设备连接正常"):t.$message.error("设备连接失败"),r.next=11;break;case 8:r.prev=8,r.t0=r["catch"](0),t.$message.error("连接测试失败");case 11:case"end":return r.stop()}}),r,null,[[0,8]])})))()},handleUserManage:function(e){this.currentData=e,this.userModalVisible=!0},handleAddSubmit:function(e){this.addModalVisible=!1,this.getFirewallDeviceList(),e&&e.ip&&this.addDeviceNode(e)},handleUserSubmit:function(){this.userModalVisible=!1},handleSelectionChange:function(e){this.selectedRows=e},handleSizeChange:function(e){this.pagination.pageSize=e,this.getFirewallDeviceList()},handlePageChange:function(e){this.pagination.currentPage=e,this.getFirewallDeviceList()},getTopoDataFromServer:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,h();case 3:a=r.sent,0===a.code?(t.topoData=a.data||{nodes:[],edges:[]},e&&e()):t.$message.error(a.message),r.next=10;break;case 7:r.prev=7,r.t0=r["catch"](0),t.$message.error("获取拓扑数据失败");case 10:case"end":return r.stop()}}),r,null,[[0,7]])})))()},saveTopoDataToServer:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function r(){var a;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,m({topology_text:e});case 3:a=r.sent,0===a.code?t.getFirewallDeviceList():t.$message.error(a.message),r.next=10;break;case 7:r.prev=7,r.t0=r["catch"](0),t.$message.error("保存拓扑数据失败");case 10:case"end":return r.stop()}}),r,null,[[0,7]])})))()},generateNode:function(e){return Object(s["a"])({id:P(),type:"node",size:"50",shape:"koni-custom-node",color:"#69C0FF",labelOffsetY:38},e)},addDeviceNode:function(e){var t=this;this.getTopoDataFromServer((function(){var r=Object(s["a"])({},t.topoData);r.nodes||(r.nodes=[]),r.edges||(r.edges=[]);var a=e.ip,n=a.split(".");n.pop();var i=n.join(".")+".0",o=r.nodes.length>0&&r.nodes.some((function(e){return e.device_ip===i}));if(!o){var c=t.generateNode({device_ip:i,category:3,label:"路由器"});r.nodes.push(c)}var u=r.nodes.length>0&&r.nodes.some((function(t){return t.device_ip===e.ip}));if(!u){var l=t.generateNode({label:"防火墙",category:1,device_ip:e.ip});r.nodes.push(l);var d=r.nodes.find((function(e){return e.device_ip===i}));d&&r.edges.push({id:P(),source:d.id,target:l.id})}t.saveTopoDataToServer(r)}))},delDeviceNode:function(e){var t=this;this.getTopoDataFromServer((function(){var r=Object(s["a"])({},t.topoData);r.nodes||(r.nodes=[]),r.edges||(r.edges=[]),e.forEach((function(e){var t=r.nodes.find((function(t){return t.device_ip===e}));t&&(r.nodes=r.nodes.filter((function(e){return e.id!==t.id})),r.edges=r.edges.filter((function(e){return e.target!==t.id})))}));var a=r.nodes.filter((function(e){return 3===e.category}));a.forEach((function(e){var t=r.edges.every((function(t){return t.source!==e.id}));t&&(r.nodes=r.nodes.filter((function(t){return t.id!==e.id})))})),t.saveTopoDataToServer(r)}))}}},V=R,L=(r("0133"),Object($["a"])(V,a,n,!1,null,"28d122be",null));t["default"]=L.exports},"7db0":function(e,t,r){"use strict";var a=r("23e7"),n=r("b727").find,s=r("44d2"),i=r("ae40"),o="find",c=!0,u=i(o);o in[]&&Array(1)[o]((function(){c=!1})),a({target:"Array",proto:!0,forced:c||!u},{find:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),s(o)},"9d3c":function(e,t,r){"use strict";var a=r("ff15"),n=r.n(a);n.a},"9ffe":function(e,t,r){"use strict";var a=r("23e8"),n=r.n(a);n.a},a623:function(e,t,r){"use strict";var a=r("23e7"),n=r("b727").every,s=r("a640"),i=r("ae40"),o=s("every"),c=i("every");a({target:"Array",proto:!0,forced:!o||!c},{every:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},ab13:function(e,t,r){var a=r("b622"),n=a("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,"/./"[e](t)}catch(a){}}return!1}},c9d9:function(e,t,r){"use strict";r("99af"),r("c975"),r("a9e3"),r("d3b7"),r("ac1f"),r("5319"),r("2ca0");var a=r("bc3a"),n=r.n(a),s=r("4360"),i=r("a18c"),o=r("a47e"),c=r("f7b5"),u=r("f907"),l=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"40000",a=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",VUE_APP_IS_MOCK:"false",VUE_APP_PROXY_TARGET:"",BASE_URL:"/"}),l=a.NODE_ENV,d=a.VUE_APP_IS_MOCK,p=a.VUE_APP_BASE_API,f="true"===d?"":p;"production"===l&&(f="");var h={baseURL:f,withCredentials:!1,headers:{"Content-Type":"application/json;charset=utf-8"}};switch("production"===l&&(h.timeout=r),t){case"upload":h.headers["Content-Type"]="multipart/form-data",h["processData"]=!1,h["contentType"]=!1;break;case"download":h["responseType"]="blob";break;case"eventSource":break;default:break}var m=n.a.create(h);return m.interceptors.request.use((function(e){var t=s["a"].getters.token;return""!==t&&(e.headers["access_token"]=t,e.url.startsWith("/api2/")&&(e.headers["Authorization"]="Basic YWRtaW5pc3RyYXRvcjpBZG1pbjEyMw==")),e}),(function(e){Object(c["a"])({i18nCode:"ajax.interceptors.error",type:"error",error:e,print:!0}),Promise.reject("response-err:"+e)})),m.interceptors.response.use((function(e){var r=void 0===e.headers["code"]?200:Number(e.headers["code"]),a=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(s["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))},n=function(){var t=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"exception",a=arguments.length>2?arguments[2]:void 0,n="";return(500===e.data.code||e.data.code>=1e3&&e.data.code<2e3)&&(n="error"),e.data.code>=2e3&&e.data.code<3e3&&(n="warning"),Object(c["a"])({i18nCode:"ajax.".concat(r,".").concat(t),type:n}),Promise.reject("response-err-status:".concat(a||u["a"][r][t]," \nerr-question: ").concat(o["a"].t("ajax.".concat(r,".").concat(t))))};switch(e.data.code){case u["a"].exception.system:t("system");break;case u["a"].exception.server:t("server");break;case u["a"].exception.session:a();break;case u["a"].exception.access:a();break;case u["a"].exception.certification:t("certification");break;case u["a"].exception.auth:t("auth"),i["a"].replace({path:"/401"});break;case u["a"].exception.token:t("token");break;case u["a"].exception.param:t("param");break;case u["a"].exception.idempotency:t("idempotency");break;case u["a"].exception.ip:t("ip"),s["a"].dispatch("user/reset"),i["a"].replace({path:"/login"});break;case u["a"].exception.upload:t("upload");break;case u["a"].attack.xss:t("xss","attack");break;default:t("code","exception",-1);break}};switch(t){case"upload":if(0===r)return e.data.data;n();break;case"download":if(0===r)return{data:e.data,fileName:decodeURI(e.headers["file-name"])};n();break;default:if(0===e.data.code||0===e.data.retcode)return e.data;n();break}}),(function(e){var r=function(){Object(c["a"])({i18nCode:"logout.message",type:"error"},(function(){i["a"].currentRoute.path.indexOf("/login")>-1?location.reload():(s["a"].dispatch("user/reset"),i["a"].replace({path:"/login"}))}))};return"upload"===t?(Object(c["a"])({i18nCode:"ajax.service.upload",type:"error",duration:2e3}),403==e.response.status&&r(),Promise.reject("response-err-status:Upload Error \nerr-question: ".concat(o["a"].t("ajax.service.upload")))):(Object(c["a"])({i18nCode:"ajax.service.timeout",type:"error"}),403==e.response.status&&r(),Promise.reject("response-err-status:".concat(e," \nerr-question: ").concat(o["a"].t("ajax.service.timeout"))))})),m(e)};t["a"]=l},d81d:function(e,t,r){"use strict";var a=r("23e7"),n=r("b727").map,s=r("1dde"),i=r("ae40"),o=s("map"),c=i("map");a({target:"Array",proto:!0,forced:!o||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},e5be:function(e,t,r){},ff15:function(e,t,r){}}]);