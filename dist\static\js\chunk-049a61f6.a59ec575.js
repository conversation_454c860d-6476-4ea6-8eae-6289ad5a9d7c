(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-049a61f6"],{"21f4":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return r}));n("d3b7"),n("ac1f"),n("25f0"),n("5319");function a(t){return"undefined"===typeof t||null===t||""===t}function r(t,e){var n=t.per_page||t.size,a=t.total-n*(t.page-1),r=Math.floor((e-a)/n)+1;r<0&&(r=0);var s=t.page-r;return s<1&&(s=1),s}},3541:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"network-wrapper"},[n("section",{staticClass:"netcard"},[n("div",{staticClass:"netcard-tip"},[n("el-alert",{attrs:{title:"点击网口图标可进行网口测试，测试时间1分钟，",type:"warning"}})],1),n("div",{staticClass:"netcard-wrapper"},t._l(t.panels,(function(e,a){return n("div",{key:a,staticClass:"netcard-wrapper-row"},[n("section",{class:{"is-flash":e.id===t.enps.enpsId&&1===t.enps.status},on:{click:function(n){return t.testNetwork(e,a)}}},[n("i",{class:["netcard-wrapper-row-cursor","soc-icon-network-port",1===e.state?"color":""]})]),n("div",[t._v(t._s(e.name))])])})),0)]),n("section",{staticClass:"main"},[n("section",{staticClass:"main-wlan"},t._l(t.jsonData.networkConfig,(function(e,a){return n("div",{key:a,attrs:{id:"id"+a}},[n("h2",{staticClass:"main-wlan-title"},[t._v(" "+t._s(t.$t("management.network.title.network",[e.name]))+" ")]),n("div",{staticClass:"main-wlan-form"},[n("el-form",{key:a,attrs:{model:e,rules:t.rules,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{prop:"ipAddress",label:t.$t("management.network.form.ipAddress")}},[[n("el-input",{staticClass:"width-small",attrs:{clearable:""},model:{value:e.ipAddress,callback:function(n){t.$set(e,"ipAddress","string"===typeof n?n.trim():n)},expression:"item.ipAddress"}})]],2)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{label:t.$t("management.network.form.maskCodeBit")}},[[n("el-select",{staticClass:"width-small",attrs:{clearable:"",placeholder:t.$t("management.network.form.bits")},on:{change:function(e){return t.bitsChange(e,a)}},model:{value:e.bits,callback:function(n){t.$set(e,"bits",n)},expression:"item.bits"}},t._l(t.bitsOption,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.label}})})),1)]],2)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticClass:"width-small",attrs:{prop:"maskCode",label:t.$t("management.network.form.maskCode")}},[t._v(" "+t._s(e.maskCode)+" ")])],1)],1),n("el-row",[n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{prop:"dns1",label:t.$t("management.network.form.firstDns")}},[n("el-input",{staticClass:"width-small",attrs:{clearable:""},model:{value:e.dns1,callback:function(n){t.$set(e,"dns1","string"===typeof n?n.trim():n)},expression:"item.dns1"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{prop:"dns2",label:t.$t("management.network.form.secondDns")}},[n("el-input",{staticClass:"width-small",attrs:{clearable:""},model:{value:e.dns2,callback:function(n){t.$set(e,"dns2","string"===typeof n?n.trim():n)},expression:"item.dns2"}})],1)],1),n("el-col",{attrs:{span:8}},[n("el-form-item",{attrs:{prop:"gateWay",label:t.$t("management.network.form.gateWay")}},[n("el-input",{staticClass:"width-small",attrs:{clearable:""},model:{value:e.gateWay,callback:function(n){t.$set(e,"gateWay","string"===typeof n?n.trim():n)},expression:"item.gateWay"}})],1)],1)],1)],1)],1)])})),0),n("section",{staticClass:"form-button"},[n("el-button",{on:{click:t.submitForm}},[t._v(" "+t._s(t.$t("button.save"))+" ")])],1)])])},r=[],s=(n("d81d"),n("54f8")),o=n("f3f3"),i=n("2f62"),l=n("c54a"),u=n("f7b5"),c=n("4020");function d(t){return Object(c["a"])({url:"/config/network/configuration",method:"get",params:t||{}},"default","180000")}function f(t){return Object(c["a"])({url:"/config/network/configuration",method:"put",data:t||{}})}function m(){return Object(c["a"])({url:"/config/network/panel",method:"get"})}function p(t){return Object(c["a"])({url:"/config/network/checkCard/".concat(t),method:"get"})}var b=n("21f4"),v={name:"ManagementNetwork",data:function(){var t=this,e=function(e,n,a){""===n||Object(l["f"])(n)?a():a(new Error(t.$t("validate.ip.incorrect")))};return{navgatorIndex:0,values:"",enps:{status:0,enpsId:""},useFlag:0,bitsEnum:{1:"*********",2:"*********",3:"*********",4:"240.0.0.0",5:"*********",6:"*********",7:"*********",8:"*********",9:"***********",10:"***********",11:"***********",12:"***********",13:"***********",14:"***********",15:"***********",16:"***********",17:"*************",18:"*************",19:"*************",20:"*************",21:"*************",22:"*************",23:"*************",24:"*************",25:"***************",26:"***************",27:"***************",28:"***************",29:"***************",30:"***************",31:"***************",32:"***************"},bitsOption:[{label:"1",value:"*********"},{label:"2",value:"*********"},{label:"3",value:"*********"},{label:"4",value:"240.0.0.0"},{label:"5",value:"*********"},{label:"6",value:"*********"},{label:"7",value:"*********"},{label:"8",value:"*********"},{label:"9",value:"***********"},{label:"10",value:"***********"},{label:"11",value:"***********"},{label:"12",value:"***********"},{label:"13",value:"***********"},{label:"14",value:"***********"},{label:"15",value:"***********"},{label:"16",value:"***********"},{label:"17",value:"*************"},{label:"18",value:"*************"},{label:"19",value:"*************"},{label:"20",value:"*************"},{label:"21",value:"*************"},{label:"22",value:"*************"},{label:"23",value:"*************"},{label:"24",value:"*************"},{label:"25",value:"***************"},{label:"26",value:"***************"},{label:"27",value:"***************"},{label:"28",value:"***************"},{label:"29",value:"***************"},{label:"30",value:"***************"},{label:"31",value:"***************"},{label:"32",value:"***************"}],rules:{ipAddress:[{validator:e,trigger:"blur"}],gateWay:[{validator:e,trigger:"blur"}],dns1:[{validator:e,trigger:"blur"}],dns2:[{validator:e,trigger:"blur"}]},panels:[],jsonData:{},allow:""}},computed:Object(o["a"])(Object(o["a"])({},Object(i["c"])({status:function(t){return t.websocket.status},managementNetwork:function(t){return t.websocket.managementNetwork}})),{},{buildId:function(){return function(t){return"#".concat(t)}},changeButton:function(){var t=!1;return t=0!==this.enps.status,t}}),watch:{status:{handler:function(t){t&&this.sendWebsocket(1)},immediate:!0},managementNetwork:function(t){var e=t.message,n=e.enpsId,a=e.status;this.enps={enpsId:n,status:a}}},mounted:function(){this.queryNetWorkConfig(),this.queryPanel(),this.queryload()},beforeDestroy:function(){this.sendWebsocket(2)},methods:{queryload:function(){var t=[],e=[],n=this.managementNetwork.message;if(""!==n||null!==n||void 0!==n){for(var a in n)t.push(a),e.push(n[a]);this.enps.enpsId=t[0],this.enps.status=e[0]}},sendWebsocket:function(t){this.status&&this.$store.dispatch("websocket/send",{topic:"check_network_card_push",action:t,message:null})},submitForm:function(){var t,e=this,n=function(t){var e,n=0,a=Object(s["a"])(t);try{for(a.s();!(e=a.n()).done;){var r=e.value,o=r.ipAddress,i=r.maskCode,c=r.dns1,d=r.dns2,f=r.gateWay,m=[o,i,c,d,f];m.map((function(t){Object(b["b"])(t)||null===t||""===t||Object(l["f"])(t)||Object(u["a"])({i18nCode:"validate.form.warning",type:"warning",print:!0},(function(){return n=1,!1}))}))}}catch(p){a.e(p)}finally{a.f()}return n};0!==this.jsonData.networkConfig.length&&(t=n(this.jsonData.networkConfig)),this.validateIpRepeat(this.jsonData.networkConfig)&&(t=1),0===t&&f(this.jsonData).then((function(t){1===t?Object(u["a"])({i18nCode:"tip.update.success",type:"success"},(function(){e.queryNetWorkConfig()})):2===t?Object(u["a"])({i18nCode:"management.network.tip.configNone",type:"error"}):3===t?Object(u["a"])({i18nCode:"management.network.tip.manageNone",type:"error"}):4===t?Object(u["a"])({i18nCode:"management.network.tip.false",type:"error"}):Object(u["a"])({i18nCode:"tip.update.error",type:"error"})}))},validateIpRepeat:function(t){var e=[];t.map((function(t){t.ipAddress.isNotEmpty()&&e.push(t.ipAddress)}));for(var n=e.sort(),a=0;a<e.length;a++)if(n[a]===n[a+1])return Object(u["a"])({i18nCode:"management.network.tip.ipRepeat",type:"error"}),!0;return!1},queryNetWorkConfig:function(){var t=this;d().then((function(e){t.jsonData=e}))},bitsChange:function(t,e){this.jsonData.networkConfig[e].maskCode=this.bitsEnum[t]},queryPanel:function(){var t=this;m().then((function(e){t.panels=e}))},queryAnchor:function(t,e){this.navgatorIndex=e,document.querySelector("#id".concat(e)).scrollIntoView({behavior:"smooth"})},testNetwork:function(t,e){var n=this,a=this.managementNetwork.message;if(""===a||null===a||void 0===a)this.queryAnchor(t,e),p(t.id).then((function(e){n.enps.enpsId=String(t.id),n.enps.status=1}));else for(var r in a)this.queryAnchor(t,e),p(t.id).then((function(e){n.enps.enpsId=String(t.id),n.enps.status=1}))}}},h=v,g=(n("3ed3"),n("2877")),w=Object(g["a"])(h,a,r,!1,null,"705410f4",null);e["default"]=w.exports},"3ed3":function(t,e,n){"use strict";var a=n("55ff"),r=n.n(a);r.a},"54f8":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));n("a4d3"),n("e01a"),n("d28b"),n("d3b7"),n("3ca3"),n("ddb0");var a=n("dde1");function r(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=Object(a["a"])(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,s=function(){};return{s:s,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,l=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return i=t.done,t},e:function(t){l=!0,o=t},f:function(){try{i||null==n["return"]||n["return"]()}finally{if(l)throw o}}}}},"55ff":function(t,e,n){},c54a:function(t,e,n){"use strict";n.d(e,"l",(function(){return a})),n.d(e,"m",(function(){return r})),n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"j",(function(){return l})),n.d(e,"q",(function(){return u})),n.d(e,"d",(function(){return c})),n.d(e,"f",(function(){return d})),n.d(e,"g",(function(){return f})),n.d(e,"e",(function(){return m})),n.d(e,"n",(function(){return p})),n.d(e,"k",(function(){return b})),n.d(e,"p",(function(){return v})),n.d(e,"h",(function(){return h})),n.d(e,"i",(function(){return g})),n.d(e,"o",(function(){return w}));n("ac1f"),n("466d"),n("1276");function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="";switch(e){case 0:n=/^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/;break;case 1:n=/^(?![_.\-])(?!.*?[_.\-]$)[a-zA-Z0-9_.\-\u4e00-\u9fa5]+$/;break;case 2:n=/^(?![_./\-])(?!.*?[_./\-]$)[a-zA-Z0-9_./\-\u4e00-\u9fa5]+$/;break;case 3:n=/^(?![_./\-\s])(?!.*?[_./\-\s]$)[a-zA-Z0-9_./\-\s\u4e00-\u9fa5]+$/;break;default:n=/^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/;break}return n.test(t)}function r(t){var e=/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\[\].<>/?\-%]).{0,}$/;return e.test(t)}function s(t){var e=/^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;return e.test(t)}function o(t){var e=/^([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;return e.test(t)}function i(t){var e=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/;return e.test(t)}function l(t){for(var e=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,n=t.split(","),a=0;a<n.length;a++)if(!e.test(n[a]))return!1;return!0}function u(t){var e=/^([0-9]{3,4}-)?[0-9]{7,8}$/;return e.test(t)}function c(t){var e=/^(\d{2,5}-)?\d{6,9}(-\d{2,4})?$/;return e.test(t)}function d(t){var e=/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;return e.test(t)}function f(t){var e=/:/.test(t)&&t.match(/:/g).length<8&&/::/.test(t)?1===t.match(/::/g).length&&/^::$|^(::)?([\da-f]{1,4}(:|::))*[\da-f]{1,4}(:|::)?$/i.test(t):/^([\da-f]{1,4}:){7}[\da-f]{1,4}$/i.test(t);return e}function m(t){return d(t)||f(t)}function p(t){var e=/^([0-9]|[1-9][0-9]{0,4})$/;return e.test(t)}function b(t){for(var e=/^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])){1}$/,n=t.split(","),a=0;a<n.length;a++)if(!e.test(n[a]))return!1;return!0}function v(t){var e=/^[^ ]+$/;return e.test(t)}function h(t){var e=/^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\.[A-Fa-f0-9]{4}){2}$/;return e.test(t)}function g(t){var e=/^([0-9a-f]{2}:){5}[0-9a-f]{2}$/;return e.test(t)}function w(t){var e=/[^\u4E00-\u9FA5]/;return e.test(t)}},d81d:function(t,e,n){"use strict";var a=n("23e7"),r=n("b727").map,s=n("1dde"),o=n("ae40"),i=s("map"),l=o("map");a({target:"Array",proto:!0,forced:!i||!l},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})}}]);