(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-15868997"],{"5c59":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"router-wrap-table"},[a("table-header",{attrs:{condition:e.query,options:e.options},on:{"update:condition":function(t){e.query=t},"on-change":e.changeQueryTable,"on-batch-handle":e.clickBatchHandle}}),a("table-body",{attrs:{"title-name":e.title,"table-loading":e.table.loading,"table-data":e.table.data,options:e.options},on:{"on-select":e.clickSelectRows}}),a("table-footer",{attrs:{pagination:e.pagination},on:{"update:pagination":function(t){e.pagination=t},"size-change":e.tableSizeChange,"page-change":e.tablePageChange}})],1)},n=[],l=(a("d81d"),a("d3b7"),a("ac1f"),a("25f0"),a("1276"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("header",{staticClass:"table-header"},[a("section",{staticClass:"table-header-main"},[a("section",{staticClass:"table-header-search"},[a("section",{directives:[{name:"show",rawName:"v-show",value:!e.filterCondition.senior,expression:"!filterCondition.senior"}],staticClass:"table-header-search-input"},[a("el-input",{attrs:{"prefix-icon":"soc-icon-search",clearable:"",placeholder:e.$t("tip.placeholder.query",[e.$t("alarm.system.table.alarmName")])},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.fuzzyField,callback:function(t){e.$set(e.filterCondition.form,"fuzzyField",t)},expression:"filterCondition.form.fuzzyField"}})],1),a("section",{staticClass:"table-header-search-button"},[e.filterCondition.senior?e._e():a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.changeQueryCondition}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.clickExactQuery}},[e._v(" "+e._s(e.$t("button.search.exact"))+" "),a("i",{staticClass:"el-icon--right",class:e.filterCondition.senior?"el-icon-arrow-up":"el-icon-arrow-down"})])],1)]),a("section",{staticClass:"table-header-button"},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],on:{click:e.clickBatchHandle}},[e._v(" "+e._s(e.$t("button.batch.ignore"))+" ")])],1)]),a("section",{staticClass:"table-header-extend"},[a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.filterCondition.senior,expression:"filterCondition.senior"}],staticClass:"table-header-query"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{clearable:"",placeholder:e.$t("alarm.system.table.alarmName")},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.alarmName,callback:function(t){e.$set(e.filterCondition.form,"alarmName",t)},expression:"filterCondition.form.alarmName"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{attrs:{clearable:"",placeholder:e.$t("alarm.system.table.isIgnore")},on:{change:e.changeQueryCondition},model:{value:e.filterCondition.form.isIgnore,callback:function(t){e.$set(e.filterCondition.form,"isIgnore",t)},expression:"filterCondition.form.isIgnore"}},e._l(e.options.isIgnore,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{align:"right",offset:10,span:4}},[a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.changeQueryCondition}},[e._v(" "+e._s(e.$t("button.query"))+" ")]),a("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:e.resetQuery}},[e._v(" "+e._s(e.$t("button.reset.default"))+" ")]),a("el-button",{ref:"shrinkButton",on:{click:e.clickUpButton}},[a("i",{staticClass:"soc-icon-scroller-top-all"})])],1)],1)],1)])],1)])}),o=[],r=a("13c3"),c={props:{condition:{required:!0,type:Object},options:{required:!0,type:Object}},data:function(){return{filterCondition:this.condition,debounce:null}},watch:{condition:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:condition",e)}},mounted:function(){this.initDebounceQuery()},methods:{initDebounceQuery:function(){var e=this;this.debounce=Object(r["a"])((function(){e.$emit("on-change")}),400)},changeQueryCondition:function(){this.debounce()},clickExactQuery:function(){this.filterCondition.senior=!this.filterCondition.senior,this.filterCondition.form.fuzzyField="",this.changeQueryCondition()},clickUpButton:function(){this.filterCondition.senior=!1,this.resetQuery(),this.changeQueryCondition()},resetQuery:function(){this.filterCondition.form={alarmName:"",isIgnore:""},this.changeQueryCondition()},clickBatchHandle:function(){this.$emit("on-batch-handle")}}},s=c,u=a("2877"),d=Object(u["a"])(s,l,o,!1,null,null,null),h=d.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("main",{staticClass:"table-body"},[a("header",{staticClass:"table-body-header"},[a("h2",{staticClass:"table-body-title"},[e._v(" "+e._s(e.titleName)+" ")])]),a("main",{staticClass:"table-body-main"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],attrs:{data:e.tableData,"element-loading-background":"rgba(0, 0, 0, 0.3)",size:"mini","highlight-current-row":"","tooltip-effect":"light",height:"100%"},on:{"selection-change":e.clickSelectRows}},[a("el-table-column",{attrs:{type:"selection",prop:"id",selectable:e.selectable,width:"50",align:"center"}}),e._l(e.columns,(function(t,i){return a("el-table-column",{key:i,attrs:{prop:t,label:e.$t("alarm.system.table."+t),"show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(i){return[a("p","isIgnore"===t?[e._v(" "+e._s(e.columnText(i.row[t],t))+" ")]:[e._v(" "+e._s(i.row[t])+" ")])]}}],null,!0)})}))],2)],1)])},b=[],f=(a("4160"),a("159b"),{props:{titleName:{required:!0,type:String},tableLoading:{required:!0,type:Boolean},tableData:{requied:!0,type:Array},options:{required:!0,type:Object}},data:function(){return{columns:["alarmName","alarmRole","isIgnore","enterDate","alarmDesc"]}},computed:{columnText:function(){var e=this;return function(t,a){var i="";return e.options[a].forEach((function(e){t===e.value&&(i=e.label)})),i}}},methods:{clickSelectRows:function(e){this.$emit("on-select",e)},selectable:function(e){return 1!==e.isIgnore}}}),g=f,m=Object(u["a"])(g,p,b,!1,null,null,null),v=m.exports,y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",{staticClass:"table-footer"},[e.filterCondition.visible?a("el-pagination",{attrs:{small:"",background:"",align:"right","current-page":e.filterCondition.pageNum,"page-sizes":[10,20,50,100],"page-size":e.filterCondition.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.filterCondition.total},on:{"size-change":e.clickSize,"current-change":e.clickPage}}):e._e()],1)},C=[],k={props:{pagination:{required:!0,type:Object}},data:function(){return{filterCondition:this.pagination}},watch:{pagination:function(e){this.filterCondition=e},filterCondition:function(e){this.$emit("update:pagination",e)}},methods:{clickSize:function(e){this.$emit("size-change",e)},clickPage:function(e){this.$emit("page-change",e)}}},S=k,w=Object(u["a"])(S,y,C,!1,null,null,null),z=w.exports,N=a("f7b5"),x=a("ba70"),q=a("4020");function $(e){return Object(q["a"])({url:"/systemalarm/findAllAlarm",method:"get",params:e||{}})}function _(e){return Object(q["a"])({url:"/systemalarm/ignorealarm/".concat(e),method:"put"})}var T={name:"SystemAlarm",inject:["sysAlarm"],components:{TableHeader:h,TableBody:v,TableFooter:z},data:function(){return{title:this.$t("alarm.system.header"),table:{loading:!1,data:[],selected:[]},query:{senior:!1,form:{fuzzyField:""}},pagination:{pageSize:this.$store.getters.pageSize,pageNum:1,total:0,visible:!0},options:{isIgnore:x["g"]}}},mounted:function(){this.queryTableData()},methods:{changeQueryTable:function(e){"turn-page"!==e&&(this.pagination.pageNum=1);var t=this.handleQueryParams();this.queryTableData(t)},handleQueryParams:function(){var e={pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};return e=this.query.senior?Object.assign(e,{alarmName:this.query.form.alarmName,isIgnore:this.query.form.isIgnore}):Object.assign(e,{fuzzyField:this.query.form.fuzzyField}),e},clickSelectRows:function(e){this.table.selected=e},clickBatchHandle:function(){var e=this;if(this.table.selected.length>0){var t=this.table.selected.map((function(e){return e.id})).toString();this.$confirm(this.$t("tip.confirm.batchIgnore"),this.$t("tip.confirm.tip"),{closeOnClickModal:!1}).then((function(){e.ignoreSystemAlarm(t)}))}else Object(N["a"])({i18nCode:"tip.ignore.prompt",type:"warning",print:!0})},tableSizeChange:function(e){this.pagination.pageSize=e,this.pagination.pageNum=1,this.changeQueryTable()},tablePageChange:function(e){this.pagination.pageNum=e,this.changeQueryTable("turn-page")},queryTableData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{pageSize:this.pagination.pageSize,pageNum:this.pagination.pageNum};this.table.loading=!0,this.pagination.visible=!1,$(t).then((function(t){t&&(e.table.data=t.rows,e.pagination.total=t.total,e.pagination.pageNum=t.pageNum,e.pagination.pageSize=t.pageSize),e.table.loading=!1,e.pagination.visible=!0}))},ignoreSystemAlarm:function(e){var t=this;_(e).then((function(a){a?Object(N["a"])({i18nCode:"tip.ignore.success",type:"success"},(function(){t.sysAlarm();var a=[t.pagination.pageNum,e.split(",")],i=a[0],n=a[1];n.length===t.table.data.length&&(t.pagination.pageNum=1===i?1:i-1),t.changeQueryTable()})):Object(N["a"])({i18nCode:"tip.ignore.error",type:"error"})}))}}},Q=T,I=Object(u["a"])(Q,i,n,!1,null,null,null);t["default"]=I.exports},ba70:function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return o})),a.d(t,"i",(function(){return r})),a.d(t,"d",(function(){return c})),a.d(t,"f",(function(){return s})),a.d(t,"h",(function(){return u})),a.d(t,"j",(function(){return d})),a.d(t,"c",(function(){return h})),a.d(t,"b",(function(){return p}));var i=a("a47e"),n=[{value:0,label:i["a"].t("code.handleStatus.unhandle")},{value:1,label:i["a"].t("code.handleStatus.ignore")}],l=[{value:"illegalAction",label:i["a"].t("code.anomalyType.illegalAction")},{value:"illegalIntruder",label:i["a"].t("code.anomalyType.illegalIntruder")}],o=(i["a"].t("code.status.off"),i["a"].t("code.status.on"),[{value:"0",label:i["a"].t("code.executeStatus.off")},{value:"1",label:i["a"].t("code.executeStatus.on")}]),r=[{value:0,label:i["a"].t("code.runStatus.abnormal")},{value:1,label:i["a"].t("code.runStatus.normal")}],c=[{value:"0",label:i["a"].t("level.serious")},{value:"1",label:i["a"].t("level.high")},{value:"2",label:i["a"].t("level.middle")},{value:"3",label:i["a"].t("level.low")},{value:"4",label:i["a"].t("level.general")}],s=[{value:"total",label:i["a"].t("code.forecastType.total")},{value:"eventType",label:i["a"].t("code.forecastType.eventType")},{value:"srcIp",label:i["a"].t("code.forecastType.srcIp")},{value:"dstIp",label:i["a"].t("code.forecastType.dstIp")},{value:"fromIp",label:i["a"].t("code.forecastType.fromIp")}],u=[{value:"0",label:i["a"].t("code.resultStatus.fail")},{value:"1",label:i["a"].t("code.resultStatus.success")}],d=[{value:"1",label:i["a"].t("code.thresholdType.fault")},{value:"2",label:i["a"].t("code.thresholdType.performance")}],h=[{value:"1",label:i["a"].t("code.displayForm.chart")},{value:"2",label:i["a"].t("code.displayForm.text")}],p={axis:[{label:i["a"].t("code.chart.axis.x"),value:1},{label:i["a"].t("code.chart.axis.y"),value:2}],line:[{label:i["a"].t("code.chart.line.line"),value:1},{label:i["a"].t("code.chart.line.lineStack"),value:2},{label:i["a"].t("code.chart.line.lineStep"),value:3},{label:i["a"].t("code.chart.line.lineStackStep"),value:4}],pie:[{label:i["a"].t("code.chart.pie.pie"),value:1},{label:i["a"].t("code.chart.pie.pieRose"),value:2},{label:i["a"].t("code.chart.pie.pieHalf"),value:3},{label:i["a"].t("code.chart.pie.pie3D"),value:4},{label:i["a"].t("code.chart.pie.ring"),value:5},{label:i["a"].t("code.chart.pie.ringRose"),value:6},{label:i["a"].t("code.chart.pie.ringHalf"),value:7},{label:i["a"].t("code.chart.pie.ring3D"),value:8}],bar:[{label:i["a"].t("code.chart.bar.bar"),value:1},{label:i["a"].t("code.chart.bar.barStack"),value:2},{label:i["a"].t("code.chart.bar.barPolar"),value:3},{label:i["a"].t("code.chart.bar.barPolarStack"),value:4},{label:i["a"].t("code.chart.bar.barRadial"),value:5},{label:i["a"].t("code.chart.bar.barRadialStack"),value:6}],formatType:[{label:i["a"].t("code.chart.formatType.byte"),value:1},{label:i["a"].t("code.chart.formatType.number"),value:2}]}},d81d:function(e,t,a){"use strict";var i=a("23e7"),n=a("b727").map,l=a("1dde"),o=a("ae40"),r=l("map"),c=o("map");i({target:"Array",proto:!0,forced:!r||!c},{map:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})}}]);