(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1741ea3d"],{3451:function(t,e,n){"use strict";var r=n("ea05"),a=n.n(r);a.a},"6a69":function(t,e,n){"use strict";n.d(e,"d",(function(){return a})),n.d(e,"p",(function(){return u})),n.d(e,"S",(function(){return o})),n.d(e,"T",(function(){return c})),n.d(e,"m",(function(){return s})),n.d(e,"q",(function(){return i})),n.d(e,"n",(function(){return m})),n.d(e,"v",(function(){return d})),n.d(e,"w",(function(){return f})),n.d(e,"x",(function(){return l})),n.d(e,"F",(function(){return p})),n.d(e,"N",(function(){return g})),n.d(e,"O",(function(){return h})),n.d(e,"P",(function(){return y})),n.d(e,"Q",(function(){return b})),n.d(e,"k",(function(){return O})),n.d(e,"C",(function(){return j})),n.d(e,"J",(function(){return v})),n.d(e,"U",(function(){return S})),n.d(e,"o",(function(){return w})),n.d(e,"D",(function(){return C})),n.d(e,"K",(function(){return k})),n.d(e,"V",(function(){return x})),n.d(e,"i",(function(){return _})),n.d(e,"I",(function(){return T})),n.d(e,"j",(function(){return N})),n.d(e,"l",(function(){return q})),n.d(e,"X",(function(){return $})),n.d(e,"b",(function(){return J})),n.d(e,"f",(function(){return D})),n.d(e,"A",(function(){return E})),n.d(e,"G",(function(){return F})),n.d(e,"g",(function(){return M})),n.d(e,"s",(function(){return z})),n.d(e,"E",(function(){return A})),n.d(e,"L",(function(){return B})),n.d(e,"a",(function(){return G})),n.d(e,"Y",(function(){return H})),n.d(e,"c",(function(){return I})),n.d(e,"z",(function(){return K})),n.d(e,"r",(function(){return L})),n.d(e,"H",(function(){return P})),n.d(e,"B",(function(){return Q})),n.d(e,"h",(function(){return R})),n.d(e,"Z",(function(){return U})),n.d(e,"t",(function(){return V})),n.d(e,"u",(function(){return W})),n.d(e,"M",(function(){return X})),n.d(e,"W",(function(){return Y})),n.d(e,"e",(function(){return Z})),n.d(e,"y",(function(){return tt})),n.d(e,"R",(function(){return et}));var r=n("4020");function a(){return Object(r["a"])({url:"/systemmanagement/basic",method:"get"})}function u(){return Object(r["a"])({url:"/systemmanagement/querySshdStatus",method:"get"})}function o(){return Object(r["a"])({url:"/systemmanagement/startSshd",method:"put"})}function c(){return Object(r["a"])({url:"/systemmanagement/stopSshd",method:"put"})}function s(){return Object(r["a"])({url:"/systemmanagement/device/restart",method:"get"})}function i(){return Object(r["a"])({url:"/systemmanagement/device/shutdown",method:"get"})}function m(){return Object(r["a"])({url:"/systemmanagement/device/restore",method:"get"})}function d(){return Object(r["a"])({url:"/systemmanagement/properties",method:"get"})}function f(){return Object(r["a"])({url:"/systemmanagement/properties1",method:"get"})}function l(){return Object(r["a"])({url:"/systemmanagement/properties2",method:"get"})}function p(){return Object(r["a"])({url:"/systemmanagement/properties",method:"put"})}function g(t){return Object(r["a"])({url:"/systemmanagement/properties",method:"post",data:t||{}})}function h(t){return Object(r["a"])({url:"/systemmanagement/propertiesNew",method:"post",data:t||{}})}function y(t){return Object(r["a"])({url:"/systemmanagement/propertiesNew1",method:"post",data:t||{}})}function b(t){return Object(r["a"])({url:"/access-control/propertiesNew2",method:"post",data:t||{}})}function O(){return Object(r["a"])({url:"/systemmanagement/mail-server",method:"get"})}function j(){return Object(r["a"])({url:"/systemmanagement/mail-server/reset",method:"put"})}function v(t){return Object(r["a"])({url:"/systemmanagement/mail-server",method:"put",data:t||{}})}function S(t){return Object(r["a"])({url:"/systemmanagement/mail-server/check",method:"put",data:t||{}},"default","20000")}function w(){return Object(r["a"])({url:"/systemmanagement/time-server",method:"get"})}function C(){return Object(r["a"])({url:"/systemmanagement/time-server/reset",method:"put"})}function k(t){return Object(r["a"])({url:"/systemmanagement/time-server",method:"put",data:t||{}})}function x(t){return Object(r["a"])({url:"/systemmanagement/time-server/check",method:"put",data:t||{}},"default","60000")}function _(){return Object(r["a"])({url:"/systemmanagement/data-cleanup/properties",method:"get"})}function T(t){return Object(r["a"])({url:"/systemmanagement/data-cleanup/properties",method:"put",data:t||{}})}function N(){return Object(r["a"])({url:"/systemmanagement/data-cleanup/records",method:"get"})}function q(){return Object(r["a"])({url:"/systemmanagement/license",method:"get"})}function $(t){return Object(r["a"])({url:"/systemmanagement/license/upload",method:"post",data:t||{}},"upload")}function J(){return Object(r["a"])({url:"/systemmanagement/license/download",method:"get"},"download")}function D(){return Object(r["a"])({url:"/systemmanagement/data-backup/properties",method:"get"})}function E(){return Object(r["a"])({url:"/systemmanagement/data-backup/reset",method:"put"})}function F(t){return Object(r["a"])({url:"/systemmanagement/data-backup/properties",method:"put",data:t||{}})}function M(){return Object(r["a"])({url:"/systemmanagement/data-backup/records",method:"get"})}function z(){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/properties",method:"get"})}function A(){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/reset",method:"put"})}function B(t){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/properties",method:"put",data:t||{}})}function G(t){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/create",method:"post"})}function H(t){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/upload",method:"post",data:t||{}},"upload")}function I(t){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/download/".concat(t),method:"get"},"download")}function K(t){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/recovery/".concat(t),method:"put"})}function L(){return Object(r["a"])({url:"/systemmanagement/SystemConfigSnapshot/records",method:"get"})}function P(t){return Object(r["a"])({url:"/systemmanagement/task",method:"put",data:t||{}})}function Q(){return Object(r["a"])({url:"/systemmanagement/task/reset",method:"put"})}function R(){return Object(r["a"])({url:"/systemmanagement/task/echoed",method:"get"})}function U(t){return Object(r["a"])({url:"/systemmanagement/upgrade",method:"post",data:t||{}},"upload","180000")}function V(){return Object(r["a"])({url:"/systemmanagement/combo/forward-relay-way",method:"get"})}function W(){return Object(r["a"])({url:"/systemmanagement/find-system-alarm-notice",method:"get"})}function X(t){return Object(r["a"])({url:"/systemmanagement/system-alarm-notice",method:"put",data:t||{}})}function Y(t){return Object(r["a"])({url:"/systemmanagement/data-backup/upload",method:"post",data:t||{}},"upload","180000")}function Z(){return Object(r["a"])({url:"/systemmanagement/getCenterTime",method:"get"})}function tt(){return Object(r["a"])({url:"/threatintelligence/confing/properties",method:"get"})}function et(t){return Object(r["a"])({url:"/threatintelligence/confing/properties",method:"post",data:t||{}})}},"9e16":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"context-wrapper"},[n("h2",[t._v("日志采集配置")]),n("el-row",{staticStyle:{"margin-top":"16px"}},[n("el-col",{staticStyle:{"text-align":"right","line-height":"32px","padding-right":"16px"},attrs:{span:4}},[t._v(" 日志去重 ")]),n("el-col",{staticStyle:{"line-height":"32px"},attrs:{span:20}},[n("el-switch",{model:{value:t.data.deduplicate,callback:function(e){t.$set(t.data,"deduplicate",e)},expression:"data.deduplicate"}})],1)],1),n("el-row",{staticStyle:{"margin-top":"16px"}},[n("el-col",{staticStyle:{"text-align":"right","line-height":"32px","padding-right":"16px"},attrs:{span:4}},[t._v(" 加密方式 ")]),n("el-col",{staticStyle:{"line-height":"32px"},attrs:{span:20}},[n("el-radio-group",{attrs:{disabled:""},model:{value:t.data.encryptType,callback:function(e){t.$set(t.data,"encryptType",e)},expression:"data.encryptType"}},[n("el-radio",{attrs:{label:"0"}},[t._v(" 否 ")]),n("el-radio",{attrs:{label:"1"}},[t._v(" SM4 ")])],1)],1)],1),n("section",{staticClass:"footer-button"},[n("el-button",{directives:[{name:"has",rawName:"v-has",value:"query",expression:"'query'"}],on:{click:t.clickSaveSystemConfig}},[t._v(" "+t._s(t.$t("button.save"))+" ")])],1)],1)},a=[],u=n("f7b5"),o=n("6a69"),c={props:{formData:{type:Object,default:function(){return{}}}},data:function(){return{data:{deduplicate:!1,encryptType:"1"},options:{snmpForward:[]}}},mounted:function(){this.init()},methods:{init:function(){this.getSystemConfig()},clickSaveSystemConfig:function(){var t=this.data.deduplicate;this.saveSystemConfig({deduplicate:t})},saveSystemConfig:function(t){Object(o["O"])(t).then((function(t){t?Object(u["a"])({i18nCode:"tip.save.success",type:"success"}):Object(u["a"])({i18nCode:"tip.save.error",type:"error"})}))},getSystemConfig:function(){var t=this;Object(o["x"])().then((function(e){t.data.deduplicate=!!e.deduplicate}))}}},s=c,i=(n("3451"),n("2877")),m=Object(i["a"])(s,r,a,!1,null,"558c6fe8",null);e["default"]=m.exports},ea05:function(t,e,n){}}]);